export * from '@unocss/core';
export { default as presetAttributify } from '@unocss/preset-attributify';
export { default as presetIcons } from '@unocss/preset-icons';
export { default as presetMini } from '@unocss/preset-mini';
export { default as presetTagify } from '@unocss/preset-tagify';
export { default as presetTypography } from '@unocss/preset-typography';
export { default as presetUno } from '@unocss/preset-uno';
export { default as presetWebFonts } from '@unocss/preset-web-fonts';
export { default as presetWind } from '@unocss/preset-wind';
export { default as presetWind3 } from '@unocss/preset-wind3';
export { default as presetWind4 } from '@unocss/preset-wind4';
export { default as transformerAttributifyJsx } from '@unocss/transformer-attributify-jsx';
export { default as transformerCompileClass } from '@unocss/transformer-compile-class';
export { default as transformerDirectives } from '@unocss/transformer-directives';
export { default as transformerVariantGroup } from '@unocss/transformer-variant-group';

function defineConfig(config) {
  return config;
}

export { defineConfig };
