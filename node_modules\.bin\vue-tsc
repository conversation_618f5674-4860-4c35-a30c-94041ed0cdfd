#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.9.2/node_modules/vue-tsc/bin/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.9.2/node_modules/vue-tsc/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.9.2/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.9.2/node_modules/vue-tsc/bin/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.9.2/node_modules/vue-tsc/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/vue-tsc@2.2.12_typescript@5.9.2/node_modules:/mnt/d/Project/Other_Project/ClaudeCode_Project/Test_3/smolvlm-realtime-webgpu/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vue-tsc/bin/vue-tsc.js" "$@"
else
  exec node  "$basedir/../vue-tsc/bin/vue-tsc.js" "$@"
fi
