import*as e from"onnxruntime-common";import*as t from"onnxruntime-web";var s,r,o={"onnxruntime-common":t=>{t.exports=e},"onnxruntime-web":e=>{e.exports=t},"?2ce3":()=>{},"?7992":()=>{},"?5af5":()=>{},"?2b25":()=>{},"?db59":()=>{},"?383f":()=>{},"?fa4b":()=>{},"./node_modules/@huggingface/jinja/dist/index.js":(e,t,s)=>{s.r(t),s.d(t,{Environment:()=>ne,Interpreter:()=>ae,Template:()=>he,parse:()=>G,tokenize:()=>c});var r=Object.freeze({Text:"Text",NumericLiteral:"NumericLiteral",StringLiteral:"StringLiteral",Identifier:"Identifier",Equals:"Equals",OpenParen:"OpenParen",CloseParen:"CloseParen",OpenStatement:"OpenStatement",CloseStatement:"CloseStatement",OpenExpression:"OpenExpression",CloseExpression:"CloseExpression",OpenSquareBracket:"OpenSquareBracket",CloseSquareBracket:"CloseSquareBracket",OpenCurlyBracket:"OpenCurlyBracket",CloseCurlyBracket:"CloseCurlyBracket",Comma:"Comma",Dot:"Dot",Colon:"Colon",Pipe:"Pipe",CallOperator:"CallOperator",AdditiveBinaryOperator:"AdditiveBinaryOperator",MultiplicativeBinaryOperator:"MultiplicativeBinaryOperator",ComparisonBinaryOperator:"ComparisonBinaryOperator",UnaryOperator:"UnaryOperator",Comment:"Comment"}),o=class{constructor(e,t){this.value=e,this.type=t}};function n(e){return/\w/.test(e)}function a(e){return/[0-9]/.test(e)}var i=[["{%",r.OpenStatement],["%}",r.CloseStatement],["{{",r.OpenExpression],["}}",r.CloseExpression],["(",r.OpenParen],[")",r.CloseParen],["{",r.OpenCurlyBracket],["}",r.CloseCurlyBracket],["[",r.OpenSquareBracket],["]",r.CloseSquareBracket],[",",r.Comma],[".",r.Dot],[":",r.Colon],["|",r.Pipe],["<=",r.ComparisonBinaryOperator],[">=",r.ComparisonBinaryOperator],["==",r.ComparisonBinaryOperator],["!=",r.ComparisonBinaryOperator],["<",r.ComparisonBinaryOperator],[">",r.ComparisonBinaryOperator],["+",r.AdditiveBinaryOperator],["-",r.AdditiveBinaryOperator],["~",r.AdditiveBinaryOperator],["*",r.MultiplicativeBinaryOperator],["/",r.MultiplicativeBinaryOperator],["%",r.MultiplicativeBinaryOperator],["=",r.Equals]],l=new Map([["n","\n"],["t","\t"],["r","\r"],["b","\b"],["f","\f"],["v","\v"],["'","'"],['"','"'],["\\","\\"]]);function c(e,t={}){const s=[],c=function(e,t={}){return e.endsWith("\n")&&(e=e.slice(0,-1)),t.lstrip_blocks&&(e=e.replace(/^[ \t]*({[#%-])/gm,"$1")),t.trim_blocks&&(e=e.replace(/([#%-]})\n/g,"$1")),e.replace(/-%}\s*/g,"%}").replace(/\s*{%-/g,"{%").replace(/-}}\s*/g,"}}").replace(/\s*{{-/g,"{{").replace(/-#}\s*/g,"#}").replace(/\s*{#-/g,"{#").replace(/{%\s*(end)?generation\s*%}/gs,"")}(e,t);let d=0,u=0;const _=e=>{let t="";for(;e(c[d]);)if("\\"!==c[d]){if(t+=c[d++],d>=c.length)throw new SyntaxError("Unexpected end of input")}else{if(++d,d>=c.length)throw new SyntaxError("Unexpected end of input");const e=c[d++],s=l.get(e);if(void 0===s)throw new SyntaxError(`Unexpected escaped character: ${e}`);t+=s}return t};e:for(;d<c.length;){const e=s.at(-1)?.type;if(void 0===e||e===r.CloseStatement||e===r.CloseExpression||e===r.Comment){let e="";for(;d<c.length&&("{"!==c[d]||"%"!==c[d+1]&&"{"!==c[d+1]&&"#"!==c[d+1]);)e+=c[d++];if(e.length>0){s.push(new o(e,r.Text));continue}}if("{"===c[d]&&"#"===c[d+1]){d+=2;let e="";for(;"#"!==c[d]||"}"!==c[d+1];){if(d+2>=c.length)throw new SyntaxError("Missing end of comment tag");e+=c[d++]}s.push(new o(e,r.Comment)),d+=2;continue}_((e=>/\s/.test(e)));const t=c[d];if("-"===t||"+"===t){const e=s.at(-1)?.type;if(e===r.Text||void 0===e)throw new SyntaxError(`Unexpected character: ${t}`);switch(e){case r.Identifier:case r.NumericLiteral:case r.StringLiteral:case r.CloseParen:case r.CloseSquareBracket:break;default:{++d;const e=_(a);s.push(new o(`${t}${e}`,e.length>0?r.NumericLiteral:r.UnaryOperator));continue}}}for(const[e,t]of i){if("}}"===e&&u>0)continue;if(c.slice(d,d+e.length)===e){s.push(new o(e,t)),t===r.OpenExpression?u=0:t===r.OpenCurlyBracket?++u:t===r.CloseCurlyBracket&&--u,d+=e.length;continue e}}if("'"!==t&&'"'!==t)if(a(t)){let e=_(a);if("."===c[d]&&a(c[d+1])){++d;e=`${e}.${_(a)}`}s.push(new o(e,r.NumericLiteral))}else{if(!n(t))throw new SyntaxError(`Unexpected character: ${t}`);{const e=_(n);s.push(new o(e,r.Identifier))}}else{++d;const e=_((e=>e!==t));s.push(new o(e,r.StringLiteral)),++d}}return s}var d=class{type="Statement"},u=class extends d{constructor(e){super(),this.body=e}type="Program"},_=class extends d{constructor(e,t,s){super(),this.test=e,this.body=t,this.alternate=s}type="If"},m=class extends d{constructor(e,t,s,r){super(),this.loopvar=e,this.iterable=t,this.body=s,this.defaultBlock=r}type="For"},p=class extends d{type="Break"},h=class extends d{type="Continue"},g=class extends d{constructor(e,t,s){super(),this.assignee=e,this.value=t,this.body=s}type="Set"},f=class extends d{constructor(e,t,s){super(),this.name=e,this.args=t,this.body=s}type="Macro"},M=class extends d{constructor(e){super(),this.value=e}type="Comment"},w=class extends d{type="Expression"},T=class extends w{constructor(e,t,s){super(),this.object=e,this.property=t,this.computed=s}type="MemberExpression"},b=class extends w{constructor(e,t){super(),this.callee=e,this.args=t}type="CallExpression"},x=class extends w{constructor(e){super(),this.value=e}type="Identifier"},P=class extends w{constructor(e){super(),this.value=e}type="Literal"},k=class extends P{type="IntegerLiteral"},F=class extends P{type="FloatLiteral"},v=class extends P{type="StringLiteral"},y=class extends P{type="ArrayLiteral"},C=class extends P{type="TupleLiteral"},S=class extends P{type="ObjectLiteral"},A=class extends w{constructor(e,t,s){super(),this.operator=e,this.left=t,this.right=s}type="BinaryExpression"},E=class extends w{constructor(e,t){super(),this.operand=e,this.filter=t}type="FilterExpression"},L=class extends d{constructor(e,t){super(),this.filter=e,this.body=t}type="FilterStatement"},I=class extends w{constructor(e,t){super(),this.lhs=e,this.test=t}type="SelectExpression"},z=class extends w{constructor(e,t,s){super(),this.operand=e,this.negate=t,this.test=s}type="TestExpression"},D=class extends w{constructor(e,t){super(),this.operator=e,this.argument=t}type="UnaryExpression"},j=class extends w{constructor(e=void 0,t=void 0,s=void 0){super(),this.start=e,this.stop=t,this.step=s}type="SliceExpression"},V=class extends w{constructor(e,t){super(),this.key=e,this.value=t}type="KeywordArgumentExpression"},N=class extends w{constructor(e){super(),this.argument=e}type="SpreadExpression"},O=class extends d{constructor(e,t,s){super(),this.call=e,this.callerArgs=t,this.body=s}type="CallStatement"},B=class extends w{constructor(e,t,s){super(),this.condition=e,this.trueExpr=t,this.falseExpr=s}type="Ternary"};function G(e){const t=new u([]);let s=0;function n(t,r){const o=e[s++];if(!o||o.type!==t)throw new Error(`Parser Error: ${r}. ${o.type} !== ${t}.`);return o}function a(e){if(!d(e))throw new SyntaxError(`Expected ${e}`);++s}function i(){switch(e[s].type){case r.Comment:return new M(e[s++].value);case r.Text:return new v(n(r.Text,"Expected text token").value);case r.OpenStatement:return function(){if(n(r.OpenStatement,"Expected opening statement token"),e[s].type!==r.Identifier)throw new SyntaxError(`Unknown statement, got ${e[s].type}`);const t=e[s].value;let o;switch(t){case"set":++s,o=function(){const e=P();let t=null;const o=[];if(l(r.Equals))++s,t=P();else{for(n(r.CloseStatement,"Expected %} token");!c("endset");)o.push(i());n(r.OpenStatement,"Expected {% token"),a("endset")}return n(r.CloseStatement,"Expected closing statement token"),new g(e,t,o)}();break;case"if":++s,o=w(),n(r.OpenStatement,"Expected {% token"),a("endif"),n(r.CloseStatement,"Expected %} token");break;case"macro":++s,o=function(){const e=Z();if("Identifier"!==e.type)throw new SyntaxError("Expected identifier following macro statement");const t=X();n(r.CloseStatement,"Expected closing statement token");const s=[];for(;!c("endmacro");)s.push(i());return new f(e,t,s)}(),n(r.OpenStatement,"Expected {% token"),a("endmacro"),n(r.CloseStatement,"Expected %} token");break;case"for":++s,o=function(){const e=P(!0);if(!(e instanceof x||e instanceof C))throw new SyntaxError(`Expected identifier/tuple for the loop variable, got ${e.type} instead`);if(!d("in"))throw new SyntaxError("Expected `in` keyword following loop variable");++s;const t=G();n(r.CloseStatement,"Expected closing statement token");const o=[];for(;!c("endfor","else");)o.push(i());const a=[];if(c("else"))for(++s,++s,n(r.CloseStatement,"Expected closing statement token");!c("endfor");)a.push(i());return new m(e,t,o,a)}(),n(r.OpenStatement,"Expected {% token"),a("endfor"),n(r.CloseStatement,"Expected %} token");break;case"call":{++s;let e=null;l(r.OpenParen)&&(e=X());const t=Z();if("Identifier"!==t.type)throw new SyntaxError("Expected identifier following call statement");const d=X();n(r.CloseStatement,"Expected closing statement token");const u=[];for(;!c("endcall");)u.push(i());n(r.OpenStatement,"Expected '{%'"),a("endcall"),n(r.CloseStatement,"Expected closing statement token");const _=new b(t,d);o=new O(_,e,u);break}case"break":++s,n(r.CloseStatement,"Expected closing statement token"),o=new p;break;case"continue":++s,n(r.CloseStatement,"Expected closing statement token"),o=new h;break;case"filter":{++s;let e=Z();e instanceof x&&l(r.OpenParen)&&(e=Q(e)),n(r.CloseStatement,"Expected closing statement token");const t=[];for(;!c("endfilter");)t.push(i());n(r.OpenStatement,"Expected '{%'"),a("endfilter"),n(r.CloseStatement,"Expected '%}'"),o=new L(e,t);break}default:throw new SyntaxError(`Unknown statement type: ${t}`)}return o}();case r.OpenExpression:return function(){n(r.OpenExpression,"Expected opening expression token");const e=G();return n(r.CloseExpression,"Expected closing expression token"),e}();default:throw new SyntaxError(`Unexpected token type: ${e[s].type}`)}}function l(...t){return s+t.length<=e.length&&t.every(((t,r)=>t===e[s+r].type))}function c(...t){return e[s]?.type===r.OpenStatement&&e[s+1]?.type===r.Identifier&&t.includes(e[s+1]?.value)}function d(...t){return s+t.length<=e.length&&t.every(((t,r)=>"Identifier"===e[s+r].type&&t===e[s+r].value))}function w(){const e=G();n(r.CloseStatement,"Expected closing statement token");const t=[],o=[];for(;!c("elif","else","endif");)t.push(i());if(c("elif")){++s,++s;const e=w();o.push(e)}else if(c("else"))for(++s,++s,n(r.CloseStatement,"Expected closing statement token");!c("endif");)o.push(i());return new _(e,t,o)}function P(e=!1){const t=e?Z:G,o=[t()],n=l(r.Comma);for(;n&&(++s,o.push(t()),l(r.Comma)););return n?new C(o):o[0]}function G(){return R()}function R(){const e=q();if(d("if")){++s;const t=q();if(d("else")){++s;const r=R();return new B(t,e,r)}return new I(e,t)}return e}function q(){let t=$();for(;d("or");){const r=e[s];++s;const o=$();t=new A(r,t,o)}return t}function $(){let t=W();for(;d("and");){const r=e[s];++s;const o=W();t=new A(r,t,o)}return t}function W(){let t;for(;d("not");){const r=e[s];++s;const o=W();t=new D(r,o)}return t??function(){let t=U();for(;;){let n;if(d("not","in"))n=new o("not in",r.Identifier),s+=2;else if(d("in"))n=e[s++];else{if(!l(r.ComparisonBinaryOperator))break;n=e[s++]}const a=U();t=new A(n,t,a)}return t}()}function U(){let t=Y();for(;l(r.AdditiveBinaryOperator);){const r=e[s];++s;const o=Y();t=new A(r,t,o)}return t}function Q(e){let t=new b(e,X());return t=J(t),l(r.OpenParen)&&(t=Q(t)),t}function X(){n(r.OpenParen,"Expected opening parenthesis for arguments list");const t=function(){const t=[];for(;!l(r.CloseParen);){let o;if(e[s].type===r.MultiplicativeBinaryOperator&&"*"===e[s].value){++s;const e=G();o=new N(e)}else if(o=G(),l(r.Equals)){if(++s,!(o instanceof x))throw new SyntaxError("Expected identifier for keyword argument");const e=G();o=new V(o,e)}t.push(o),l(r.Comma)&&++s}return t}();return n(r.CloseParen,"Expected closing parenthesis for arguments list"),t}function H(){const e=[];let t=!1;for(;!l(r.CloseSquareBracket);)l(r.Colon)?(e.push(void 0),++s,t=!0):(e.push(G()),l(r.Colon)&&(++s,t=!0));if(0===e.length)throw new SyntaxError("Expected at least one argument for member/slice expression");if(t){if(e.length>3)throw new SyntaxError("Expected 0-3 arguments for slice expression");return new j(...e)}return e[0]}function J(t){for(;l(r.Dot)||l(r.OpenSquareBracket);){const o=e[s];let a;++s;const i=o.type===r.OpenSquareBracket;if(i)a=H(),n(r.CloseSquareBracket,"Expected closing square bracket");else if(a=Z(),"Identifier"!==a.type)throw new SyntaxError("Expected identifier following dot operator");t=new T(t,a,i)}return t}function Y(){let t=K();for(;l(r.MultiplicativeBinaryOperator);){const r=e[s++],o=K();t=new A(r,t,o)}return t}function K(){let e=function(){let e=function(){const e=J(Z());return l(r.OpenParen)?Q(e):e}();for(;l(r.Pipe);){++s;let t=Z();if(!(t instanceof x))throw new SyntaxError("Expected identifier for the filter");l(r.OpenParen)&&(t=Q(t)),e=new E(e,t)}return e}();for(;d("is");){++s;const t=d("not");t&&++s;const r=Z();if(!(r instanceof x))throw new SyntaxError("Expected identifier for the test");e=new z(e,t,r)}return e}function Z(){const t=e[s++];switch(t.type){case r.NumericLiteral:{const e=t.value;return e.includes(".")?new F(Number(e)):new k(Number(e))}case r.StringLiteral:{let o=t.value;for(;l(r.StringLiteral);)o+=e[s++].value;return new v(o)}case r.Identifier:return new x(t.value);case r.OpenParen:{const e=P();return n(r.CloseParen,"Expected closing parenthesis, got ${tokens[current].type} instead."),e}case r.OpenSquareBracket:{const e=[];for(;!l(r.CloseSquareBracket);)e.push(G()),l(r.Comma)&&++s;return++s,new y(e)}case r.OpenCurlyBracket:{const e=new Map;for(;!l(r.CloseCurlyBracket);){const t=G();n(r.Colon,"Expected colon between key and value in object literal");const o=G();e.set(t,o),l(r.Comma)&&++s}return++s,new S(e)}default:throw new SyntaxError(`Unexpected token: ${t.type}`)}}for(;s<e.length;)t.body.push(i());return t}function R(e,t,s=1){void 0===t&&(t=e,e=0);const r=[];for(let o=e;o<t;o+=s)r.push(o);return r}function q(e,t,s,r=1){const o=Math.sign(r);o>=0?(t=(t??=0)<0?Math.max(e.length+t,0):Math.min(t,e.length),s=(s??=e.length)<0?Math.max(e.length+s,0):Math.min(s,e.length)):(t=(t??=e.length-1)<0?Math.max(e.length+t,-1):Math.min(t,e.length-1),s=(s??=-1)<-1?Math.max(e.length+s,-1):Math.min(s,e.length-1));const n=[];for(let a=t;o*a<o*s;a+=r)n.push(e[a]);return n}function $(e){return function(e,t){const s=new Intl.DateTimeFormat(void 0,{month:"long"}),r=new Intl.DateTimeFormat(void 0,{month:"short"}),o=e=>e<10?"0"+e:e.toString();return t.replace(/%[YmdbBHM%]/g,(t=>{switch(t){case"%Y":return e.getFullYear().toString();case"%m":return o(e.getMonth()+1);case"%d":return o(e.getDate());case"%b":return r.format(e);case"%B":return s.format(e);case"%H":return o(e.getHours());case"%M":return o(e.getMinutes());case"%%":return"%";default:return t}}))}(new Date,e)}var W=class extends Error{},U=class extends Error{},Q=class{type="RuntimeValue";value;builtins=new Map;constructor(e=void 0){this.value=e}__bool__(){return new Y(!!this.value)}toString(){return String(this.value)}},X=class extends Q{type="IntegerValue"},H=class extends Q{type="FloatValue";toString(){return this.value%1==0?this.value.toFixed(1):this.value.toString()}},J=class extends Q{type="StringValue";builtins=new Map([["upper",new se((()=>new J(this.value.toUpperCase())))],["lower",new se((()=>new J(this.value.toLowerCase())))],["strip",new se((()=>new J(this.value.trim())))],["title",new se((()=>new J(this.value.replace(/\b\w/g,(e=>e.toUpperCase())))))],["capitalize",new se((()=>new J(this.value.charAt(0).toUpperCase()+this.value.slice(1))))],["length",new X(this.value.length)],["rstrip",new se((()=>new J(this.value.trimEnd())))],["lstrip",new se((()=>new J(this.value.trimStart())))],["startswith",new se((e=>{if(0===e.length)throw new Error("startswith() requires at least one argument");const t=e[0];if(t instanceof J)return new Y(this.value.startsWith(t.value));if(t instanceof ee){for(const e of t.value){if(!(e instanceof J))throw new Error("startswith() tuple elements must be strings");if(this.value.startsWith(e.value))return new Y(!0)}return new Y(!1)}throw new Error("startswith() argument must be a string or tuple of strings")}))],["endswith",new se((e=>{if(0===e.length)throw new Error("endswith() requires at least one argument");const t=e[0];if(t instanceof J)return new Y(this.value.endsWith(t.value));if(t instanceof ee){for(const e of t.value){if(!(e instanceof J))throw new Error("endswith() tuple elements must be strings");if(this.value.endsWith(e.value))return new Y(!0)}return new Y(!1)}throw new Error("endswith() argument must be a string or tuple of strings")}))],["split",new se((e=>{const t=e[0]??new re;if(!(t instanceof J||t instanceof re))throw new Error("sep argument must be a string or null");const s=e[1]??new X(-1);if(!(s instanceof X))throw new Error("maxsplit argument must be a number");let r=[];if(t instanceof re){const e=this.value.trimStart();for(const{0:t,index:o}of e.matchAll(/\S+/g)){if(-1!==s.value&&r.length>=s.value&&void 0!==o){r.push(t+e.slice(o+t.length));break}r.push(t)}}else{if(""===t.value)throw new Error("empty separator");r=this.value.split(t.value),-1!==s.value&&r.length>s.value&&r.push(r.splice(s.value).join(t.value))}return new ee(r.map((e=>new J(e))))}))],["replace",new se((e=>{if(e.length<2)throw new Error("replace() requires at least two arguments");const t=e[0],s=e[1];if(!(t instanceof J&&s instanceof J))throw new Error("replace() arguments must be strings");let r;if(r=e.length>2?"KeywordArgumentsValue"===e[2].type?e[2].value.get("count")??new re:e[2]:new re,!(r instanceof X||r instanceof re))throw new Error("replace() count argument must be a number or null");return new J(function(e,t,s,r){if(0===r)return e;let o=null==r||r<0?1/0:r;const n=0===t.length?new RegExp("(?=)","gu"):new RegExp(t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gu");return e.replaceAll(n,(e=>o>0?(--o,s):e))}(this.value,t.value,s.value,r.value))}))]])},Y=class extends Q{type="BooleanValue"},K=class extends Q{type="ObjectValue";__bool__(){return new Y(this.value.size>0)}builtins=new Map([["get",new se((([e,t])=>{if(!(e instanceof J))throw new Error(`Object key must be a string: got ${e.type}`);return this.value.get(e.value)??t??new re}))],["items",new se((()=>this.items()))],["keys",new se((()=>this.keys()))],["values",new se((()=>this.values()))]]);items(){return new ee(Array.from(this.value.entries()).map((([e,t])=>new ee([new J(e),t]))))}keys(){return new ee(Array.from(this.value.keys()).map((e=>new J(e))))}values(){return new ee(Array.from(this.value.values()))}},Z=class extends K{type="KeywordArgumentsValue"},ee=class extends Q{type="ArrayValue";builtins=new Map([["length",new X(this.value.length)]]);__bool__(){return new Y(this.value.length>0)}},te=class extends ee{type="TupleValue"},se=class extends Q{type="FunctionValue"},re=class extends Q{type="NullValue"},oe=class extends Q{type="UndefinedValue"},ne=class{constructor(e){this.parent=e}variables=new Map([["namespace",new se((e=>{if(0===e.length)return new K(new Map);if(1!==e.length||!(e[0]instanceof K))throw new Error("`namespace` expects either zero arguments or a single object argument");return e[0]}))]]);tests=new Map([["boolean",e=>"BooleanValue"===e.type],["callable",e=>e instanceof se],["odd",e=>{if(!(e instanceof X))throw new Error(`cannot odd on ${e.type}`);return e.value%2!=0}],["even",e=>{if(!(e instanceof X))throw new Error(`cannot even on ${e.type}`);return e.value%2==0}],["false",e=>"BooleanValue"===e.type&&!e.value],["true",e=>"BooleanValue"===e.type&&e.value],["none",e=>"NullValue"===e.type],["string",e=>"StringValue"===e.type],["number",e=>e instanceof X||e instanceof H],["integer",e=>e instanceof X],["iterable",e=>"ArrayValue"===e.type||"StringValue"===e.type],["mapping",e=>"ObjectValue"===e.type],["lower",e=>{const t=e.value;return"StringValue"===e.type&&t===t.toLowerCase()}],["upper",e=>{const t=e.value;return"StringValue"===e.type&&t===t.toUpperCase()}],["none",e=>"NullValue"===e.type],["defined",e=>"UndefinedValue"!==e.type],["undefined",e=>"UndefinedValue"===e.type],["equalto",(e,t)=>e.value===t.value],["eq",(e,t)=>e.value===t.value]]);set(e,t){return this.declareVariable(e,ie(t))}declareVariable(e,t){if(this.variables.has(e))throw new SyntaxError(`Variable already declared: ${e}`);return this.variables.set(e,t),t}setVariable(e,t){return this.variables.set(e,t),t}resolve(e){if(this.variables.has(e))return this;if(this.parent)return this.parent.resolve(e);throw new Error(`Unknown variable: ${e}`)}lookupVariable(e){try{return this.resolve(e).variables.get(e)??new oe}catch{return new oe}}};var ae=class{global;constructor(e){this.global=e??new ne}run(e){return this.evaluate(e,this.global)}evaluateBinaryExpression(e,t){const s=this.evaluate(e.left,t);switch(e.operator.value){case"and":return s.__bool__().value?this.evaluate(e.right,t):s;case"or":return s.__bool__().value?s:this.evaluate(e.right,t)}const r=this.evaluate(e.right,t);switch(e.operator.value){case"==":return new Y(s.value==r.value);case"!=":return new Y(s.value!=r.value)}if(s instanceof oe||r instanceof oe){if(r instanceof oe&&["in","not in"].includes(e.operator.value))return new Y("not in"===e.operator.value);throw new Error(`Cannot perform operation ${e.operator.value} on undefined values`)}if(s instanceof re||r instanceof re)throw new Error("Cannot perform operation on null values");if("~"===e.operator.value)return new J(s.value.toString()+r.value.toString());if((s instanceof X||s instanceof H)&&(r instanceof X||r instanceof H)){const t=s.value,o=r.value;switch(e.operator.value){case"+":case"-":case"*":{const n="+"===e.operator.value?t+o:"-"===e.operator.value?t-o:t*o;return s instanceof H||r instanceof H?new H(n):new X(n)}case"/":return new H(t/o);case"%":{const e=t%o;return s instanceof H||r instanceof H?new H(e):new X(e)}case"<":return new Y(t<o);case">":return new Y(t>o);case">=":return new Y(t>=o);case"<=":return new Y(t<=o)}}else if(s instanceof ee&&r instanceof ee){if("+"===e.operator.value)return new ee(s.value.concat(r.value))}else if(r instanceof ee){const t=void 0!==r.value.find((e=>e.value===s.value));switch(e.operator.value){case"in":return new Y(t);case"not in":return new Y(!t)}}if((s instanceof J||r instanceof J)&&"+"===e.operator.value)return new J(s.value.toString()+r.value.toString());if(s instanceof J&&r instanceof J)switch(e.operator.value){case"in":return new Y(r.value.includes(s.value));case"not in":return new Y(!r.value.includes(s.value))}if(s instanceof J&&r instanceof K)switch(e.operator.value){case"in":return new Y(r.value.has(s.value));case"not in":return new Y(!r.value.has(s.value))}throw new SyntaxError(`Unknown operator "${e.operator.value}" between ${s.type} and ${r.type}`)}evaluateArguments(e,t){const s=[],r=new Map;for(const o of e)if("SpreadExpression"===o.type){const e=o,r=this.evaluate(e.argument,t);if(!(r instanceof ee))throw new Error(`Cannot unpack non-iterable type: ${r.type}`);for(const e of r.value)s.push(e)}else if("KeywordArgumentExpression"===o.type){const e=o;r.set(e.key.value,this.evaluate(e.value,t))}else{if(r.size>0)throw new Error("Positional arguments must come before keyword arguments");s.push(this.evaluate(o,t))}return[s,r]}applyFilter(e,t,s){if("Identifier"===t.type){const r=t;if("tojson"===r.value)return new J(le(e));if(e instanceof ee)switch(r.value){case"list":return e;case"first":return e.value[0];case"last":return e.value[e.value.length-1];case"length":return new X(e.value.length);case"reverse":return new ee(e.value.reverse());case"sort":return new ee(e.value.sort(((e,t)=>{if(e.type!==t.type)throw new Error(`Cannot compare different types: ${e.type} and ${t.type}`);switch(e.type){case"IntegerValue":case"FloatValue":return e.value-t.value;case"StringValue":return e.value.localeCompare(t.value);default:throw new Error(`Cannot compare type: ${e.type}`)}})));case"join":return new J(e.value.map((e=>e.value)).join(""));case"string":return new J(le(e));case"unique":{const t=new Set,s=[];for(const r of e.value)t.has(r.value)||(t.add(r.value),s.push(r));return new ee(s)}default:throw new Error(`Unknown ArrayValue filter: ${r.value}`)}else if(e instanceof J)switch(r.value){case"length":case"upper":case"lower":case"title":case"capitalize":{const t=e.builtins.get(r.value);if(t instanceof se)return t.value([],s);if(t instanceof X)return t;throw new Error(`Unknown StringValue filter: ${r.value}`)}case"trim":return new J(e.value.trim());case"indent":return new J(e.value.split("\n").map(((e,t)=>0===t||0===e.length?e:"    "+e)).join("\n"));case"join":case"string":return e;case"int":{const t=parseInt(e.value,10);return new X(isNaN(t)?0:t)}case"float":{const t=parseFloat(e.value);return new H(isNaN(t)?0:t)}default:throw new Error(`Unknown StringValue filter: ${r.value}`)}else if(e instanceof X||e instanceof H)switch(r.value){case"abs":return e instanceof X?new X(Math.abs(e.value)):new H(Math.abs(e.value));case"int":return new X(Math.floor(e.value));case"float":return new H(e.value);default:throw new Error(`Unknown NumericValue filter: ${r.value}`)}else if(e instanceof K)switch(r.value){case"items":return new ee(Array.from(e.value.entries()).map((([e,t])=>new ee([new J(e),t]))));case"length":return new X(e.value.size);default:throw new Error(`Unknown ObjectValue filter: ${r.value}`)}else if(e instanceof Y)switch(r.value){case"bool":return new Y(e.value);case"int":return new X(e.value?1:0);case"float":return new H(e.value?1:0);case"string":return new J(e.value?"true":"false");default:throw new Error(`Unknown BooleanValue filter: ${r.value}`)}throw new Error(`Cannot apply filter "${r.value}" to type: ${e.type}`)}if("CallExpression"===t.type){const r=t;if("Identifier"!==r.callee.type)throw new Error(`Unknown filter: ${r.callee.type}`);const o=r.callee.value;if("tojson"===o){const[,t]=this.evaluateArguments(r.args,s),o=t.get("indent")??new re;if(!(o instanceof X||o instanceof re))throw new Error("If set, indent must be a number");return new J(le(e,o.value))}if("join"===o){let t;if(e instanceof J)t=Array.from(e.value);else{if(!(e instanceof ee))throw new Error(`Cannot apply filter "${o}" to type: ${e.type}`);t=e.value.map((e=>e.value))}const[n,a]=this.evaluateArguments(r.args,s),i=n.at(0)??a.get("separator")??new J("");if(!(i instanceof J))throw new Error("separator must be a string");return new J(t.join(i.value))}if("int"===o||"float"===o){const[t,n]=this.evaluateArguments(r.args,s),a=t.at(0)??n.get("default")??("int"===o?new X(0):new H(0));if(e instanceof J){const t="int"===o?parseInt(e.value,10):parseFloat(e.value);return isNaN(t)?a:"int"===o?new X(t):new H(t)}if(e instanceof X||e instanceof H)return e;if(e instanceof Y)return"int"===o?new X(e.value?1:0):new H(e.value?1:0);throw new Error(`Cannot apply filter "${o}" to type: ${e.type}`)}if("default"===o){const[t,o]=this.evaluateArguments(r.args,s),n=t[0]??new J(""),a=t[1]??o.get("boolean")??new Y(!1);if(!(a instanceof Y))throw new Error("`default` filter flag must be a boolean");return e instanceof oe||a.value&&!e.__bool__().value?n:e}if(e instanceof ee){switch(o){case"selectattr":case"rejectattr":{const t="selectattr"===o;if(e.value.some((e=>!(e instanceof K))))throw new Error(`\`${o}\` can only be applied to array of objects`);if(r.args.some((e=>"StringLiteral"!==e.type)))throw new Error(`arguments of \`${o}\` must be strings`);const[n,a,i]=r.args.map((e=>this.evaluate(e,s)));let l;if(a){const e=s.tests.get(a.value);if(!e)throw new Error(`Unknown test: ${a.value}`);l=e}else l=(...e)=>e[0].__bool__().value;const c=e.value.filter((e=>{const s=e.value.get(n.value),r=!!s&&l(s,i);return t?r:!r}));return new ee(c)}case"map":{const[,t]=this.evaluateArguments(r.args,s);if(t.has("attribute")){const s=t.get("attribute");if(!(s instanceof J))throw new Error("attribute must be a string");const r=t.get("default"),o=e.value.map((e=>{if(!(e instanceof K))throw new Error("items in map must be an object");return e.value.get(s.value)??r??new oe}));return new ee(o)}throw new Error("`map` expressions without `attribute` set are not currently supported.")}}throw new Error(`Unknown ArrayValue filter: ${o}`)}if(e instanceof J){switch(o){case"indent":{const[t,o]=this.evaluateArguments(r.args,s),n=t.at(0)??o.get("width")??new X(4);if(!(n instanceof X))throw new Error("width must be a number");const a=t.at(1)??o.get("first")??new Y(!1),i=t.at(2)??o.get("blank")??new Y(!1),l=e.value.split("\n"),c=" ".repeat(n.value),d=l.map(((e,t)=>!a.value&&0===t||!i.value&&0===e.length?e:c+e));return new J(d.join("\n"))}case"replace":{const t=e.builtins.get("replace");if(!(t instanceof se))throw new Error("replace filter not available");const[o,n]=this.evaluateArguments(r.args,s);return t.value([...o,new Z(n)],s)}}throw new Error(`Unknown StringValue filter: ${o}`)}throw new Error(`Cannot apply filter "${o}" to type: ${e.type}`)}throw new Error(`Unknown filter: ${t.type}`)}evaluateFilterExpression(e,t){const s=this.evaluate(e.operand,t);return this.applyFilter(s,e.filter,t)}evaluateTestExpression(e,t){const s=this.evaluate(e.operand,t),r=t.tests.get(e.test.value);if(!r)throw new Error(`Unknown test: ${e.test.value}`);const o=r(s);return new Y(e.negate?!o:o)}evaluateSelectExpression(e,t){return this.evaluate(e.test,t).__bool__().value?this.evaluate(e.lhs,t):new oe}evaluateUnaryExpression(e,t){const s=this.evaluate(e.argument,t);if("not"===e.operator.value)return new Y(!s.value);throw new SyntaxError(`Unknown operator: ${e.operator.value}`)}evaluateTernaryExpression(e,t){return this.evaluate(e.condition,t).__bool__().value?this.evaluate(e.trueExpr,t):this.evaluate(e.falseExpr,t)}evalProgram(e,t){return this.evaluateBlock(e.body,t)}evaluateBlock(e,t){let s="";for(const r of e){const e=this.evaluate(r,t);"NullValue"!==e.type&&"UndefinedValue"!==e.type&&(s+=e.toString())}return new J(s)}evaluateIdentifier(e,t){return t.lookupVariable(e.value)}evaluateCallExpression(e,t){const[s,r]=this.evaluateArguments(e.args,t);r.size>0&&s.push(new Z(r));const o=this.evaluate(e.callee,t);if("FunctionValue"!==o.type)throw new Error(`Cannot call something that is not a function: got ${o.type}`);return o.value(s,t)}evaluateSliceExpression(e,t,s){if(!(e instanceof ee||e instanceof J))throw new Error("Slice object must be an array or string");const r=this.evaluate(t.start,s),o=this.evaluate(t.stop,s),n=this.evaluate(t.step,s);if(!(r instanceof X||r instanceof oe))throw new Error("Slice start must be numeric or undefined");if(!(o instanceof X||o instanceof oe))throw new Error("Slice stop must be numeric or undefined");if(!(n instanceof X||n instanceof oe))throw new Error("Slice step must be numeric or undefined");return e instanceof ee?new ee(q(e.value,r.value,o.value,n.value)):new J(q(Array.from(e.value),r.value,o.value,n.value).join(""))}evaluateMemberExpression(e,t){const s=this.evaluate(e.object,t);let r,o;if(e.computed){if("SliceExpression"===e.property.type)return this.evaluateSliceExpression(s,e.property,t);r=this.evaluate(e.property,t)}else r=new J(e.property.value);if(s instanceof K){if(!(r instanceof J))throw new Error(`Cannot access property with non-string: got ${r.type}`);o=s.value.get(r.value)??s.builtins.get(r.value)}else if(s instanceof ee||s instanceof J)if(r instanceof X)o=s.value.at(r.value),s instanceof J&&(o=new J(s.value.at(r.value)));else{if(!(r instanceof J))throw new Error(`Cannot access property with non-string/non-number: got ${r.type}`);o=s.builtins.get(r.value)}else{if(!(r instanceof J))throw new Error(`Cannot access property with non-string: got ${r.type}`);o=s.builtins.get(r.value)}return o instanceof Q?o:new oe}evaluateSet(e,t){const s=e.value?this.evaluate(e.value,t):this.evaluateBlock(e.body,t);if("Identifier"===e.assignee.type){const r=e.assignee.value;t.setVariable(r,s)}else if("TupleLiteral"===e.assignee.type){const r=e.assignee;if(!(s instanceof ee))throw new Error(`Cannot unpack non-iterable type in set: ${s.type}`);const o=s.value;if(o.length!==r.value.length)throw new Error(`Too ${r.value.length>o.length?"few":"many"} items to unpack in set`);for(let e=0;e<r.value.length;++e){const s=r.value[e];if("Identifier"!==s.type)throw new Error(`Cannot unpack to non-identifier in set: ${s.type}`);t.setVariable(s.value,o[e])}}else{if("MemberExpression"!==e.assignee.type)throw new Error(`Invalid LHS inside assignment expression: ${JSON.stringify(e.assignee)}`);{const r=e.assignee,o=this.evaluate(r.object,t);if(!(o instanceof K))throw new Error("Cannot assign to member of non-object");if("Identifier"!==r.property.type)throw new Error("Cannot assign to member with non-identifier property");o.value.set(r.property.value,s)}}return new re}evaluateIf(e,t){const s=this.evaluate(e.test,t);return this.evaluateBlock(s.__bool__().value?e.body:e.alternate,t)}evaluateFor(e,t){const s=new ne(t);let r,o;if("SelectExpression"===e.iterable.type){const t=e.iterable;o=this.evaluate(t.lhs,s),r=t.test}else o=this.evaluate(e.iterable,s);if(!(o instanceof ee||o instanceof K))throw new Error(`Expected iterable or object type in for loop: got ${o.type}`);o instanceof K&&(o=o.keys());const n=[],a=[];for(let t=0;t<o.value.length;++t){const i=new ne(s),l=o.value[t];let c;if("Identifier"===e.loopvar.type)c=t=>t.setVariable(e.loopvar.value,l);else{if("TupleLiteral"!==e.loopvar.type)throw new Error(`Invalid loop variable(s): ${e.loopvar.type}`);{const t=e.loopvar;if("ArrayValue"!==l.type)throw new Error(`Cannot unpack non-iterable type: ${l.type}`);const s=l;if(t.value.length!==s.value.length)throw new Error(`Too ${t.value.length>s.value.length?"few":"many"} items to unpack`);c=e=>{for(let r=0;r<t.value.length;++r){if("Identifier"!==t.value[r].type)throw new Error(`Cannot unpack non-identifier type: ${t.value[r].type}`);e.setVariable(t.value[r].value,s.value[r])}}}}if(r){c(i);if(!this.evaluate(r,i).__bool__().value)continue}n.push(l),a.push(c)}let i="",l=!0;for(let t=0;t<n.length;++t){const r=new Map([["index",new X(t+1)],["index0",new X(t)],["revindex",new X(n.length-t)],["revindex0",new X(n.length-t-1)],["first",new Y(0===t)],["last",new Y(t===n.length-1)],["length",new X(n.length)],["previtem",t>0?n[t-1]:new oe],["nextitem",t<n.length-1?n[t+1]:new oe]]);s.setVariable("loop",new K(r)),a[t](s);try{i+=this.evaluateBlock(e.body,s).value}catch(e){if(e instanceof U)continue;if(e instanceof W)break;throw e}l=!1}if(l){i+=this.evaluateBlock(e.defaultBlock,s).value}return new J(i)}evaluateMacro(e,t){return t.setVariable(e.name.value,new se(((t,s)=>{const r=new ne(s);let o;t=t.slice(),"KeywordArgumentsValue"===t.at(-1)?.type&&(o=t.pop());for(let s=0;s<e.args.length;++s){const n=e.args[s],a=t[s];if("Identifier"===n.type){const e=n;if(!a)throw new Error(`Missing positional argument: ${e.value}`);r.setVariable(e.value,a)}else{if("KeywordArgumentExpression"!==n.type)throw new Error(`Unknown argument type: ${n.type}`);{const e=n,t=a??o?.value.get(e.key.value)??this.evaluate(e.value,r);r.setVariable(e.key.value,t)}}}return this.evaluateBlock(e.body,r)}))),new re}evaluateCallStatement(e,t){const s=new se(((t,s)=>{const r=new ne(s);if(e.callerArgs)for(let s=0;s<e.callerArgs.length;++s){const o=e.callerArgs[s];if("Identifier"!==o.type)throw new Error(`Caller parameter must be an identifier, got ${o.type}`);r.setVariable(o.value,t[s]??new oe)}return this.evaluateBlock(e.body,r)})),[r,o]=this.evaluateArguments(e.call.args,t);r.push(new Z(o));const n=this.evaluate(e.call.callee,t);if("FunctionValue"!==n.type)throw new Error(`Cannot call something that is not a function: got ${n.type}`);const a=new ne(t);return a.setVariable("caller",s),n.value(r,a)}evaluateFilterStatement(e,t){const s=this.evaluateBlock(e.body,t);return this.applyFilter(s,e.filter,t)}evaluate(e,t){if(!e)return new oe;switch(e.type){case"Program":return this.evalProgram(e,t);case"Set":return this.evaluateSet(e,t);case"If":return this.evaluateIf(e,t);case"For":return this.evaluateFor(e,t);case"Macro":return this.evaluateMacro(e,t);case"CallStatement":return this.evaluateCallStatement(e,t);case"Break":throw new W;case"Continue":throw new U;case"IntegerLiteral":return new X(e.value);case"FloatLiteral":return new H(e.value);case"StringLiteral":return new J(e.value);case"ArrayLiteral":return new ee(e.value.map((e=>this.evaluate(e,t))));case"TupleLiteral":return new te(e.value.map((e=>this.evaluate(e,t))));case"ObjectLiteral":{const s=new Map;for(const[r,o]of e.value){const e=this.evaluate(r,t);if(!(e instanceof J))throw new Error(`Object keys must be strings: got ${e.type}`);s.set(e.value,this.evaluate(o,t))}return new K(s)}case"Identifier":return this.evaluateIdentifier(e,t);case"CallExpression":return this.evaluateCallExpression(e,t);case"MemberExpression":return this.evaluateMemberExpression(e,t);case"UnaryExpression":return this.evaluateUnaryExpression(e,t);case"BinaryExpression":return this.evaluateBinaryExpression(e,t);case"FilterExpression":return this.evaluateFilterExpression(e,t);case"FilterStatement":return this.evaluateFilterStatement(e,t);case"TestExpression":return this.evaluateTestExpression(e,t);case"SelectExpression":return this.evaluateSelectExpression(e,t);case"Ternary":return this.evaluateTernaryExpression(e,t);case"Comment":return new re;default:throw new SyntaxError(`Unknown node type: ${e.type}`)}}};function ie(e){switch(typeof e){case"number":return Number.isInteger(e)?new X(e):new H(e);case"string":return new J(e);case"boolean":return new Y(e);case"undefined":return new oe;case"object":return null===e?new re:Array.isArray(e)?new ee(e.map(ie)):new K(new Map(Object.entries(e).map((([e,t])=>[e,ie(t)]))));case"function":return new se(((t,s)=>ie(e(...t.map((e=>e.value)))??null)));default:throw new Error(`Cannot convert to runtime value: ${e}`)}}function le(e,t,s){const r=s??0;switch(e.type){case"NullValue":case"UndefinedValue":return"null";case"IntegerValue":case"FloatValue":case"StringValue":case"BooleanValue":return JSON.stringify(e.value);case"ArrayValue":case"ObjectValue":{const s=t?" ".repeat(t):"",o="\n"+s.repeat(r),n=o+s;if("ArrayValue"===e.type){const s=e.value.map((e=>le(e,t,r+1)));return t?`[${n}${s.join(`,${n}`)}${o}]`:`[${s.join(", ")}]`}{const s=Array.from(e.value.entries()).map((([e,s])=>{const o=`"${e}": ${le(s,t,r+1)}`;return t?`${n}${o}`:o}));return t?`{${s.join(",")}${o}}`:`{${s.join(", ")}}`}}default:throw new Error(`Cannot convert to JSON: ${e.type}`)}}var ce="\n",de="{%- ",ue=" -%}";function _e(...e){return de+e.join(" ")+ue}function me(e,t,s){return e.map((e=>function(e,t,s){const r=s.repeat(t);switch(e.type){case"Program":return me(e.body,t,s);case"If":return function(e,t,s){const r=s.repeat(t),o=[];let n=e;for(;n&&(o.push({test:n.test,body:n.body}),1===n.alternate.length&&"If"===n.alternate[0].type);)n=n.alternate[0];let a=r+_e("if",pe(o[0].test))+ce+me(o[0].body,t+1,s);for(let e=1;e<o.length;++e)a+=ce+r+_e("elif",pe(o[e].test))+ce+me(o[e].body,t+1,s);n&&n.alternate.length>0&&(a+=ce+r+_e("else")+ce+me(n.alternate,t+1,s));return a+=ce+r+_e("endif"),a}(e,t,s);case"For":return function(e,t,s){const r=s.repeat(t);let o="";if("SelectExpression"===e.iterable.type){const t=e.iterable;o=`${pe(t.lhs)} if ${pe(t.test)}`}else o=pe(e.iterable);let n=r+_e("for",pe(e.loopvar),"in",o)+ce+me(e.body,t+1,s);e.defaultBlock.length>0&&(n+=ce+r+_e("else")+ce+me(e.defaultBlock,t+1,s));return n+=ce+r+_e("endfor"),n}(e,t,s);case"Set":return function(e,t,s){const r=s.repeat(t),o=pe(e.assignee),n=e.value?pe(e.value):"",a=r+_e("set",`${o}${e.value?" = "+n:""}`);if(0===e.body.length)return a;return a+ce+me(e.body,t+1,s)+ce+r+_e("endset")}(e,t,s);case"Macro":return function(e,t,s){const r=s.repeat(t),o=e.args.map(pe).join(", ");return r+_e("macro",`${e.name.value}(${o})`)+ce+me(e.body,t+1,s)+ce+r+_e("endmacro")}(e,t,s);case"Break":return r+_e("break");case"Continue":return r+_e("continue");case"CallStatement":return function(e,t,s){const r=s.repeat(t),o=e.callerArgs&&e.callerArgs.length>0?`(${e.callerArgs.map(pe).join(", ")})`:"",n=pe(e.call);let a=r+_e(`call${o}`,n)+ce;return a+=me(e.body,t+1,s)+ce,a+=r+_e("endcall"),a}(e,t,s);case"FilterStatement":return function(e,t,s){const r=s.repeat(t),o="Identifier"===e.filter.type?e.filter.value:pe(e.filter);let n=r+_e("filter",o)+ce;return n+=me(e.body,t+1,s)+ce,n+=r+_e("endfilter"),n}(e,t,s);case"Comment":return r+"{# "+e.value+" #}";default:return r+"{{- "+pe(e)+" -}}"}}(e,t,s))).join(ce)}function pe(e,t=-1){switch(e.type){case"SpreadExpression":return`*${pe(e.argument)}`;case"Identifier":return e.value;case"IntegerLiteral":case"FloatLiteral":return`${e.value}`;case"StringLiteral":return JSON.stringify(e.value);case"BinaryExpression":{const s=e,r=function(e){switch(e.operator.type){case"MultiplicativeBinaryOperator":return 4;case"AdditiveBinaryOperator":return 3;case"ComparisonBinaryOperator":return 2;case"Identifier":return"and"===e.operator.value?1:"in"===e.operator.value||"not in"===e.operator.value?2:0}return 0}(s),o=pe(s.left,r),n=pe(s.right,r+1),a=`${o} ${s.operator.value} ${n}`;return r<t?`(${a})`:a}case"UnaryExpression":{const t=e;return t.operator.value+("not"===t.operator.value?" ":"")+pe(t.argument,1/0)}case"CallExpression":{const t=e,s=t.args.map(pe).join(", ");return`${pe(t.callee)}(${s})`}case"MemberExpression":{const t=e;let s=pe(t.object);["Identifier","MemberExpression","CallExpression","StringLiteral","IntegerLiteral","FloatLiteral","ArrayLiteral","TupleLiteral","ObjectLiteral"].includes(t.object.type)||(s=`(${s})`);let r=pe(t.property);return t.computed||"Identifier"===t.property.type||(r=`(${r})`),t.computed?`${s}[${r}]`:`${s}.${r}`}case"FilterExpression":{const t=e,s=pe(t.operand,1/0);return"CallExpression"===t.filter.type?`${s} | ${pe(t.filter)}`:`${s} | ${t.filter.value}`}case"SelectExpression":{const t=e;return`${pe(t.lhs)} if ${pe(t.test)}`}case"TestExpression":{const t=e;return`${pe(t.operand)} is${t.negate?" not":""} ${t.test.value}`}case"ArrayLiteral":case"TupleLiteral":{const t=e.value.map(pe),s="ArrayLiteral"===e.type?"[]":"()";return`${s[0]}${t.join(", ")}${s[1]}`}case"ObjectLiteral":return`{${Array.from(e.value.entries()).map((([e,t])=>`${pe(e)}: ${pe(t)}`)).join(", ")}}`;case"SliceExpression":{const t=e;return`${t.start?pe(t.start):""}:${t.stop?pe(t.stop):""}${t.step?`:${pe(t.step)}`:""}`}case"KeywordArgumentExpression":{const t=e;return`${t.key.value}=${pe(t.value)}`}case"Ternary":{const s=e,r=`${pe(s.trueExpr)} if ${pe(s.condition,0)} else ${pe(s.falseExpr)}`;return t>-1?`(${r})`:r}default:throw new Error(`Unknown expression type: ${e.type}`)}}var he=class{parsed;constructor(e){const t=c(e,{lstrip_blocks:!0,trim_blocks:!0});this.parsed=G(t)}render(e){const t=new ne;if(function(e){e.set("false",!1),e.set("true",!0),e.set("none",null),e.set("raise_exception",(e=>{throw new Error(e)})),e.set("range",R),e.set("strftime_now",$),e.set("True",!0),e.set("False",!1),e.set("None",null)}(t),e)for(const[s,r]of Object.entries(e))t.set(s,r);return new ae(t).run(this.parsed).value}format(e){return function(e,t="\t"){const s="number"==typeof t?" ".repeat(t):t;return me(e.body,0,s).replace(/\n$/,"")}(this.parsed,e?.indent||"\t")}}},"./src/backends/onnx.js":(e,t,s)=>{var r;s.r(t),s.d(t,{Tensor:()=>i.Tensor,createInferenceSession:()=>g,deviceToExecutionProviders:()=>p,isONNXProxy:()=>w,isONNXTensor:()=>f});var o=s("./src/env.js"),n=s("?2ce3"),a=s("onnxruntime-web"),i=s("onnxruntime-common");const l=Object.freeze({auto:null,gpu:null,cpu:"cpu",wasm:"wasm",webgpu:"webgpu",cuda:"cuda",dml:"dml",webnn:{name:"webnn",deviceType:"cpu"},"webnn-npu":{name:"webnn",deviceType:"npu"},"webnn-gpu":{name:"webnn",deviceType:"gpu"},"webnn-cpu":{name:"webnn",deviceType:"cpu"}}),c=[];let d,u;const _=Symbol.for("onnxruntime");if(_ in globalThis)u=globalThis[_];else if(o.apis.IS_NODE_ENV){switch(u=n??(r||(r=s.t(n,2))),process.platform){case"win32":c.push("dml");break;case"linux":"x64"===process.arch&&c.push("cuda")}c.push("cpu"),d=["cpu"]}else u=a,o.apis.IS_WEBNN_AVAILABLE&&c.push("webnn-npu","webnn-gpu","webnn-cpu","webnn"),o.apis.IS_WEBGPU_AVAILABLE&&c.push("webgpu"),c.push("wasm"),d=["wasm"];const m=u.InferenceSession;function p(e=null){if(!e)return d;switch(e){case"auto":return c;case"gpu":return c.filter((e=>["webgpu","cuda","dml","webnn-gpu"].includes(e)))}if(c.includes(e))return[l[e]??e];throw new Error(`Unsupported device: "${e}". Should be one of: ${c.join(", ")}.`)}let h=null;async function g(e,t,s){h&&await h;const r=m.create(e,t);h??=r;const o=await r;return o.config=s,o}function f(e){return e instanceof u.Tensor}const M=u?.env;function w(){return M?.wasm?.proxy}M?.wasm&&("undefined"!=typeof ServiceWorkerGlobalScope&&self instanceof ServiceWorkerGlobalScope||M.wasm.wasmPaths||(M.wasm.wasmPaths=`https://cdn.jsdelivr.net/npm/@huggingface/transformers@${o.env.version}/dist/`),M.wasm.proxy=!1),M?.webgpu&&(M.webgpu.powerPreference="high-performance"),o.env.backends.onnx=M},"./src/base/feature_extraction_utils.js":(e,t,s)=>{s.r(t),s.d(t,{FeatureExtractor:()=>a,validate_audio_inputs:()=>i});var r=s("./src/utils/constants.js"),o=s("./src/utils/generic.js"),n=s("./src/utils/hub.js");class a extends o.Callable{constructor(e){super(),this.config=e}static async from_pretrained(e,t={}){return new this(await(0,n.getModelJSON)(e,r.FEATURE_EXTRACTOR_NAME,!0,t))}}function i(e,t){if(!(e instanceof Float32Array||e instanceof Float64Array))throw new Error(`${t} expects input to be a Float32Array or a Float64Array, but got ${e?.constructor?.name??typeof e} instead. If using the feature extractor directly, remember to use \`read_audio(url, sampling_rate)\` to obtain the raw audio data of the file/url.`)}},"./src/base/image_processors_utils.js":(e,t,s)=>{s.r(t),s.d(t,{ImageProcessor:()=>w,center_to_corners_format:()=>u,post_process_instance_segmentation:()=>M,post_process_object_detection:()=>_,post_process_panoptic_segmentation:()=>f,post_process_semantic_segmentation:()=>m});var r=s("./src/utils/generic.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/maths.js"),a=(s("./src/utils/image.js"),s("./src/utils/core.js")),i=s("./src/utils/hub.js"),l=s("./src/utils/constants.js");function c(e,t,s=0,r=null){const o=e/t;let a=(0,n.bankers_round)(o)*t;return null!==r&&a>r&&(a=Math.floor(o)*t),a<s&&(a=Math.ceil(o)*t),a}function d([e,t],s){return[Math.max(Math.floor(e/s),1)*s,Math.max(Math.floor(t/s),1)*s]}function u([e,t,s,r]){return[e-s/2,t-r/2,e+s/2,t+r/2]}function _(e,t=.5,s=null,r=!1){const o=e.logits,a=e.pred_boxes,[i,l,c]=o.dims;if(null!==s&&s.length!==i)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");let d=[];for(let e=0;e<i;++e){let i=null!==s?s[e]:null,_={boxes:[],classes:[],scores:[]},m=o[e],p=a[e];for(let e=0;e<l;++e){let s,o=m[e],a=[];if(r){s=o.sigmoid().data;for(let e=0;e<s.length;++e)s[e]>t&&a.push(e)}else{let e=(0,n.max)(o.data)[1];if(e===c-1)continue;if(s=(0,n.softmax)(o.data),s[e]<t)continue;a.push(e)}for(const t of a){let r=p[e].data;r=u(r),null!==i&&(r=r.map(((e,t)=>e*i[(t+1)%2]))),_.boxes.push(r),_.classes.push(t),_.scores.push(s[t])}}d.push(_)}return d}function m(e,t=null){const s=e.logits,r=s.dims[0];if(null!==t&&t.length!==r)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");const n=[];for(let e=0;e<r;++e){const r=null!==t?t[e]:null;let a=s[e];null!==r&&(a=(0,o.interpolate)(a,r,"bilinear",!1));const[i,l]=r??a.dims.slice(-2),c=new o.Tensor("int32",new Int32Array(i*l),[i,l]),d=a[0].data,u=c.data;for(let e=1;e<a.dims[0];++e){const t=a[e].data;for(let s=0;s<t.length;++s)t[s]>d[s]&&(d[s]=t[s],u[s]=e)}const _=new Array(a.dims[0]);for(let e=0;e<u.length;++e){const t=u[e];_[t]=t}const m=_.filter((e=>void 0!==e));n.push({segmentation:c,labels:m})}return n}function p(e,t,s,r){const o=[],a=[],i=[];for(let l=0;l<e.dims[0];++l){const c=e[l],d=t[l],u=(0,n.max)(c.data)[1];if(u===r)continue;const _=(0,n.softmax)(c.data)[u];_>s&&(o.push(d),a.push(_),i.push(u))}return[o,a,i]}function h(e,t,s,r=.5,o=.8){const n=[];let a=0,i=0;const l=t[s].data;for(let t=0;t<e.length;++t)e[t]===s&&(n.push(t),++a),l[t]>=r&&++i;let c=a>0&&i>0;if(c){c=a/i>o}return[c,n]}function g(e,t,s,r,n,a=null,i=null){const[l,c]=i??e[0].dims,d=new o.Tensor("int32",new Int32Array(l*c),[l,c]),u=[];if(null!==i)for(let t=0;t<e.length;++t)e[t]=(0,o.interpolate)(e[t],i,"bilinear",!1);const _=new Int32Array(e[0].data.length),m=new Float32Array(e[0].data.length);for(let s=0;s<e.length;++s){let r=t[s];const o=e[s].data;for(let e=0;e<o.length;++e)o[e]*=r,o[e]>m[e]&&(_[e]=s,m[e]=o[e])}let p=0;const g=d.data;for(let o=0;o<s.length;++o){const a=s[o],[i,l]=h(_,e,o,r,n);if(i){++p;for(const e of l)g[e]=p;u.push({id:p,label_id:a,score:t[o]})}}return[d,u]}function f(e,t=.5,s=.5,r=.8,n=null,a=null){null===n&&(console.warn("`label_ids_to_fuse` unset. No instance will be fused."),n=new Set);const i=e.class_queries_logits??e.logits,l=(e.masks_queries_logits??e.pred_masks).sigmoid();let[c,d,u]=i.dims;if(u-=1,null!==a&&a.length!==c)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");let _=[];for(let e=0;e<c;++e){let c=null!==a?a[e]:null,d=i[e],m=l[e],[h,f,M]=p(d,m,t,u);if(0===M.length){let[e,t]=c??m.dims.slice(-2),s=new o.Tensor("int32",new Int32Array(e*t).fill(-1),[e,t]);_.push({segmentation:s,segments_info:[]});continue}let[w,T]=g(h,f,M,s,r,n,c);_.push({segmentation:w,segments_info:T})}return _}function M(e,t=.5,s=null){throw new Error("`post_process_instance_segmentation` is not yet implemented.")}class w extends r.Callable{constructor(e){super(),this.image_mean=e.image_mean??e.mean,this.image_std=e.image_std??e.std,this.resample=e.resample??2,this.do_rescale=e.do_rescale??!0,this.rescale_factor=e.rescale_factor??1/255,this.do_normalize=e.do_normalize,this.do_thumbnail=e.do_thumbnail,this.size=e.size??e.image_size,this.do_resize=e.do_resize??void 0!==this.size,this.size_divisibility=e.size_divisibility??e.size_divisor,this.do_center_crop=e.do_center_crop,this.crop_size=e.crop_size,this.do_convert_rgb=e.do_convert_rgb??!0,this.do_crop_margin=e.do_crop_margin,this.pad_size=e.pad_size,this.do_pad=e.do_pad,this.min_pixels=e.min_pixels,this.max_pixels=e.max_pixels,this.do_pad&&!this.pad_size&&this.size&&void 0!==this.size.width&&void 0!==this.size.height&&(this.pad_size=this.size),this.do_flip_channel_order=e.do_flip_channel_order??!1,this.config=e}async thumbnail(e,t,s=2){const r=e.height,o=e.width,n=t.height,a=t.width;let i=Math.min(r,n),l=Math.min(o,a);return i===r&&l===o?e:(r>o?l=Math.floor(o*i/r):o>r&&(i=Math.floor(r*l/o)),await e.resize(l,i,{resample:s}))}async crop_margin(e,t=200){const s=e.clone().grayscale(),r=(0,n.min)(s.data)[0],o=(0,n.max)(s.data)[0]-r;if(0===o)return e;const a=t/255;let i=s.width,l=s.height,c=0,d=0;const u=s.data;for(let e=0;e<s.height;++e){const t=e*s.width;for(let n=0;n<s.width;++n)(u[t+n]-r)/o<a&&(i=Math.min(i,n),l=Math.min(l,e),c=Math.max(c,n),d=Math.max(d,e))}return e=await e.crop([i,l,c,d])}pad_image(e,t,s,{mode:r="constant",center:o=!1,constant_values:n=0}={}){const[i,l,c]=t;let d,u;if("number"==typeof s?(d=s,u=s):"square"===s?d=u=Math.max(i,l):(d=s.width,u=s.height),d!==l||u!==i){const s=new Float32Array(d*u*c);if(Array.isArray(n))for(let e=0;e<s.length;++e)s[e]=n[e%c];else 0!==n&&s.fill(n);const[_,m]=o?[Math.floor((d-l)/2),Math.floor((u-i)/2)]:[0,0];for(let t=0;t<i;++t){const r=(t+m)*d,o=t*l;for(let t=0;t<l;++t){const n=(r+t+_)*c,a=(o+t)*c;for(let t=0;t<c;++t)s[n+t]=e[a+t]}}if("symmetric"===r){if(o)throw new Error("`center` padding is not supported when `mode` is set to `symmetric`.");const t=i-1,r=l-1;for(let o=0;o<u;++o){const n=o*d,u=(0,a.calculateReflectOffset)(o,t)*l;for(let t=0;t<d;++t){if(o<i&&t<l)continue;const d=(n+t)*c,_=(u+(0,a.calculateReflectOffset)(t,r))*c;for(let t=0;t<c;++t)s[d+t]=e[_+t]}}}e=s,t=[u,d,c]}return[e,t]}rescale(e){for(let t=0;t<e.length;++t)e[t]=this.rescale_factor*e[t]}get_resize_output_image_size(e,t){const[s,r]=e.size;let o,n;if(this.do_thumbnail){const{height:e,width:s}=t;o=Math.min(e,s)}else Number.isInteger(t)?(o=t,n=this.config.max_size??o):void 0!==t&&(o=t.shortest_edge,n=t.longest_edge);if(void 0!==o||void 0!==n){const e=void 0===o?1:Math.max(o/s,o/r),t=s*e,a=r*e,i=void 0===n?1:Math.min(n/t,n/a);let l=Math.floor(Number((t*i).toFixed(2))),c=Math.floor(Number((a*i).toFixed(2)));return void 0!==this.size_divisibility&&([l,c]=d([l,c],this.size_divisibility)),[l,c]}if(void 0!==t&&void 0!==t.width&&void 0!==t.height){let e=t.width,o=t.height;if(this.config.keep_aspect_ratio&&this.config.ensure_multiple_of){let t=o/r,n=e/s;Math.abs(1-n)<Math.abs(1-t)?t=n:n=t,o=c(t*r,this.config.ensure_multiple_of),e=c(n*s,this.config.ensure_multiple_of)}return[e,o]}if(void 0!==this.size_divisibility)return d([s,r],this.size_divisibility);if(void 0!==this.min_pixels&&void 0!==this.max_pixels){return function(e,t,s=28,r=3136,o=1003520){if(e<s||t<s)throw new Error(`height:${e} or width:${t} must be larger than factor:${s}`);if(Math.max(e,t)/Math.min(e,t)>200)throw new Error("absolute aspect ratio must be smaller than 200, got "+Math.max(e,t)/Math.min(e,t));let n=Math.round(e/s)*s,a=Math.round(t/s)*s;if(n*a>o){const r=Math.sqrt(e*t/o);n=Math.floor(e/r/s)*s,a=Math.floor(t/r/s)*s}else if(n*a<r){const o=Math.sqrt(r/(e*t));n=Math.ceil(e*o/s)*s,a=Math.ceil(t*o/s)*s}return[n,a]}(r,s,this.config.patch_size*this.config.merge_size,this.min_pixels,this.max_pixels)}throw new Error(`Could not resize image due to unsupported \`this.size\` option in config: ${JSON.stringify(t)}`)}async resize(e){const[t,s]=this.get_resize_output_image_size(e,this.size);return await e.resize(t,s,{resample:this.resample})}async preprocess(e,{do_normalize:t=null,do_pad:s=null,do_convert_rgb:r=null,do_convert_grayscale:n=null,do_flip_channel_order:a=null}={}){this.do_crop_margin&&(e=await this.crop_margin(e));const[i,l]=e.size;if(r??this.do_convert_rgb?e=e.rgb():n&&(e=e.grayscale()),this.do_resize&&(e=await this.resize(e)),this.do_thumbnail&&(e=await this.thumbnail(e,this.size,this.resample)),this.do_center_crop){let t,s;Number.isInteger(this.crop_size)?(t=this.crop_size,s=this.crop_size):(t=this.crop_size.width,s=this.crop_size.height),e=await e.center_crop(t,s)}const c=[e.height,e.width];let u=Float32Array.from(e.data),_=[e.height,e.width,e.channels];if(this.do_rescale&&this.rescale(u),t??this.do_normalize){let t=this.image_mean;Array.isArray(this.image_mean)||(t=new Array(e.channels).fill(t));let s=this.image_std;if(Array.isArray(this.image_std)||(s=new Array(e.channels).fill(t)),t.length!==e.channels||s.length!==e.channels)throw new Error(`When set to arrays, the length of \`image_mean\` (${t.length}) and \`image_std\` (${s.length}) must match the number of channels in the image (${e.channels}).`);for(let r=0;r<u.length;r+=e.channels)for(let o=0;o<e.channels;++o)u[r+o]=(u[r+o]-t[o])/s[o]}if(s??this.do_pad)if(this.pad_size){const t=this.pad_image(u,[e.height,e.width,e.channels],this.pad_size);[u,_]=t}else if(this.size_divisibility){const[e,t]=d([_[1],_[0]],this.size_divisibility);[u,_]=this.pad_image(u,_,{width:e,height:t})}if(a??this.do_flip_channel_order){if(3!==_[2])throw new Error("Flipping channel order is only supported for RGB images.");for(let e=0;e<u.length;e+=3){const t=u[e];u[e]=u[e+2],u[e+2]=t}}return{original_size:[l,i],reshaped_input_size:c,pixel_values:new o.Tensor("float32",u,_).permute(2,0,1)}}async _call(e,...t){Array.isArray(e)||(e=[e]);const s=await Promise.all(e.map((e=>this.preprocess(e))));return{pixel_values:(0,o.stack)(s.map((e=>e.pixel_values)),0),original_sizes:s.map((e=>e.original_size)),reshaped_input_sizes:s.map((e=>e.reshaped_input_size))}}static async from_pretrained(e,t={}){return new this(await(0,i.getModelJSON)(e,l.IMAGE_PROCESSOR_NAME,!0,t))}}},"./src/base/processing_utils.js":(e,t,s)=>{s.r(t),s.d(t,{Processor:()=>a});var r=s("./src/utils/constants.js"),o=s("./src/utils/generic.js"),n=s("./src/utils/hub.js");class a extends o.Callable{static classes=["image_processor_class","tokenizer_class","feature_extractor_class"];static uses_processor_config=!1;static uses_chat_template_file=!1;constructor(e,t,s){super(),this.config=e,this.components=t,this.chat_template=s}get image_processor(){return this.components.image_processor}get tokenizer(){return this.components.tokenizer}get feature_extractor(){return this.components.feature_extractor}apply_chat_template(e,t={}){if(!this.tokenizer)throw new Error("Unable to apply chat template without a tokenizer.");return this.tokenizer.apply_chat_template(e,{tokenize:!1,chat_template:this.chat_template??void 0,...t})}batch_decode(...e){if(!this.tokenizer)throw new Error("Unable to decode without a tokenizer.");return this.tokenizer.batch_decode(...e)}decode(...e){if(!this.tokenizer)throw new Error("Unable to decode without a tokenizer.");return this.tokenizer.decode(...e)}async _call(e,...t){for(const s of[this.image_processor,this.feature_extractor,this.tokenizer])if(s)return s(e,...t);throw new Error("No image processor, feature extractor, or tokenizer found.")}static async from_pretrained(e,t={}){const[s,o,a]=await Promise.all([this.uses_processor_config?(0,n.getModelJSON)(e,r.PROCESSOR_NAME,!0,t):{},Promise.all(this.classes.filter((e=>e in this)).map((async s=>{const r=await this[s].from_pretrained(e,t);return[s.replace(/_class$/,""),r]}))).then(Object.fromEntries),this.uses_chat_template_file?(0,n.getModelText)(e,r.CHAT_TEMPLATE_NAME,!0,t):null]);return new this(s,o,a)}}},"./src/configs.js":(e,t,s)=>{s.r(t),s.d(t,{AutoConfig:()=>l,PretrainedConfig:()=>i,getCacheShapes:()=>a});var r=s("./src/utils/core.js"),o=s("./src/utils/hub.js");function n(e){const t={};let s={};switch(e.model_type){case"llava":case"paligemma":case"gemma3":case"florence2":case"llava_onevision":case"idefics3":case"ultravox":case"voxtral":case"smolvlm":case"gemma3n":s=n(e.text_config);break;case"moondream1":s=n(e.phi_config);break;case"musicgen":s=n(e.decoder);break;case"multi_modality":s=n(e.language_config);break;case"gpt2":case"gptj":case"jais":case"codegen":case"gpt_bigcode":t.num_heads="n_head",t.num_layers="n_layer",t.hidden_size="n_embd";break;case"gpt_neox":case"stablelm":case"opt":case"falcon":case"modernbert-decoder":t.num_heads="num_attention_heads",t.num_layers="num_hidden_layers",t.hidden_size="hidden_size";break;case"llama":case"arcee":case"lfm2":case"smollm3":case"olmo":case"olmo2":case"mobilellm":case"granite":case"cohere":case"mistral":case"starcoder2":case"qwen2":case"qwen2_vl":case"phi":case"phi3":case"phi3_v":case"llava_qwen2":t.num_heads="num_key_value_heads",t.num_layers="num_hidden_layers",t.hidden_size="hidden_size",t.num_attention_heads="num_attention_heads",t.dim_kv="head_dim";break;case"qwen3":case"gemma":case"gemma2":case"gemma3_text":case"gemma3n_text":case"glm":case"helium":case"ernie4_5":t.num_heads="num_key_value_heads",t.num_layers="num_hidden_layers",t.dim_kv="head_dim";break;case"openelm":t.num_heads="num_kv_heads",t.num_layers="num_transformer_layers",t.dim_kv="head_dim";break;case"gpt_neo":case"donut-swin":t.num_heads="num_heads",t.num_layers="num_layers",t.hidden_size="hidden_size";break;case"bloom":t.num_heads="n_head",t.num_layers="n_layer",t.hidden_size="hidden_size";break;case"mpt":t.num_heads="n_heads",t.num_layers="n_layers",t.hidden_size="d_model";break;case"exaone":t.num_heads="num_key_value_heads",t.num_layers="num_layers",t.dim_kv="head_dim",t.num_attention_heads="num_attention_heads";break;case"t5":case"mt5":case"longt5":t.num_decoder_layers="num_decoder_layers",t.num_decoder_heads="num_heads",t.decoder_dim_kv="d_kv",t.num_encoder_layers="num_layers",t.num_encoder_heads="num_heads",t.encoder_dim_kv="d_kv";break;case"bart":case"mbart":case"marian":case"whisper":case"lite-whisper":case"m2m_100":case"blenderbot":case"blenderbot-small":case"florence2_language":t.num_decoder_layers="decoder_layers",t.num_decoder_heads="decoder_attention_heads",t.decoder_hidden_size="d_model",t.num_encoder_layers="encoder_layers",t.num_encoder_heads="encoder_attention_heads",t.encoder_hidden_size="d_model";break;case"speecht5":t.num_decoder_layers="decoder_layers",t.num_decoder_heads="decoder_attention_heads",t.decoder_hidden_size="hidden_size",t.num_encoder_layers="encoder_layers",t.num_encoder_heads="encoder_attention_heads",t.encoder_hidden_size="hidden_size";break;case"trocr":t.num_encoder_layers=t.num_decoder_layers="decoder_layers",t.num_encoder_heads=t.num_decoder_heads="decoder_attention_heads",t.encoder_hidden_size=t.decoder_hidden_size="d_model";break;case"musicgen_decoder":t.num_encoder_layers=t.num_decoder_layers="num_hidden_layers",t.num_encoder_heads=t.num_decoder_heads="num_attention_heads",t.encoder_hidden_size=t.decoder_hidden_size="hidden_size";break;case"moonshine":t.num_decoder_layers="decoder_num_hidden_layers",t.num_decoder_heads="decoder_num_key_value_heads",t.num_encoder_layers="encoder_num_hidden_layers",t.num_encoder_heads="encoder_num_key_value_heads",t.encoder_hidden_size=t.decoder_hidden_size="hidden_size";break;case"vision-encoder-decoder":const o=n(e.decoder),a="num_decoder_layers"in o,i=(0,r.pick)(e,["model_type","is_encoder_decoder"]);return a?(i.num_decoder_layers=o.num_decoder_layers,i.num_decoder_heads=o.num_decoder_heads,i.decoder_hidden_size=o.decoder_hidden_size,i.num_encoder_layers=o.num_encoder_layers,i.num_encoder_heads=o.num_encoder_heads,i.encoder_hidden_size=o.encoder_hidden_size):(i.num_layers=o.num_layers,i.num_heads=o.num_heads,i.hidden_size=o.hidden_size),i}const o={...s,...(0,r.pick)(e,["model_type","multi_query","is_encoder_decoder"])};for(const s in t)o[s]=e[t[s]];return o}function a(e,t){if("lfm2"===e.model_type){const s=t?.prefix??"past_key_values",r="present"===s?"present":"past",o={},{layer_types:n,num_attention_heads:a,num_key_value_heads:i,hidden_size:l,conv_L_cache:c}=e,d=l/a,u=t?.batch_size??1;for(let e=0;e<n.length;++e)if("full_attention"===n[e])for(const t of["key","value"])o[`${s}.${e}.${t}`]=[u,i,0,d];else{if("conv"!==n[e])throw new Error(`Unsupported layer type: ${n[e]}`);o[`${r}_conv.${e}`]=[u,l,c]}return o}return function(e,{prefix:t="past_key_values",batch_size:s=1}={}){const r={},o=e.normalized_config;if(o.is_encoder_decoder&&"num_encoder_heads"in o&&"num_decoder_heads"in o){const e=o.encoder_dim_kv??o.encoder_hidden_size/o.num_encoder_heads,n=o.decoder_dim_kv??o.decoder_hidden_size/o.num_decoder_heads,a=[s,o.num_encoder_heads,0,e],i=[s,o.num_decoder_heads,0,n];for(let e=0;e<o.num_decoder_layers;++e)r[`${t}.${e}.encoder.key`]=a,r[`${t}.${e}.encoder.value`]=a,r[`${t}.${e}.decoder.key`]=i,r[`${t}.${e}.decoder.value`]=i}else{const e=o.num_heads,n=o.num_layers,a=o.dim_kv??o.hidden_size/(o.num_attention_heads??e);if("falcon"===o.model_type){const o=[s*e,0,a];for(let e=0;e<n;++e)r[`${t}.${e}.key`]=o,r[`${t}.${e}.value`]=o}else if(o.multi_query){const o=[s*e,0,2*a];for(let e=0;e<n;++e)r[`${t}.${e}.key_value`]=o}else if("bloom"===o.model_type){const o=[s*e,a,0],i=[s*e,0,a];for(let e=0;e<n;++e)r[`${t}.${e}.key`]=o,r[`${t}.${e}.value`]=i}else if("openelm"===o.model_type)for(let o=0;o<n;++o){const n=[s,e[o],0,a];r[`${t}.${o}.key`]=n,r[`${t}.${o}.value`]=n}else{const o=[s,e,0,a];for(let e=0;e<n;++e)r[`${t}.${e}.key`]=o,r[`${t}.${e}.value`]=o}}return r}(e,t)}class i{model_type=null;is_encoder_decoder=!1;max_position_embeddings;"transformers.js_config";constructor(e){Object.assign(this,e),this.normalized_config=n(this)}static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:r=null,local_files_only:n=!1,revision:a="main"}={}){!s||s instanceof i||(s=new i(s));const l=s??await async function(e,t){return await(0,o.getModelJSON)(e,"config.json",!0,t)}(e,{progress_callback:t,config:s,cache_dir:r,local_files_only:n,revision:a});return new this(l)}}class l{static async from_pretrained(...e){return i.from_pretrained(...e)}}},"./src/env.js":(e,t,s)=>{s.r(t),s.d(t,{apis:()=>g,env:()=>b});var r=s("?db59"),o=s("?383f"),n=s("?fa4b");const a="undefined"!=typeof window&&void 0!==window.document,i="undefined"!=typeof self&&["DedicatedWorkerGlobalScope","ServiceWorkerGlobalScope","SharedWorkerGlobalScope"].includes(self.constructor?.name),l="undefined"!=typeof self&&"caches"in self,c="undefined"!=typeof navigator&&"gpu"in navigator,d="undefined"!=typeof navigator&&"ml"in navigator,u="undefined"!=typeof process,_=u&&"node"===process?.release?.name,m=!x(r),p=!x(o),h=void 0!==globalThis.Deno,g=(globalThis.Bun,Object.freeze({IS_BROWSER_ENV:a,IS_WEBWORKER_ENV:i,IS_WEB_CACHE_AVAILABLE:l,IS_WEBGPU_AVAILABLE:c,IS_WEBNN_AVAILABLE:d,IS_PROCESS_AVAILABLE:u,IS_NODE_ENV:_,IS_FS_AVAILABLE:m,IS_PATH_AVAILABLE:p})),f=m&&p;let M="./";if(f){const e=Object(import.meta).url;e?M=o.dirname(o.dirname(n.fileURLToPath(e))):"undefined"!=typeof __dirname&&(M=o.dirname(__dirname))}const w=f?o.join(M,"/.cache/"):null,T="/models/",b={version:"3.7.2",backends:{onnx:{}},allowRemoteModels:!0,remoteHost:"https://huggingface.co/",remotePathTemplate:"{model}/resolve/{revision}/",allowLocalModels:!(a||i),localModelPath:f?o.join(M,T):T,useFS:m,useBrowserCache:l&&!h,useFSCache:m,cacheDir:w,useCustomCache:!1,customCache:null};function x(e){return 0===Object.keys(e).length}},"./src/generation/configuration_utils.js":(e,t,s)=>{s.r(t),s.d(t,{GenerationConfig:()=>o});var r=s("./src/utils/core.js");class o{max_length=20;max_new_tokens=null;min_length=0;min_new_tokens=null;early_stopping=!1;max_time=null;do_sample=!1;num_beams=1;num_beam_groups=1;penalty_alpha=null;use_cache=!0;temperature=1;top_k=50;top_p=1;typical_p=1;epsilon_cutoff=0;eta_cutoff=0;diversity_penalty=0;repetition_penalty=1;encoder_repetition_penalty=1;length_penalty=1;no_repeat_ngram_size=0;bad_words_ids=null;force_words_ids=null;renormalize_logits=!1;constraints=null;forced_bos_token_id=null;forced_eos_token_id=null;remove_invalid_values=!1;exponential_decay_length_penalty=null;suppress_tokens=null;streamer=null;begin_suppress_tokens=null;forced_decoder_ids=null;guidance_scale=null;num_return_sequences=1;output_attentions=!1;output_hidden_states=!1;output_scores=!1;return_dict_in_generate=!1;pad_token_id=null;bos_token_id=null;eos_token_id=null;encoder_no_repeat_ngram_size=0;decoder_start_token_id=null;generation_kwargs={};constructor(e){Object.assign(this,(0,r.pick)(e,Object.getOwnPropertyNames(this)))}}},"./src/generation/logits_process.js":(e,t,s)=>{s.r(t),s.d(t,{ClassifierFreeGuidanceLogitsProcessor:()=>f,ForcedBOSTokenLogitsProcessor:()=>l,ForcedEOSTokenLogitsProcessor:()=>c,LogitsProcessor:()=>n,LogitsProcessorList:()=>i,LogitsWarper:()=>a,MinLengthLogitsProcessor:()=>p,MinNewTokensLengthLogitsProcessor:()=>h,NoBadWordsLogitsProcessor:()=>g,NoRepeatNGramLogitsProcessor:()=>_,RepetitionPenaltyLogitsProcessor:()=>m,SuppressTokensAtBeginLogitsProcessor:()=>d,TemperatureLogitsWarper:()=>M,TopKLogitsWarper:()=>T,TopPLogitsWarper:()=>w,WhisperTimeStampLogitsProcessor:()=>u});var r=s("./src/utils/generic.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/maths.js"));class n extends r.Callable{_call(e,t){throw Error("`_call` should be implemented in a subclass")}}class a extends r.Callable{_call(e,t){throw Error("`_call` should be implemented in a subclass")}}class i extends r.Callable{constructor(){super(),this.processors=[]}push(e){this.processors.push(e)}extend(e){this.processors.push(...e)}_call(e,t){let s=t;for(const t of this.processors)s=t(e,s);return s}[Symbol.iterator](){return this.processors.values()}}class l extends n{constructor(e){super(),this.bos_token_id=e}_call(e,t){for(let s=0;s<e.length;++s)if(1===e[s].length){const e=t[s].data;e.fill(-1/0),e[this.bos_token_id]=0}return t}}class c extends n{constructor(e,t){super(),this.max_length=e,this.eos_token_id=Array.isArray(t)?t:[t]}_call(e,t){for(let s=0;s<e.length;++s)if(e[s].length===this.max_length-1){const e=t[s].data;e.fill(-1/0);for(const t of this.eos_token_id)e[t]=0}return t}}class d extends n{constructor(e,t){super(),this.begin_suppress_tokens=e,this.begin_index=t}_call(e,t){for(let s=0;s<e.length;++s)if(e[s].length===this.begin_index){const e=t[s].data;for(const t of this.begin_suppress_tokens)e[t]=-1/0}return t}}class u extends n{constructor(e,t){super(),this.eos_token_id=Array.isArray(e.eos_token_id)?e.eos_token_id[0]:e.eos_token_id,this.no_timestamps_token_id=e.no_timestamps_token_id,this.timestamp_begin=this.no_timestamps_token_id+1,this.begin_index=t.length,t.at(-1)===this.no_timestamps_token_id&&(this.begin_index-=1),this.max_initial_timestamp_index=e.max_initial_timestamp_index}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data;if(r[this.no_timestamps_token_id]=-1/0,e[s].length===this.begin_index-1){r.fill(-1/0),r[this.timestamp_begin]=0;continue}const n=e[s].slice(this.begin_index),a=n.length>=1&&n[n.length-1]>=this.timestamp_begin,i=n.length<2||n[n.length-2]>=this.timestamp_begin;if(a&&(i?r.subarray(this.timestamp_begin).fill(-1/0):r.subarray(0,this.eos_token_id).fill(-1/0)),e[s].length===this.begin_index&&null!==this.max_initial_timestamp_index){const e=this.timestamp_begin+this.max_initial_timestamp_index;r.subarray(e+1).fill(-1/0)}const l=(0,o.log_softmax)(r);Math.log(l.subarray(this.timestamp_begin).map(Math.exp).reduce(((e,t)=>e+t)))>(0,o.max)(l.subarray(0,this.timestamp_begin))[0]&&r.subarray(0,this.timestamp_begin).fill(-1/0)}return t}}class _ extends n{constructor(e){super(),this.no_repeat_ngram_size=e}getNgrams(e){const t=e.length,s=[];for(let r=0;r<t+1-this.no_repeat_ngram_size;++r){const t=[];for(let s=0;s<this.no_repeat_ngram_size;++s)t.push(e[r+s]);s.push(t.map(Number))}const r=new Map;for(const e of s){const t=e.slice(0,e.length-1),s=JSON.stringify(t),o=r.get(s)??[];o.push(e[e.length-1]),r.set(s,o)}return r}getGeneratedNgrams(e,t){const s=t.slice(t.length+1-this.no_repeat_ngram_size,t.length);return e.get(JSON.stringify(s.map(Number)))??[]}calcBannedNgramTokens(e){const t=[];if(e.length+1<this.no_repeat_ngram_size)return t;{const t=this.getNgrams(e);return this.getGeneratedNgrams(t,e)}}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data,o=this.calcBannedNgramTokens(e[s]);for(const e of o)r[e]=-1/0}return t}}class m extends n{constructor(e){super(),this.penalty=e}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data;for(const t of new Set(e[s])){const e=Number(t);r[e]<0?r[e]*=this.penalty:r[e]/=this.penalty}}return t}}class p extends n{constructor(e,t){super(),this.min_length=e,this.eos_token_id=Array.isArray(t)?t:[t]}_call(e,t){for(let s=0;s<e.length;++s)if(e[s].length<this.min_length){const e=t[s].data;for(const t of this.eos_token_id)e[t]=-1/0}return t}}class h extends n{constructor(e,t,s){super(),this.prompt_length_to_skip=e,this.min_new_tokens=t,this.eos_token_id=Array.isArray(s)?s:[s]}_call(e,t){for(let s=0;s<e.length;++s){if(e[s].length-this.prompt_length_to_skip<this.min_new_tokens){const e=t[s].data;for(const t of this.eos_token_id)e[t]=-1/0}}return t}}class g extends n{constructor(e,t){super(),this.bad_words_ids=e,this.eos_token_id=Array.isArray(t)?t:[t]}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data,o=e[s];for(const e of this.bad_words_ids){if(o.length<e.length-1)continue;let t=!0;for(let s=1;s<=e.length-1;++s)if(e.at(-s-1)!=o.at(-s)){t=!1;break}t&&(r[e.at(-1)]=-1/0)}}return t}}class f extends n{constructor(e){if(super(),e<=1)throw new Error(`Require guidance scale >1 to use the classifier free guidance processor, got guidance scale ${e}.`);this.guidance_scale=e}_call(e,t){if(t.dims[0]!==2*e.length)throw new Error(`Logits should have twice the batch size of the input ids, the first half of batches corresponding to the conditional inputs, and the second half of batches corresponding to the unconditional inputs. Got batch size ${t.dims[0]} for the logits and ${e.length} for the input ids.`);const s=e.length,r=t.slice([0,s],null),o=t.slice([s,t.dims[0]],null);for(let e=0;e<o.data.length;++e)o.data[e]+=(r.data[e]-o.data[e])*this.guidance_scale;return o}}class M extends a{constructor(e){if(super(),"number"!=typeof e||e<=0){let t=`\`temperature\` (=${e}) must be a strictly positive float, otherwise your next token scores will be invalid.`;0===e&&(t+=" If you're looking for greedy decoding strategies, set `do_sample=false`.")}this.temperature=e}_call(e,t){const s=t.data;for(let e=0;e<s.length;++e)s[e]/=this.temperature;return t}}class w extends a{constructor(e,{filter_value:t=-1/0,min_tokens_to_keep:s=1}={}){if(super(),e<0||e>1)throw new Error(`\`top_p\` must be a float > 0 and < 1, but is ${e}`);if(!Number.isInteger(s)||s<1)throw new Error(`\`min_tokens_to_keep\` must be a positive integer, but is ${s}`);this.top_p=e,this.filter_value=t,this.min_tokens_to_keep=s}}class T extends a{constructor(e,{filter_value:t=-1/0,min_tokens_to_keep:s=1}={}){if(super(),!Number.isInteger(e)||e<0)throw new Error(`\`top_k\` must be a positive integer, but is ${e}`);this.top_k=Math.max(e,s),this.filter_value=t}}},"./src/generation/logits_sampler.js":(e,t,s)=>{s.r(t),s.d(t,{LogitsSampler:()=>a});var r=s("./src/utils/generic.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/maths.js");s("./src/generation/configuration_utils.js");class a extends r.Callable{constructor(e){super(),this.generation_config=e}async _call(e){return this.sample(e)}async sample(e){throw Error("sample should be implemented in subclasses.")}getLogits(e,t){let s=e.dims.at(-1),r=e.data;if(-1===t)r=r.slice(-s);else{let e=t*s;r=r.slice(e,e+s)}return r}randomSelect(e){let t=0;for(let s=0;s<e.length;++s)t+=e[s];let s=Math.random()*t;for(let t=0;t<e.length;++t)if(s-=e[t],s<=0)return t;return 0}static getSampler(e){if(e.do_sample)return new l(e);if(e.num_beams>1)return new c(e);if(e.num_return_sequences>1)throw Error(`num_return_sequences has to be 1 when doing greedy search, but is ${e.num_return_sequences}.`);return new i(e)}}class i extends a{async sample(e){const t=(0,n.max)(e.data)[1];return[[BigInt(t),0]]}}class l extends a{async sample(e){let t=e.dims.at(-1);this.generation_config.top_k>0&&(t=Math.min(this.generation_config.top_k,t));const[s,r]=await(0,o.topk)(e,t),a=(0,n.softmax)(s.data);return Array.from({length:this.generation_config.num_beams},(()=>{const e=this.randomSelect(a);return[r.data[e],Math.log(a[e])]}))}}class c extends a{async sample(e){let t=e.dims.at(-1);this.generation_config.top_k>0&&(t=Math.min(this.generation_config.top_k,t));const[s,r]=await(0,o.topk)(e,t),a=(0,n.softmax)(s.data);return Array.from({length:this.generation_config.num_beams},((e,t)=>[r.data[t],Math.log(a[t])]))}}},"./src/generation/stopping_criteria.js":(e,t,s)=>{s.r(t),s.d(t,{EosTokenCriteria:()=>i,InterruptableStoppingCriteria:()=>l,MaxLengthCriteria:()=>a,StoppingCriteria:()=>o,StoppingCriteriaList:()=>n});var r=s("./src/utils/generic.js");class o extends r.Callable{_call(e,t){throw Error("StoppingCriteria needs to be subclassed")}}class n extends r.Callable{constructor(){super(),this.criteria=[]}push(e){this.criteria.push(e)}extend(e){e instanceof n?e=e.criteria:e instanceof o&&(e=[e]),this.criteria.push(...e)}_call(e,t){const s=new Array(e.length).fill(!1);for(const r of this.criteria){const o=r(e,t);for(let e=0;e<s.length;++e)s[e]||=o[e]}return s}[Symbol.iterator](){return this.criteria.values()}}class a extends o{constructor(e,t=null){super(),this.max_length=e,this.max_position_embeddings=t}_call(e){return e.map((e=>e.length>=this.max_length))}}class i extends o{constructor(e){super(),Array.isArray(e)||(e=[e]),this.eos_token_id=e}_call(e,t){return e.map((e=>{const t=e.at(-1);return this.eos_token_id.some((e=>t==e))}))}}class l extends o{constructor(){super(),this.interrupted=!1}interrupt(){this.interrupted=!0}reset(){this.interrupted=!1}_call(e,t){return new Array(e.length).fill(this.interrupted)}}},"./src/generation/streamers.js":(e,t,s)=>{s.r(t),s.d(t,{BaseStreamer:()=>a,TextStreamer:()=>l,WhisperTextStreamer:()=>c});var r=s("./src/utils/core.js"),o=s("./src/tokenizers.js"),n=s("./src/env.js");class a{put(e){throw Error("Not implemented")}end(){throw Error("Not implemented")}}const i=n.apis.IS_PROCESS_AVAILABLE?e=>process.stdout.write(e):e=>console.log(e);class l extends a{constructor(e,{skip_prompt:t=!1,callback_function:s=null,token_callback_function:r=null,skip_special_tokens:o=!0,decode_kwargs:n={},...a}={}){super(),this.tokenizer=e,this.skip_prompt=t,this.callback_function=s??i,this.token_callback_function=r,this.decode_kwargs={skip_special_tokens:o,...n,...a},this.token_cache=[],this.print_len=0,this.next_tokens_are_prompt=!0}put(e){if(e.length>1)throw Error("TextStreamer only supports batch size of 1");const t=this.next_tokens_are_prompt;if(t&&(this.next_tokens_are_prompt=!1,this.skip_prompt))return;const s=e[0];this.token_callback_function?.(s),this.token_cache=(0,r.mergeArrays)(this.token_cache,s);const n=this.tokenizer.decode(this.token_cache,this.decode_kwargs);let a;t||n.endsWith("\n")?(a=n.slice(this.print_len),this.token_cache=[],this.print_len=0):n.length>0&&(0,o.is_chinese_char)(n.charCodeAt(n.length-1))?(a=n.slice(this.print_len),this.print_len+=a.length):(a=n.slice(this.print_len,n.lastIndexOf(" ")+1),this.print_len+=a.length),this.on_finalized_text(a,!1)}end(){let e;if(this.token_cache.length>0){e=this.tokenizer.decode(this.token_cache,this.decode_kwargs).slice(this.print_len),this.token_cache=[],this.print_len=0}else e="";this.next_tokens_are_prompt=!0,this.on_finalized_text(e,!0)}on_finalized_text(e,t){e.length>0&&this.callback_function?.(e),t&&this.callback_function===i&&n.apis.IS_PROCESS_AVAILABLE&&this.callback_function?.("\n")}}class c extends l{constructor(e,{skip_prompt:t=!1,callback_function:s=null,token_callback_function:r=null,on_chunk_start:o=null,on_chunk_end:n=null,on_finalize:a=null,time_precision:i=.02,skip_special_tokens:l=!0,decode_kwargs:c={}}={}){super(e,{skip_prompt:t,skip_special_tokens:l,callback_function:s,token_callback_function:r,decode_kwargs:c}),this.timestamp_begin=e.timestamp_begin,this.on_chunk_start=o,this.on_chunk_end=n,this.on_finalize=a,this.time_precision=i,this.waiting_for_timestamp=!1}put(e){if(e.length>1)throw Error("WhisperTextStreamer only supports batch size of 1");const t=e[0];if(1===t.length){const e=Number(t[0])-this.timestamp_begin;if(e>=0){const s=e*this.time_precision;return this.waiting_for_timestamp?this.on_chunk_end?.(s):this.on_chunk_start?.(s),this.waiting_for_timestamp=!this.waiting_for_timestamp,void this.token_callback_function?.(t)}}return super.put(e)}end(){super.end(),this.on_finalize?.()}}},"./src/models.js":(e,t,s)=>{s.r(t),s.d(t,{ASTForAudioClassification:()=>Is,ASTModel:()=>Ls,ASTPreTrainedModel:()=>Es,AlbertForMaskedLM:()=>Rt,AlbertForQuestionAnswering:()=>Gt,AlbertForSequenceClassification:()=>Bt,AlbertModel:()=>Ot,AlbertPreTrainedModel:()=>Nt,ArceeForCausalLM:()=>Hr,ArceeModel:()=>Xr,ArceePreTrainedModel:()=>Qr,AutoModel:()=>zd,AutoModelForAudioClassification:()=>Zd,AutoModelForAudioFrameClassification:()=>tu,AutoModelForAudioTextToText:()=>du,AutoModelForCTC:()=>Kd,AutoModelForCausalLM:()=>Gd,AutoModelForDepthEstimation:()=>nu,AutoModelForDocumentQuestionAnswering:()=>su,AutoModelForImageClassification:()=>Wd,AutoModelForImageFeatureExtraction:()=>lu,AutoModelForImageMatting:()=>ru,AutoModelForImageSegmentation:()=>Ud,AutoModelForImageTextToText:()=>cu,AutoModelForImageToImage:()=>ou,AutoModelForMaskGeneration:()=>Yd,AutoModelForMaskedLM:()=>Rd,AutoModelForNormalEstimation:()=>au,AutoModelForObjectDetection:()=>Hd,AutoModelForPoseEstimation:()=>iu,AutoModelForQuestionAnswering:()=>qd,AutoModelForSemanticSegmentation:()=>Qd,AutoModelForSeq2SeqLM:()=>Vd,AutoModelForSequenceClassification:()=>Dd,AutoModelForSpeechSeq2Seq:()=>Nd,AutoModelForTextToSpectrogram:()=>Od,AutoModelForTextToWaveform:()=>Bd,AutoModelForTokenClassification:()=>jd,AutoModelForUniversalSegmentation:()=>Xd,AutoModelForVision2Seq:()=>$d,AutoModelForXVector:()=>eu,AutoModelForZeroShotObjectDetection:()=>Jd,BartForConditionalGeneration:()=>es,BartForSequenceClassification:()=>ts,BartModel:()=>Zt,BartPretrainedModel:()=>Kt,BaseModelOutput:()=>ae,BeitForImageClassification:()=>Un,BeitModel:()=>Wn,BeitPreTrainedModel:()=>$n,BertForMaskedLM:()=>ce,BertForQuestionAnswering:()=>_e,BertForSequenceClassification:()=>de,BertForTokenClassification:()=>ue,BertModel:()=>le,BertPreTrainedModel:()=>ie,BlenderbotForConditionalGeneration:()=>cs,BlenderbotModel:()=>ls,BlenderbotPreTrainedModel:()=>is,BlenderbotSmallForConditionalGeneration:()=>_s,BlenderbotSmallModel:()=>us,BlenderbotSmallPreTrainedModel:()=>ds,BloomForCausalLM:()=>tn,BloomModel:()=>en,BloomPreTrainedModel:()=>Zo,CLIPModel:()=>nr,CLIPPreTrainedModel:()=>or,CLIPSegForImageSegmentation:()=>xr,CLIPSegModel:()=>br,CLIPSegPreTrainedModel:()=>Tr,CLIPTextModel:()=>ar,CLIPTextModelWithProjection:()=>ir,CLIPVisionModel:()=>lr,CLIPVisionModelWithProjection:()=>cr,CamembertForMaskedLM:()=>He,CamembertForQuestionAnswering:()=>Ke,CamembertForSequenceClassification:()=>Je,CamembertForTokenClassification:()=>Ye,CamembertModel:()=>Xe,CamembertPreTrainedModel:()=>Qe,CausalLMOutput:()=>fu,CausalLMOutputWithPast:()=>Mu,ChineseCLIPModel:()=>hr,ChineseCLIPPreTrainedModel:()=>pr,ClapAudioModelWithProjection:()=>Vl,ClapModel:()=>Dl,ClapPreTrainedModel:()=>zl,ClapTextModelWithProjection:()=>jl,CodeGenForCausalLM:()=>qr,CodeGenModel:()=>Rr,CodeGenPreTrainedModel:()=>Gr,CohereForCausalLM:()=>vo,CohereModel:()=>Fo,CoherePreTrainedModel:()=>ko,ConvBertForMaskedLM:()=>Ve,ConvBertForQuestionAnswering:()=>Be,ConvBertForSequenceClassification:()=>Ne,ConvBertForTokenClassification:()=>Oe,ConvBertModel:()=>je,ConvBertPreTrainedModel:()=>De,ConvNextForImageClassification:()=>ni,ConvNextModel:()=>oi,ConvNextPreTrainedModel:()=>ri,ConvNextV2ForImageClassification:()=>li,ConvNextV2Model:()=>ii,ConvNextV2PreTrainedModel:()=>ai,DFineForObjectDetection:()=>ma,DFineModel:()=>_a,DFinePreTrainedModel:()=>ua,DINOv3ConvNextModel:()=>Mi,DINOv3ConvNextPreTrainedModel:()=>fi,DINOv3ViTModel:()=>gi,DINOv3ViTPreTrainedModel:()=>hi,DPTForDepthEstimation:()=>ja,DPTModel:()=>Da,DPTPreTrainedModel:()=>za,DacDecoderModel:()=>qc,DacDecoderOutput:()=>Bc,DacEncoderModel:()=>Rc,DacEncoderOutput:()=>Oc,DacModel:()=>Gc,DacPreTrainedModel:()=>Nc,DebertaForMaskedLM:()=>tt,DebertaForQuestionAnswering:()=>ot,DebertaForSequenceClassification:()=>st,DebertaForTokenClassification:()=>rt,DebertaModel:()=>et,DebertaPreTrainedModel:()=>Ze,DebertaV2ForMaskedLM:()=>it,DebertaV2ForQuestionAnswering:()=>dt,DebertaV2ForSequenceClassification:()=>lt,DebertaV2ForTokenClassification:()=>ct,DebertaV2Model:()=>at,DebertaV2PreTrainedModel:()=>nt,DecisionTransformerModel:()=>fc,DecisionTransformerPreTrainedModel:()=>gc,DeiTForImageClassification:()=>Ta,DeiTModel:()=>wa,DeiTPreTrainedModel:()=>Ma,DepthAnythingForDepthEstimation:()=>Na,DepthAnythingPreTrainedModel:()=>Va,DepthProForDepthEstimation:()=>$a,DepthProPreTrainedModel:()=>qa,DetrForObjectDetection:()=>Hn,DetrForSegmentation:()=>Jn,DetrModel:()=>Xn,DetrObjectDetectionOutput:()=>Yn,DetrPreTrainedModel:()=>Qn,DetrSegmentationOutput:()=>Kn,Dinov2ForImageClassification:()=>ui,Dinov2Model:()=>di,Dinov2PreTrainedModel:()=>ci,Dinov2WithRegistersForImageClassification:()=>pi,Dinov2WithRegistersModel:()=>mi,Dinov2WithRegistersPreTrainedModel:()=>_i,DistilBertForMaskedLM:()=>gt,DistilBertForQuestionAnswering:()=>ht,DistilBertForSequenceClassification:()=>mt,DistilBertForTokenClassification:()=>pt,DistilBertModel:()=>_t,DistilBertPreTrainedModel:()=>ut,DonutSwinModel:()=>si,DonutSwinPreTrainedModel:()=>ti,EfficientNetForImageClassification:()=>Hl,EfficientNetModel:()=>Xl,EfficientNetPreTrainedModel:()=>Ql,ElectraForMaskedLM:()=>qe,ElectraForQuestionAnswering:()=>Ue,ElectraForSequenceClassification:()=>$e,ElectraForTokenClassification:()=>We,ElectraModel:()=>Re,ElectraPreTrainedModel:()=>Ge,Ernie4_5_ForCausalLM:()=>yl,Ernie4_5_Model:()=>vl,Ernie4_5_PretrainedModel:()=>Fl,EsmForMaskedLM:()=>wt,EsmForSequenceClassification:()=>Tt,EsmForTokenClassification:()=>bt,EsmModel:()=>Mt,EsmPreTrainedModel:()=>ft,ExaoneForCausalLM:()=>uo,ExaoneModel:()=>co,ExaonePreTrainedModel:()=>lo,FalconForCausalLM:()=>Il,FalconModel:()=>Ll,FalconPreTrainedModel:()=>El,FastViTForImageClassification:()=>Sn,FastViTModel:()=>Cn,FastViTPreTrainedModel:()=>yn,Florence2ForConditionalGeneration:()=>Qs,Florence2PreTrainedModel:()=>Us,GLPNForDepthEstimation:()=>ei,GLPNModel:()=>Za,GLPNPreTrainedModel:()=>Ka,GPT2LMHeadModel:()=>Fr,GPT2Model:()=>kr,GPT2PreTrainedModel:()=>Pr,GPTBigCodeForCausalLM:()=>Br,GPTBigCodeModel:()=>Or,GPTBigCodePreTrainedModel:()=>Nr,GPTJForCausalLM:()=>Vr,GPTJModel:()=>jr,GPTJPreTrainedModel:()=>Dr,GPTNeoForCausalLM:()=>Er,GPTNeoModel:()=>Ar,GPTNeoPreTrainedModel:()=>Sr,GPTNeoXForCausalLM:()=>zr,GPTNeoXModel:()=>Ir,GPTNeoXPreTrainedModel:()=>Lr,Gemma2ForCausalLM:()=>Lo,Gemma2Model:()=>Eo,Gemma2PreTrainedModel:()=>Ao,Gemma3ForCausalLM:()=>Do,Gemma3Model:()=>zo,Gemma3PreTrainedModel:()=>Io,Gemma3nForConditionalGeneration:()=>Ks,Gemma3nPreTrainedModel:()=>Ys,GemmaForCausalLM:()=>So,GemmaModel:()=>Co,GemmaPreTrainedModel:()=>yo,GlmForCausalLM:()=>io,GlmModel:()=>ao,GlmPreTrainedModel:()=>no,GraniteForCausalLM:()=>Po,GraniteModel:()=>xo,GranitePreTrainedModel:()=>bo,GroundingDinoForObjectDetection:()=>Ti,GroundingDinoPreTrainedModel:()=>wi,GroupViTModel:()=>vn,GroupViTPreTrainedModel:()=>Fn,HeliumForCausalLM:()=>oo,HeliumModel:()=>ro,HeliumPreTrainedModel:()=>so,HieraForImageClassification:()=>Pa,HieraModel:()=>xa,HieraPreTrainedModel:()=>ba,HubertForCTC:()=>nl,HubertForSequenceClassification:()=>al,HubertModel:()=>ol,HubertPreTrainedModel:()=>rl,IJepaForImageClassification:()=>pn,IJepaModel:()=>mn,IJepaPreTrainedModel:()=>_n,Idefics3ForConditionalGeneration:()=>er,Idefics3PreTrainedModel:()=>Zs,ImageMattingOutput:()=>wu,JAISLMHeadModel:()=>Cr,JAISModel:()=>yr,JAISPreTrainedModel:()=>vr,JinaCLIPModel:()=>fr,JinaCLIPPreTrainedModel:()=>gr,JinaCLIPTextModel:()=>Mr,JinaCLIPVisionModel:()=>wr,Lfm2ForCausalLM:()=>Kr,Lfm2Model:()=>Yr,Lfm2PreTrainedModel:()=>Jr,LiteWhisperForConditionalGeneration:()=>Vs,LlamaForCausalLM:()=>Ur,LlamaModel:()=>Wr,LlamaPreTrainedModel:()=>$r,LlavaForConditionalGeneration:()=>qs,LlavaOnevisionForConditionalGeneration:()=>$s,LlavaPreTrainedModel:()=>Rs,LlavaQwen2ForCausalLM:()=>Js,LongT5ForConditionalGeneration:()=>Xt,LongT5Model:()=>Qt,LongT5PreTrainedModel:()=>Ut,M2M100ForConditionalGeneration:()=>Ii,M2M100Model:()=>Li,M2M100PreTrainedModel:()=>Ei,MBartForCausalLM:()=>as,MBartForConditionalGeneration:()=>os,MBartForSequenceClassification:()=>ns,MBartModel:()=>rs,MBartPreTrainedModel:()=>ss,MPNetForMaskedLM:()=>St,MPNetForQuestionAnswering:()=>Lt,MPNetForSequenceClassification:()=>At,MPNetForTokenClassification:()=>Et,MPNetModel:()=>Ct,MPNetPreTrainedModel:()=>yt,MT5ForConditionalGeneration:()=>Yt,MT5Model:()=>Jt,MT5PreTrainedModel:()=>Ht,MarianMTModel:()=>Ai,MarianModel:()=>Si,MarianPreTrainedModel:()=>Ci,MaskFormerForInstanceSegmentation:()=>Ya,MaskFormerModel:()=>Ja,MaskFormerPreTrainedModel:()=>Ha,MaskedLMOutput:()=>hu,Metric3DForDepthEstimation:()=>Ua,Metric3DPreTrainedModel:()=>Wa,Metric3Dv2ForDepthEstimation:()=>Xa,Metric3Dv2PreTrainedModel:()=>Qa,MgpstrForSceneTextRecognition:()=>xc,MgpstrModelOutput:()=>Tc,MgpstrPreTrainedModel:()=>bc,MimiDecoderModel:()=>Vc,MimiDecoderOutput:()=>zc,MimiEncoderModel:()=>jc,MimiEncoderOutput:()=>Ic,MimiModel:()=>Dc,MimiPreTrainedModel:()=>Lc,MistralForCausalLM:()=>kl,MistralModel:()=>Pl,MistralPreTrainedModel:()=>xl,MobileBertForMaskedLM:()=>kt,MobileBertForQuestionAnswering:()=>vt,MobileBertForSequenceClassification:()=>Ft,MobileBertModel:()=>Pt,MobileBertPreTrainedModel:()=>xt,MobileLLMForCausalLM:()=>po,MobileLLMModel:()=>mo,MobileLLMPreTrainedModel:()=>_o,MobileNetV1ForImageClassification:()=>sc,MobileNetV1ForSemanticSegmentation:()=>rc,MobileNetV1Model:()=>tc,MobileNetV1PreTrainedModel:()=>ec,MobileNetV2ForImageClassification:()=>ac,MobileNetV2ForSemanticSegmentation:()=>ic,MobileNetV2Model:()=>nc,MobileNetV2PreTrainedModel:()=>oc,MobileNetV3ForImageClassification:()=>dc,MobileNetV3ForSemanticSegmentation:()=>uc,MobileNetV3Model:()=>cc,MobileNetV3PreTrainedModel:()=>lc,MobileNetV4ForImageClassification:()=>pc,MobileNetV4ForSemanticSegmentation:()=>hc,MobileNetV4Model:()=>mc,MobileNetV4PreTrainedModel:()=>_c,MobileViTForImageClassification:()=>zn,MobileViTModel:()=>In,MobileViTPreTrainedModel:()=>Ln,MobileViTV2ForImageClassification:()=>Vn,MobileViTV2Model:()=>jn,MobileViTV2PreTrainedModel:()=>Dn,ModelOutput:()=>ne,ModernBertDecoderForCausalLM:()=>ve,ModernBertDecoderModel:()=>Fe,ModernBertDecoderPreTrainedModel:()=>ke,ModernBertForMaskedLM:()=>be,ModernBertForSequenceClassification:()=>xe,ModernBertForTokenClassification:()=>Pe,ModernBertModel:()=>Te,ModernBertPreTrainedModel:()=>we,Moondream1ForConditionalGeneration:()=>Ws,MoonshineForConditionalGeneration:()=>Bs,MoonshineModel:()=>Os,MoonshinePreTrainedModel:()=>Ns,MptForCausalLM:()=>on,MptModel:()=>rn,MptPreTrainedModel:()=>sn,MultiModalityCausalLM:()=>wc,MultiModalityPreTrainedModel:()=>Mc,MusicgenForCausalLM:()=>Kl,MusicgenForConditionalGeneration:()=>Zl,MusicgenModel:()=>Yl,MusicgenPreTrainedModel:()=>Jl,NeoBertForMaskedLM:()=>he,NeoBertForQuestionAnswering:()=>Me,NeoBertForSequenceClassification:()=>ge,NeoBertForTokenClassification:()=>fe,NeoBertModel:()=>pe,NeoBertPreTrainedModel:()=>me,NomicBertModel:()=>Ce,NomicBertPreTrainedModel:()=>ye,OPTForCausalLM:()=>ln,OPTModel:()=>an,OPTPreTrainedModel:()=>nn,Olmo2ForCausalLM:()=>To,Olmo2Model:()=>wo,Olmo2PreTrainedModel:()=>Mo,OlmoForCausalLM:()=>fo,OlmoModel:()=>go,OlmoPreTrainedModel:()=>ho,OpenELMForCausalLM:()=>No,OpenELMModel:()=>Vo,OpenELMPreTrainedModel:()=>jo,OwlViTForObjectDetection:()=>Bn,OwlViTModel:()=>On,OwlViTPreTrainedModel:()=>Nn,Owlv2ForObjectDetection:()=>qn,Owlv2Model:()=>Rn,Owlv2PreTrainedModel:()=>Gn,PaliGemmaForConditionalGeneration:()=>Hs,PaliGemmaPreTrainedModel:()=>Xs,PatchTSMixerForPrediction:()=>Cc,PatchTSMixerModel:()=>yc,PatchTSMixerPreTrainedModel:()=>vc,PatchTSTForPrediction:()=>Fc,PatchTSTModel:()=>kc,PatchTSTPreTrainedModel:()=>Pc,Phi3ForCausalLM:()=>Ko,Phi3Model:()=>Yo,Phi3PreTrainedModel:()=>Jo,Phi3VForCausalLM:()=>rr,Phi3VPreTrainedModel:()=>sr,PhiForCausalLM:()=>Ho,PhiModel:()=>Xo,PhiPreTrainedModel:()=>Qo,PreTrainedModel:()=>oe,PretrainedMixin:()=>Xc,PvtForImageClassification:()=>wn,PvtModel:()=>Mn,PvtPreTrainedModel:()=>fn,PyAnnoteForAudioFrameClassification:()=>Gi,PyAnnoteModel:()=>Bi,PyAnnotePreTrainedModel:()=>Oi,QuestionAnsweringModelOutput:()=>gu,Qwen2ForCausalLM:()=>Go,Qwen2Model:()=>Bo,Qwen2PreTrainedModel:()=>Oo,Qwen2VLForConditionalGeneration:()=>Uo,Qwen2VLPreTrainedModel:()=>Wo,Qwen3ForCausalLM:()=>$o,Qwen3Model:()=>qo,Qwen3PreTrainedModel:()=>Ro,RFDetrForObjectDetection:()=>ca,RFDetrModel:()=>la,RFDetrObjectDetectionOutput:()=>da,RFDetrPreTrainedModel:()=>ia,RTDetrForObjectDetection:()=>ta,RTDetrModel:()=>ea,RTDetrObjectDetectionOutput:()=>sa,RTDetrPreTrainedModel:()=>Zn,RTDetrV2ForObjectDetection:()=>na,RTDetrV2Model:()=>oa,RTDetrV2ObjectDetectionOutput:()=>aa,RTDetrV2PreTrainedModel:()=>ra,ResNetForImageClassification:()=>va,ResNetModel:()=>Fa,ResNetPreTrainedModel:()=>ka,RoFormerForMaskedLM:()=>Ee,RoFormerForQuestionAnswering:()=>ze,RoFormerForSequenceClassification:()=>Le,RoFormerForTokenClassification:()=>Ie,RoFormerModel:()=>Ae,RoFormerPreTrainedModel:()=>Se,RobertaForMaskedLM:()=>hs,RobertaForQuestionAnswering:()=>Ms,RobertaForSequenceClassification:()=>gs,RobertaForTokenClassification:()=>fs,RobertaModel:()=>ps,RobertaPreTrainedModel:()=>ms,SamImageSegmentationOutput:()=>yi,SamModel:()=>vi,SamPreTrainedModel:()=>Fi,SapiensForDepthEstimation:()=>Ga,SapiensForNormalEstimation:()=>Ra,SapiensForSemanticSegmentation:()=>Ba,SapiensPreTrainedModel:()=>Oa,SegformerForImageClassification:()=>Rl,SegformerForSemanticSegmentation:()=>ql,SegformerModel:()=>Gl,SegformerPreTrainedModel:()=>Bl,Seq2SeqLMOutput:()=>uu,SequenceClassifierOutput:()=>_u,SiglipModel:()=>ur,SiglipPreTrainedModel:()=>dr,SiglipTextModel:()=>_r,SiglipVisionModel:()=>mr,SmolLM3ForCausalLM:()=>to,SmolLM3Model:()=>eo,SmolLM3PreTrainedModel:()=>Zr,SmolVLMForConditionalGeneration:()=>tr,SnacDecoderModel:()=>Qc,SnacEncoderModel:()=>Uc,SnacModel:()=>Wc,SnacPreTrainedModel:()=>$c,SpeechT5ForSpeechToText:()=>fl,SpeechT5ForTextToSpeech:()=>Ml,SpeechT5HifiGan:()=>wl,SpeechT5Model:()=>gl,SpeechT5PreTrainedModel:()=>hl,SqueezeBertForMaskedLM:()=>Dt,SqueezeBertForQuestionAnswering:()=>Vt,SqueezeBertForSequenceClassification:()=>jt,SqueezeBertModel:()=>zt,SqueezeBertPreTrainedModel:()=>It,StableLmForCausalLM:()=>Ul,StableLmModel:()=>Wl,StableLmPreTrainedModel:()=>$l,Starcoder2ForCausalLM:()=>Al,Starcoder2Model:()=>Sl,Starcoder2PreTrainedModel:()=>Cl,StyleTextToSpeech2Model:()=>pl,StyleTextToSpeech2PreTrainedModel:()=>ml,Swin2SRForImageSuperResolution:()=>Ia,Swin2SRModel:()=>La,Swin2SRPreTrainedModel:()=>Ea,SwinForImageClassification:()=>Sa,SwinForSemanticSegmentation:()=>Aa,SwinModel:()=>Ca,SwinPreTrainedModel:()=>ya,T5ForConditionalGeneration:()=>Wt,T5Model:()=>$t,T5PreTrainedModel:()=>qt,TableTransformerForObjectDetection:()=>ga,TableTransformerModel:()=>ha,TableTransformerObjectDetectionOutput:()=>fa,TableTransformerPreTrainedModel:()=>pa,TokenClassifierOutput:()=>pu,TrOCRForCausalLM:()=>bl,TrOCRPreTrainedModel:()=>Tl,UltravoxModel:()=>Ac,UltravoxPreTrainedModel:()=>Sc,UniSpeechForCTC:()=>Ui,UniSpeechForSequenceClassification:()=>Qi,UniSpeechModel:()=>Wi,UniSpeechPreTrainedModel:()=>$i,UniSpeechSatForAudioFrameClassification:()=>Ki,UniSpeechSatForCTC:()=>Ji,UniSpeechSatForSequenceClassification:()=>Yi,UniSpeechSatModel:()=>Hi,UniSpeechSatPreTrainedModel:()=>Xi,ViTForImageClassification:()=>un,ViTMAEModel:()=>bn,ViTMAEPreTrainedModel:()=>Tn,ViTMSNForImageClassification:()=>kn,ViTMSNModel:()=>Pn,ViTMSNPreTrainedModel:()=>xn,ViTModel:()=>dn,ViTPreTrainedModel:()=>cn,VisionEncoderDecoderModel:()=>Gs,VitMatteForImageMatting:()=>En,VitMattePreTrainedModel:()=>An,VitPoseForPoseEstimation:()=>gn,VitPosePreTrainedModel:()=>hn,VitsModel:()=>Ol,VitsModelOutput:()=>Tu,VitsPreTrainedModel:()=>Nl,VoxtralForConditionalGeneration:()=>Ec,Wav2Vec2BertForCTC:()=>tl,Wav2Vec2BertForSequenceClassification:()=>sl,Wav2Vec2BertModel:()=>el,Wav2Vec2BertPreTrainedModel:()=>Zi,Wav2Vec2ForAudioFrameClassification:()=>Ni,Wav2Vec2ForCTC:()=>ji,Wav2Vec2ForSequenceClassification:()=>Vi,Wav2Vec2Model:()=>Di,Wav2Vec2PreTrainedModel:()=>zi,WavLMForAudioFrameClassification:()=>_l,WavLMForCTC:()=>cl,WavLMForSequenceClassification:()=>dl,WavLMForXVector:()=>ul,WavLMModel:()=>ll,WavLMPreTrainedModel:()=>il,WeSpeakerResNetModel:()=>qi,WeSpeakerResNetPreTrainedModel:()=>Ri,WhisperForConditionalGeneration:()=>js,WhisperModel:()=>Ds,WhisperPreTrainedModel:()=>zs,XLMForQuestionAnswering:()=>ks,XLMForSequenceClassification:()=>xs,XLMForTokenClassification:()=>Ps,XLMModel:()=>Ts,XLMPreTrainedModel:()=>ws,XLMRobertaForMaskedLM:()=>ys,XLMRobertaForQuestionAnswering:()=>As,XLMRobertaForSequenceClassification:()=>Cs,XLMRobertaForTokenClassification:()=>Ss,XLMRobertaModel:()=>vs,XLMRobertaPreTrainedModel:()=>Fs,XLMWithLMHeadModel:()=>bs,XVectorOutput:()=>mu,YolosForObjectDetection:()=>Pi,YolosModel:()=>xi,YolosObjectDetectionOutput:()=>ki,YolosPreTrainedModel:()=>bi});var r=s("./src/configs.js"),o=s("./src/backends/onnx.js"),n=s("./src/utils/dtypes.js"),a=s("./src/utils/generic.js"),i=s("./src/utils/core.js"),l=s("./src/utils/hub.js"),c=s("./src/utils/constants.js"),d=s("./src/generation/logits_process.js"),u=s("./src/generation/configuration_utils.js"),_=s("./src/utils/tensor.js"),m=s("./src/utils/image.js"),p=s("./src/utils/maths.js"),h=s("./src/generation/stopping_criteria.js"),g=s("./src/generation/logits_sampler.js"),f=s("./src/env.js"),M=s("./src/models/whisper/generation_whisper.js"),w=s("./src/models/whisper/common_whisper.js");const T=0,b=1,x=2,P=3,k=4,F=5,v=6,y=7,C=8,S=9,A=10,E=11,L=12,I=new Map,z=new Map,D=new Map;async function j(e,t,s){return Object.fromEntries(await Promise.all(Object.keys(t).map((async a=>{const{buffer_or_path:i,session_options:c,session_config:d}=await async function(e,t,s){let a=s.config?.["transformers.js_config"]??{},i=s.device??a.device;i&&"string"!=typeof i&&(i.hasOwnProperty(t)?i=i[t]:(console.warn(`device not specified for "${t}". Using the default device.`),i=null));const c=i??(f.apis.IS_NODE_ENV?"cpu":"wasm"),d=(0,o.deviceToExecutionProviders)(c),u=a.device_config??{};u.hasOwnProperty(c)&&(a={...a,...u[c]});let _=s.dtype??a.dtype;if("string"!=typeof _&&(_&&_.hasOwnProperty(t)?_=_[t]:(_=n.DEFAULT_DEVICE_DTYPE_MAPPING[c]??n.DATA_TYPES.fp32,console.warn(`dtype not specified for "${t}". Using the default dtype (${_}) for this device (${c}).`))),_===n.DATA_TYPES.auto){let e=a.dtype;"string"!=typeof e&&(e=e?.[t]),_=e&&e!==n.DATA_TYPES.auto&&n.DATA_TYPES.hasOwnProperty(e)?e:n.DEFAULT_DEVICE_DTYPE_MAPPING[c]??n.DATA_TYPES.fp32}const m=_;if(!n.DEFAULT_DTYPE_SUFFIX_MAPPING.hasOwnProperty(m))throw new Error(`Invalid dtype: ${m}. Should be one of: ${Object.keys(n.DATA_TYPES).join(", ")}`);if(m===n.DATA_TYPES.fp16&&"webgpu"===c&&!await(0,n.isWebGpuFp16Supported)())throw new Error(`The device (${c}) does not support fp16.`);const p=a.kv_cache_dtype,h=p?"string"==typeof p?p:p[m]??"float32":void 0;if(h&&!["float32","float16"].includes(h))throw new Error(`Invalid kv_cache_dtype: ${h}. Should be one of: float32, float16`);const g={dtype:m,kv_cache_dtype:h,device:c},M=`${t}${n.DEFAULT_DTYPE_SUFFIX_MAPPING[m]}.onnx`,w=`${s.subfolder??""}/${M}`,T={...s.session_options};T.executionProviders??=d;const b=a.free_dimension_overrides;b?T.freeDimensionOverrides??=b:c.startsWith("webnn")&&!T.freeDimensionOverrides&&console.warn(`WebNN does not currently support dynamic shapes and requires 'free_dimension_overrides' to be set in config.json, preferably as a field within config["transformers.js_config"]["device_config"]["${c}"]. When 'free_dimension_overrides' is not set, you may experience significant performance degradation.`);const x=f.apis.IS_NODE_ENV&&f.env.useFSCache,P=(0,l.getModelFile)(e,w,!0,s,x),k=s.use_external_data_format??a.use_external_data_format;let F=[];if(k){let r;r="object"==typeof k?k.hasOwnProperty(M)?k[M]:!!k.hasOwnProperty(t)&&k[t]:k;const o=+r;if(o>l.MAX_EXTERNAL_DATA_CHUNKS)throw new Error(`The number of external data chunks (${o}) exceeds the maximum allowed value (${l.MAX_EXTERNAL_DATA_CHUNKS}).`);for(let t=0;t<o;++t){const r=`${M}_data${0===t?"":"_"+t}`,o=`${s.subfolder??""}/${r}`;F.push(new Promise((async(t,n)=>{const a=await(0,l.getModelFile)(e,o,!0,s,x);t(a instanceof Uint8Array?{path:r,data:a}:r)})))}}else void 0!==T.externalData&&(F=T.externalData.map((async t=>{if("string"==typeof t.data){const r=await(0,l.getModelFile)(e,t.data,!0,s);return{...t,data:r}}return t})));if(F.length>0){const e=await Promise.all(F);f.apis.IS_NODE_ENV||(T.externalData=e)}if("webgpu"===c){const e=(0,r.getCacheShapes)(s.config,{prefix:"present"});if(Object.keys(e).length>0&&!(0,o.isONNXProxy)()){const t={};for(const s in e)t[s]="gpu-buffer";T.preferredOutputLocation=t}}return{buffer_or_path:await P,session_options:T,session_config:g}}(e,t[a],s);return[a,await(0,o.createInferenceSession)(i,c,d)]}))))}async function V(e,t,s){return Object.fromEntries(await Promise.all(Object.keys(t).map((async r=>[r,await(0,l.getModelJSON)(e,t[r],!1,s)]))))}let N=Promise.resolve();async function O(e,t){const s=function(e,t){const s=Object.create(null),r=[];for(const n of e.inputNames){const e=t[n];e instanceof _.Tensor?s[n]=(0,o.isONNXProxy)()?e.clone():e:r.push(n)}if(r.length>0)throw new Error(`An error occurred during model execution: "Missing the following inputs: ${r.join(", ")}.`);const n=Object.keys(t).length,a=e.inputNames.length;if(n>a){let s=Object.keys(t).filter((t=>!e.inputNames.includes(t)));console.warn(`WARNING: Too many inputs were provided (${n} > ${a}). The following inputs will be ignored: "${s.join(", ")}".`)}return s}(e,t);try{const t=Object.fromEntries(Object.entries(s).map((([e,t])=>[e,t.ort_tensor]))),r=()=>e.run(t);return B(await(f.apis.IS_BROWSER_ENV||f.apis.IS_WEBWORKER_ENV?N=N.then(r):r()))}catch(e){const t=Object.fromEntries(Object.entries(s).map((([e,t])=>{const s={type:t.type,dims:t.dims,location:t.location};return"gpu-buffer"!==s.location&&(s.data=t.data),[e,s]})));throw console.error(`An error occurred during model execution: "${e}".`),console.error("Inputs given to model:",t),e}}function B(e){for(let t in e)(0,o.isONNXTensor)(e[t])?e[t]=new _.Tensor(e[t]):"object"==typeof e[t]&&B(e[t]);return e}function G(e){if(e instanceof _.Tensor)return e;if(0===e.length)throw Error("items must be non-empty");if(Array.isArray(e[0])){if(e.some((t=>t.length!==e[0].length)))throw Error("Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' and/or 'truncation=True' to have batched tensors with the same length.");return new _.Tensor("int64",BigInt64Array.from(e.flat().map((e=>BigInt(e)))),[e.length,e[0].length])}return new _.Tensor("int64",BigInt64Array.from(e.map((e=>BigInt(e)))),[1,e.length])}function R(e){return new _.Tensor("bool",[e],[1])}async function q(e,t){let{encoder_outputs:s,input_ids:r,decoder_input_ids:o,...n}=t;if(!s){const r=(0,i.pick)(t,e.sessions.model.inputNames);s=(await $(e,r)).last_hidden_state}n.input_ids=o,n.encoder_hidden_states=s,e.sessions.decoder_model_merged.inputNames.includes("encoder_attention_mask")&&(n.encoder_attention_mask=t.attention_mask);return await U(e,n,!0)}async function $(e,t){const s=e.sessions.model,r=(0,i.pick)(t,s.inputNames);if(s.inputNames.includes("inputs_embeds")&&!r.inputs_embeds){if(!t.input_ids)throw new Error("Both `input_ids` and `inputs_embeds` are missing in the model inputs.");r.inputs_embeds=await e.encode_text({input_ids:t.input_ids})}if(s.inputNames.includes("token_type_ids")&&!r.token_type_ids){if(!r.input_ids)throw new Error("Both `input_ids` and `token_type_ids` are missing in the model inputs.");r.token_type_ids=(0,_.zeros_like)(r.input_ids)}if(s.inputNames.includes("pixel_mask")&&!r.pixel_mask){if(!r.pixel_values)throw new Error("Both `pixel_values` and `pixel_mask` are missing in the model inputs.");const e=r.pixel_values.dims;r.pixel_mask=(0,_.ones)([e[0],e[2],e[3]])}return await O(s,r)}async function W(e,t){const s=await e.encode(t);return await e.decode(s)}async function U(e,t,s=!1){const r=e.sessions[s?"decoder_model_merged":"model"],{past_key_values:o,...n}=t;if(r.inputNames.includes("use_cache_branch")&&(n.use_cache_branch=R(!!o)),r.inputNames.includes("position_ids")&&n.attention_mask&&!n.position_ids){const t=["paligemma","gemma3_text","gemma3"].includes(e.config.model_type)?1:0;n.position_ids=function(e,t=null,s=0){const{input_ids:r,inputs_embeds:o,attention_mask:n}=e,{data:a,dims:i}=Z(n,s);let l=new _.Tensor("int64",a,i);if(t){const e=-(r??o).dims.at(1);l=l.slice(null,[e,null])}return l}(n,o,t)}e.addPastKeyValues(n,o);const a=(0,i.pick)(n,r.inputNames);return await O(r,a)}function Q({modality_token_id:e,inputs_embeds:t,modality_features:s,input_ids:r,attention_mask:o}){const n=r.tolist().map((t=>t.reduce(((t,s,r)=>(s==e&&t.push(r),t)),[]))),a=n.reduce(((e,t)=>e+t.length),0),i=s.dims[0];if(a!==i)throw new Error(`Number of tokens and features do not match: tokens: ${a}, features ${i}`);let l=0;for(let e=0;e<n.length;++e){const r=n[e],o=t[e];for(let e=0;e<r.length;++e)o[r[e]].data.set(s[l++].data)}return{inputs_embeds:t,attention_mask:o}}function X({image_token_id:e,inputs_embeds:t,image_features:s,input_ids:r,attention_mask:o}){return Q({modality_token_id:e,inputs_embeds:t,modality_features:s,input_ids:r,attention_mask:o})}function H({audio_token_id:e,inputs_embeds:t,audio_features:s,input_ids:r,attention_mask:o}){return Q({modality_token_id:e,inputs_embeds:t,modality_features:s,input_ids:r,attention_mask:o})}async function J(e,{encode_function:t,merge_function:s,modality_input_name:r,modality_output_name:o,input_ids:n=null,attention_mask:a=null,position_ids:i=null,inputs_embeds:l=null,past_key_values:c=null,generation_config:d=null,logits_processor:u=null,...m}){const p=m[r];if(!l)if(l=await e.encode_text({input_ids:n,...m}),p&&1!==n.dims[1]){const e=await t({[r]:p,...m});({inputs_embeds:l,attention_mask:a}=s({[o]:e,inputs_embeds:l,input_ids:n,attention_mask:a}))}else if(c&&p&&1===n.dims[1]){const e=n.dims[1],t=Object.values(c)[0].dims.at(-2);a=(0,_.cat)([(0,_.ones)([n.dims[0],t]),a.slice(null,[a.dims[1]-e,a.dims[1]])],1)}if(!i&&"qwen2_vl"===e.config.model_type){const{image_grid_thw:t,video_grid_thw:s}=m;[i]=e.get_rope_index(n,t,s,a)}return await U(e,{inputs_embeds:l,past_key_values:c,attention_mask:a,position_ids:i,generation_config:d,logits_processor:u},!0)}async function Y(e,t){return await J(e,{...t,modality_input_name:"audio_values",modality_output_name:"audio_features",encode_function:e.encode_audio.bind(e),merge_function:e._merge_input_ids_with_audio_features.bind(e)})}async function K(e,t){return await J(e,{...t,modality_input_name:"pixel_values",modality_output_name:"image_features",encode_function:e.encode_image.bind(e),merge_function:e._merge_input_ids_with_image_features.bind(e)})}function Z(e,t=0){const[s,r]=e.dims,o=e.data,n=new BigInt64Array(o.length);for(let e=0;e<s;++e){const s=e*r;let a=BigInt(t);for(let e=0;e<r;++e){const t=s+e;0n===o[t]?n[t]=BigInt(1):(n[t]=a,a+=o[t])}}return{data:n,dims:e.dims}}function ee(e,t,s,r){const o=s.past_key_values?Object.values(s.past_key_values)[0].dims.at(-2):0;if(!s.attention_mask){let e;for(const t of["input_ids","inputs_embeds","position_ids"])if(s[t]){e=s[t].dims;break}if(!e)throw new Error("attention_mask is not provided, and unable to infer its shape from model inputs.");s.attention_mask=(0,_.ones)([e[0],o+e[1]])}if(s.past_key_values){const{input_ids:e,attention_mask:t}=s;t&&t.dims[1]>e.dims[1]||o<e.dims[1]&&(s.input_ids=e.slice(null,[o,null]))}return s}function te(e,t,s,r){return s.past_key_values&&(t=t.map((e=>[e.at(-1)]))),{...s,decoder_input_ids:G(t)}}function se(e,...t){return e.config.is_encoder_decoder?te(e,...t):ee(e,...t)}function re(e,t,s,r){const o=!!s.past_key_values;if(null!==r.guidance_scale&&r.guidance_scale>1&&(o?s.input_ids=(0,_.cat)([s.input_ids,s.input_ids],0):(s.input_ids=(0,_.cat)([s.input_ids,(0,_.full_like)(s.input_ids,BigInt(r.pad_token_id))],0),s.attention_mask=(0,_.cat)([s.attention_mask,(0,_.full_like)(s.attention_mask,0n)],0))),!o&&s.pixel_values||(s.pixel_values=(0,_.full)([0,0,3,384,384],1)),o){const e=0,t=1,r=e>0?1:0,o=1;s.images_seq_mask=new _.Tensor("bool",new Array(e+t).fill(!0).fill(!1,0,t),[o,e+t]),s.images_emb_mask=new _.Tensor("bool",new Array(e).fill(!!r),[o,1,e])}return s}class oe extends a.Callable{main_input_name="input_ids";forward_params=["input_ids","attention_mask"];constructor(e,t,s){super(),this.config=e,this.sessions=t,this.configs=s;const r=D.get(this.constructor),o=I.get(r);switch(this.can_generate=!1,this._forward=null,this._prepare_inputs_for_generation=null,o){case k:this.can_generate=!0,this._forward=U,this._prepare_inputs_for_generation=ee;break;case x:case P:case y:this.can_generate=!0,this._forward=q,this._prepare_inputs_for_generation=te;break;case b:this._forward=q;break;case v:this.can_generate=!0,this._forward=K,this._prepare_inputs_for_generation=se;break;case A:this.can_generate=!0,this._forward=Y,this._prepare_inputs_for_generation=se;break;case S:case L:this.can_generate=!0,this._prepare_inputs_for_generation=se;break;case C:this.can_generate=!0,this._prepare_inputs_for_generation=re;break;case E:this._forward=W;break;default:this._forward=$}this.can_generate&&this.forward_params.push("past_key_values"),this.custom_config=this.config["transformers.js_config"]??{}}async dispose(){const e=[];for(const t of Object.values(this.sessions))t?.handler?.dispose&&e.push(t.handler.dispose());return await Promise.all(e)}static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:o=null,local_files_only:n=!1,revision:a="main",model_file_name:i=null,subfolder:l="onnx",device:d=null,dtype:u=null,use_external_data_format:_=null,session_options:m={}}={}){let p={progress_callback:t,config:s,cache_dir:o,local_files_only:n,revision:a,model_file_name:i,subfolder:l,device:d,dtype:u,use_external_data_format:_,session_options:m};const h=D.get(this),g=I.get(h);let f;if(s=p.config=await r.AutoConfig.from_pretrained(e,p),g===k)f=await Promise.all([j(e,{model:p.model_file_name??"model"},p),V(e,{generation_config:"generation_config.json"},p)]);else if(g===x||g===P)f=await Promise.all([j(e,{model:"encoder_model",decoder_model_merged:"decoder_model_merged"},p),V(e,{generation_config:"generation_config.json"},p)]);else if(g===F)f=await Promise.all([j(e,{model:"vision_encoder",prompt_encoder_mask_decoder:"prompt_encoder_mask_decoder"},p)]);else if(g===b)f=await Promise.all([j(e,{model:"encoder_model",decoder_model_merged:"decoder_model_merged"},p)]);else if(g===v){const t={embed_tokens:"embed_tokens",vision_encoder:"vision_encoder",decoder_model_merged:"decoder_model_merged"};s.is_encoder_decoder&&(t.model="encoder_model"),f=await Promise.all([j(e,t,p),V(e,{generation_config:"generation_config.json"},p)])}else if(g===A){const t={embed_tokens:"embed_tokens",audio_encoder:"audio_encoder",decoder_model_merged:"decoder_model_merged"};f=await Promise.all([j(e,t,p),V(e,{generation_config:"generation_config.json"},p)])}else if(g===L){const t={embed_tokens:"embed_tokens",audio_encoder:"audio_encoder",vision_encoder:"vision_encoder",decoder_model_merged:"decoder_model_merged"};f=await Promise.all([j(e,t,p),V(e,{generation_config:"generation_config.json"},p)])}else if(g===y)f=await Promise.all([j(e,{model:"text_encoder",decoder_model_merged:"decoder_model_merged",encodec_decode:"encodec_decode"},p),V(e,{generation_config:"generation_config.json"},p)]);else if(g===C)f=await Promise.all([j(e,{prepare_inputs_embeds:"prepare_inputs_embeds",model:"language_model",lm_head:"lm_head",gen_head:"gen_head",gen_img_embeds:"gen_img_embeds",image_decode:"image_decode"},p),V(e,{generation_config:"generation_config.json"},p)]);else if(g===S)f=await Promise.all([j(e,{prepare_inputs_embeds:"prepare_inputs_embeds",model:"model",vision_encoder:"vision_encoder"},p),V(e,{generation_config:"generation_config.json"},p)]);else if(g===E)f=await Promise.all([j(e,{encoder_model:"encoder_model",decoder_model:"decoder_model"},p)]);else{if(g!==T){const e=h??s?.model_type;"custom"!==e&&console.warn(`Model type for '${e}' not found, assuming encoder-only architecture. Please report this at ${c.GITHUB_ISSUE_URL}.`)}f=await Promise.all([j(e,{model:p.model_file_name??"model"},p)])}return new this(s,...f)}async _call(e){return await this.forward(e)}async forward(e){return await this._forward(this,e)}get generation_config(){return this.configs?.generation_config??null}_get_logits_warper(e){const t=new d.LogitsProcessorList;return null!==e.temperature&&1!==e.temperature&&t.push(new d.TemperatureLogitsWarper(e.temperature)),null!==e.top_k&&0!==e.top_k&&t.push(new d.TopKLogitsWarper(e.top_k)),null!==e.top_p&&e.top_p<1&&t.push(new d.TopPLogitsWarper(e.top_p)),t}_get_logits_processor(e,t,s=null){const r=new d.LogitsProcessorList;if(null!==e.repetition_penalty&&1!==e.repetition_penalty&&r.push(new d.RepetitionPenaltyLogitsProcessor(e.repetition_penalty)),null!==e.no_repeat_ngram_size&&e.no_repeat_ngram_size>0&&r.push(new d.NoRepeatNGramLogitsProcessor(e.no_repeat_ngram_size)),null!==e.bad_words_ids&&r.push(new d.NoBadWordsLogitsProcessor(e.bad_words_ids,e.eos_token_id)),null!==e.min_length&&null!==e.eos_token_id&&e.min_length>0&&r.push(new d.MinLengthLogitsProcessor(e.min_length,e.eos_token_id)),null!==e.min_new_tokens&&null!==e.eos_token_id&&e.min_new_tokens>0&&r.push(new d.MinNewTokensLengthLogitsProcessor(t,e.min_new_tokens,e.eos_token_id)),null!==e.forced_bos_token_id&&r.push(new d.ForcedBOSTokenLogitsProcessor(e.forced_bos_token_id)),null!==e.forced_eos_token_id&&r.push(new d.ForcedEOSTokenLogitsProcessor(e.max_length,e.forced_eos_token_id)),null!==e.begin_suppress_tokens){const s=t>1||null===e.forced_bos_token_id?t:t+1;r.push(new d.SuppressTokensAtBeginLogitsProcessor(e.begin_suppress_tokens,s))}return null!==e.guidance_scale&&e.guidance_scale>1&&r.push(new d.ClassifierFreeGuidanceLogitsProcessor(e.guidance_scale)),null!==s&&r.extend(s),r}_prepare_generation_config(e,t,s=u.GenerationConfig){const r={...this.config};for(const e of["decoder","generator","text_config"])e in r&&Object.assign(r,r[e]);const o=new s(r);return Object.assign(o,this.generation_config??{}),e&&Object.assign(o,e),t&&Object.assign(o,(0,i.pick)(t,Object.getOwnPropertyNames(o))),o}_get_stopping_criteria(e,t=null){const s=new h.StoppingCriteriaList;return null!==e.max_length&&s.push(new h.MaxLengthCriteria(e.max_length,this.config.max_position_embeddings??null)),null!==e.eos_token_id&&s.push(new h.EosTokenCriteria(e.eos_token_id)),t&&s.extend(t),s}_validate_model_class(){if(!this.can_generate){const e=[nd,cd,od,Zc],t=D.get(this.constructor),s=new Set,r=this.config.model_type;for(const t of e){const e=t.get(r);e&&s.add(e[0])}let o=`The current model class (${t}) is not compatible with \`.generate()\`, as it doesn't have a language model head.`;throw s.size>0&&(o+=` Please use the following class instead: ${[...s].join(", ")}`),Error(o)}}prepare_inputs_for_generation(...e){return this._prepare_inputs_for_generation(this,...e)}_update_model_kwargs_for_generation({generated_input_ids:e,outputs:t,model_inputs:s,is_encoder_decoder:r}){return s.past_key_values=this.getPastKeyValues(t,s.past_key_values),s.input_ids=new _.Tensor("int64",e.flat(),[e.length,1]),r||(s.attention_mask=(0,_.cat)([s.attention_mask,(0,_.ones)([s.attention_mask.dims[0],1])],1)),s.position_ids=null,s}_prepare_model_inputs({inputs:e,bos_token_id:t,model_kwargs:s}){const r=(0,i.pick)(s,this.forward_params),o=this.main_input_name;if(o in r){if(e)throw new Error("`inputs`: {inputs}` were passed alongside {input_name} which is not allowed. Make sure to either pass {inputs} or {input_name}=...")}else r[o]=e;return{inputs_tensor:r[o],model_inputs:r,model_input_name:o}}async _prepare_encoder_decoder_kwargs_for_generation({inputs_tensor:e,model_inputs:t,model_input_name:s,generation_config:r}){if(this.sessions.model.inputNames.includes("inputs_embeds")&&!t.inputs_embeds&&"_prepare_inputs_embeds"in this){const{input_ids:e,pixel_values:s,attention_mask:r,...o}=t,n=await this._prepare_inputs_embeds(t);t={...o,...(0,i.pick)(n,["inputs_embeds","attention_mask"])}}let{last_hidden_state:o}=await $(this,t);if(null!==r.guidance_scale&&r.guidance_scale>1)o=(0,_.cat)([o,(0,_.full_like)(o,0)],0),"attention_mask"in t&&(t.attention_mask=(0,_.cat)([t.attention_mask,(0,_.zeros_like)(t.attention_mask)],0));else if(t.decoder_input_ids){const e=G(t.decoder_input_ids).dims[0];if(e!==o.dims[0]){if(1!==o.dims[0])throw new Error(`The encoder outputs have a different batch size (${o.dims[0]}) than the decoder inputs (${e}).`);o=(0,_.cat)(Array.from({length:e},(()=>o)),0)}}return t.encoder_outputs=o,t}_prepare_decoder_input_ids_for_generation({batch_size:e,model_input_name:t,model_kwargs:s,decoder_start_token_id:r,bos_token_id:o,generation_config:n}){let{decoder_input_ids:a,...i}=s;if(!(a instanceof _.Tensor)){if(a)Array.isArray(a[0])||(a=Array.from({length:e},(()=>a)));else if(r??=o,"musicgen"===this.config.model_type)a=Array.from({length:e*this.config.decoder.num_codebooks},(()=>[r]));else if(Array.isArray(r)){if(r.length!==e)throw new Error(`\`decoder_start_token_id\` expcted to have length ${e} but got ${r.length}`);a=r}else a=Array.from({length:e},(()=>[r]));a=G(a)}return s.decoder_attention_mask=(0,_.ones_like)(a),{input_ids:a,model_inputs:i}}async generate({inputs:e=null,generation_config:t=null,logits_processor:s=null,stopping_criteria:r=null,streamer:o=null,...n}){this._validate_model_class(),t=this._prepare_generation_config(t,n);let{inputs_tensor:a,model_inputs:i,model_input_name:l}=this._prepare_model_inputs({inputs:e,model_kwargs:n});const c=this.config.is_encoder_decoder;let d;c&&("encoder_outputs"in i||(i=await this._prepare_encoder_decoder_kwargs_for_generation({inputs_tensor:a,model_inputs:i,model_input_name:l,generation_config:t}))),c?({input_ids:d,model_inputs:i}=this._prepare_decoder_input_ids_for_generation({batch_size:i[l].dims.at(0),model_input_name:l,model_kwargs:i,decoder_start_token_id:t.decoder_start_token_id,bos_token_id:t.bos_token_id,generation_config:t})):d=i[l];let u=d.dims.at(-1);null!==t.max_new_tokens&&(t.max_length=u+t.max_new_tokens);const m=this._get_logits_processor(t,u,s),p=this._get_stopping_criteria(t,r),h=i[l].dims.at(0),f=g.LogitsSampler.getSampler(t),M=new Array(h).fill(0),w=d.tolist();let T;o&&o.put(w);let b={};for(;;){if(i=this.prepare_inputs_for_generation(w,i,t),T=await this.forward(i),t.output_attentions&&t.return_dict_in_generate){const e=this.getAttentions(T);for(const t in e)t in b||(b[t]=[]),b[t].push(e[t])}const e=m(w,T.logits.slice(null,-1,null)),s=[];for(let t=0;t<e.dims.at(0);++t){const r=e[t],o=await f(r);for(const[e,r]of o){const o=BigInt(e);M[t]+=r,w[t].push(o),s.push([o]);break}}o&&o.put(s);if(p(w).every((e=>e)))break;i=this._update_model_kwargs_for_generation({generated_input_ids:s,outputs:T,model_inputs:i,is_encoder_decoder:c})}o&&o.end();const x=this.getPastKeyValues(T,i.past_key_values,!0),P=new _.Tensor("int64",w.flat(),[w.length,w[0].length]);if(t.return_dict_in_generate)return{sequences:P,past_key_values:x,...b};for(const e of Object.values(T))"gpu-buffer"===e.location&&e.dispose();return P}getPastKeyValues(e,t,s=!1){const r=Object.create(null);for(const o in e)if(o.startsWith("present")){const n=o.replace("present_conv","past_conv").replace("present","past_key_values"),a=o.includes("encoder");if(r[n]=a&&t?t[n]:e[o],t&&(!a||s)){const e=t[n];"gpu-buffer"===e.location&&e.dispose()}}return r}getAttentions(e){const t={};for(const s of["cross_attentions","encoder_attentions","decoder_attentions"])for(const r in e)r.startsWith(s)&&(s in t||(t[s]=[]),t[s].push(e[r]));return t}addPastKeyValues(e,t){if(t)Object.assign(e,t);else{const t=this.sessions.decoder_model_merged??this.sessions.model,s=(e[this.main_input_name]??e.attention_mask)?.dims?.[0]??1,o=t?.config?.kv_cache_dtype??"float32",n="float16"===o?_.DataTypeMap.float16:_.DataTypeMap.float32,a=(0,r.getCacheShapes)(this.config,{batch_size:s});for(const t in a){const s=a[t].reduce(((e,t)=>e*t),1);e[t]=new _.Tensor(o,new n(s),a[t])}}}async encode_image({pixel_values:e}){return(await O(this.sessions.vision_encoder,{pixel_values:e})).image_features}async encode_text({input_ids:e}){return(await O(this.sessions.embed_tokens,{input_ids:e})).inputs_embeds}async encode_audio({audio_values:e}){return(await O(this.sessions.audio_encoder,{audio_values:e})).audio_features}}class ne{}class ae extends ne{constructor({last_hidden_state:e,hidden_states:t=null,attentions:s=null}){super(),this.last_hidden_state=e,this.hidden_states=t,this.attentions=s}}class ie extends oe{}class le extends ie{}class ce extends ie{async _call(e){return new hu(await super._call(e))}}class de extends ie{async _call(e){return new _u(await super._call(e))}}class ue extends ie{async _call(e){return new pu(await super._call(e))}}class _e extends ie{async _call(e){return new gu(await super._call(e))}}class me extends oe{}class pe extends me{}class he extends me{async _call(e){return new hu(await super._call(e))}}class ge extends me{async _call(e){return new _u(await super._call(e))}}class fe extends me{async _call(e){return new pu(await super._call(e))}}class Me extends me{async _call(e){return new gu(await super._call(e))}}class we extends oe{}class Te extends we{}class be extends we{async _call(e){return new hu(await super._call(e))}}class xe extends we{async _call(e){return new _u(await super._call(e))}}class Pe extends we{async _call(e){return new pu(await super._call(e))}}class ke extends oe{}class Fe extends ke{}class ve extends ke{}class ye extends oe{}class Ce extends ye{}class Se extends oe{}class Ae extends Se{}class Ee extends Se{async _call(e){return new hu(await super._call(e))}}class Le extends Se{async _call(e){return new _u(await super._call(e))}}class Ie extends Se{async _call(e){return new pu(await super._call(e))}}class ze extends Se{async _call(e){return new gu(await super._call(e))}}class De extends oe{}class je extends De{}class Ve extends De{async _call(e){return new hu(await super._call(e))}}class Ne extends De{async _call(e){return new _u(await super._call(e))}}class Oe extends De{async _call(e){return new pu(await super._call(e))}}class Be extends De{async _call(e){return new gu(await super._call(e))}}class Ge extends oe{}class Re extends Ge{}class qe extends Ge{async _call(e){return new hu(await super._call(e))}}class $e extends Ge{async _call(e){return new _u(await super._call(e))}}class We extends Ge{async _call(e){return new pu(await super._call(e))}}class Ue extends Ge{async _call(e){return new gu(await super._call(e))}}class Qe extends oe{}class Xe extends Qe{}class He extends Qe{async _call(e){return new hu(await super._call(e))}}class Je extends Qe{async _call(e){return new _u(await super._call(e))}}class Ye extends Qe{async _call(e){return new pu(await super._call(e))}}class Ke extends Qe{async _call(e){return new gu(await super._call(e))}}class Ze extends oe{}class et extends Ze{}class tt extends Ze{async _call(e){return new hu(await super._call(e))}}class st extends Ze{async _call(e){return new _u(await super._call(e))}}class rt extends Ze{async _call(e){return new pu(await super._call(e))}}class ot extends Ze{async _call(e){return new gu(await super._call(e))}}class nt extends oe{}class at extends nt{}class it extends nt{async _call(e){return new hu(await super._call(e))}}class lt extends nt{async _call(e){return new _u(await super._call(e))}}class ct extends nt{async _call(e){return new pu(await super._call(e))}}class dt extends nt{async _call(e){return new gu(await super._call(e))}}class ut extends oe{}class _t extends ut{}class mt extends ut{async _call(e){return new _u(await super._call(e))}}class pt extends ut{async _call(e){return new pu(await super._call(e))}}class ht extends ut{async _call(e){return new gu(await super._call(e))}}class gt extends ut{async _call(e){return new hu(await super._call(e))}}class ft extends oe{}class Mt extends ft{}class wt extends ft{async _call(e){return new hu(await super._call(e))}}class Tt extends ft{async _call(e){return new _u(await super._call(e))}}class bt extends ft{async _call(e){return new pu(await super._call(e))}}class xt extends oe{}class Pt extends xt{}class kt extends xt{async _call(e){return new hu(await super._call(e))}}class Ft extends xt{async _call(e){return new _u(await super._call(e))}}class vt extends xt{async _call(e){return new gu(await super._call(e))}}class yt extends oe{}class Ct extends yt{}class St extends yt{async _call(e){return new hu(await super._call(e))}}class At extends yt{async _call(e){return new _u(await super._call(e))}}class Et extends yt{async _call(e){return new pu(await super._call(e))}}class Lt extends yt{async _call(e){return new gu(await super._call(e))}}class It extends oe{}class zt extends It{}class Dt extends It{async _call(e){return new hu(await super._call(e))}}class jt extends It{async _call(e){return new _u(await super._call(e))}}class Vt extends It{async _call(e){return new gu(await super._call(e))}}class Nt extends oe{}class Ot extends Nt{}class Bt extends Nt{async _call(e){return new _u(await super._call(e))}}class Gt extends Nt{async _call(e){return new gu(await super._call(e))}}class Rt extends Nt{async _call(e){return new hu(await super._call(e))}}class qt extends oe{forward_params=["input_ids","attention_mask","encoder_outputs","decoder_input_ids","decoder_attention_mask","past_key_values"]}class $t extends qt{}class Wt extends qt{}class Ut extends oe{}class Qt extends Ut{}class Xt extends Ut{}class Ht extends oe{}class Jt extends Ht{}class Yt extends Ht{}class Kt extends oe{}class Zt extends Kt{}class es extends Kt{}class ts extends Kt{async _call(e){return new _u(await super._call(e))}}class ss extends oe{}class rs extends ss{}class os extends ss{}class ns extends ss{async _call(e){return new _u(await super._call(e))}}class as extends ss{}class is extends oe{}class ls extends is{}class cs extends is{}class ds extends oe{}class us extends ds{}class _s extends ds{}class ms extends oe{}class ps extends ms{}class hs extends ms{async _call(e){return new hu(await super._call(e))}}class gs extends ms{async _call(e){return new _u(await super._call(e))}}class fs extends ms{async _call(e){return new pu(await super._call(e))}}class Ms extends ms{async _call(e){return new gu(await super._call(e))}}class ws extends oe{}class Ts extends ws{}class bs extends ws{async _call(e){return new hu(await super._call(e))}}class xs extends ws{async _call(e){return new _u(await super._call(e))}}class Ps extends ws{async _call(e){return new pu(await super._call(e))}}class ks extends ws{async _call(e){return new gu(await super._call(e))}}class Fs extends oe{}class vs extends Fs{}class ys extends Fs{async _call(e){return new hu(await super._call(e))}}class Cs extends Fs{async _call(e){return new _u(await super._call(e))}}class Ss extends Fs{async _call(e){return new pu(await super._call(e))}}class As extends Fs{async _call(e){return new gu(await super._call(e))}}class Es extends oe{}class Ls extends Es{}class Is extends Es{}class zs extends oe{requires_attention_mask=!1;main_input_name="input_features";forward_params=["input_features","attention_mask","decoder_input_ids","decoder_attention_mask","past_key_values"]}class Ds extends zs{}class js extends zs{_prepare_generation_config(e,t){return super._prepare_generation_config(e,t,M.WhisperGenerationConfig)}_retrieve_init_tokens(e){const t=[e.decoder_start_token_id];let s=e.language;const r=e.task;if(e.is_multilingual){s||(console.warn("No language specified - defaulting to English (en)."),s="en");const o=`<|${(0,w.whisper_language_to_code)(s)}|>`;t.push(e.lang_to_id[o]),t.push(e.task_to_id[r??"transcribe"])}else if(s||r)throw new Error("Cannot specify `task` or `language` for an English-only model. If the model is intended to be multilingual, pass `is_multilingual=true` to generate, or update the generation config.");return!e.return_timestamps&&e.no_timestamps_token_id&&t.at(-1)!==e.no_timestamps_token_id?t.push(e.no_timestamps_token_id):e.return_timestamps&&t.at(-1)===e.no_timestamps_token_id&&(console.warn("<|notimestamps|> prompt token is removed from generation_config since `return_timestamps` is set to `true`."),t.pop()),t.filter((e=>null!=e))}async generate({inputs:e=null,generation_config:t=null,logits_processor:s=null,stopping_criteria:r=null,...o}){t=this._prepare_generation_config(t,o);const n=o.decoder_input_ids??this._retrieve_init_tokens(t);if(t.return_timestamps&&(s??=new d.LogitsProcessorList,s.push(new d.WhisperTimeStampLogitsProcessor(t,n))),t.begin_suppress_tokens&&(s??=new d.LogitsProcessorList,s.push(new d.SuppressTokensAtBeginLogitsProcessor(t.begin_suppress_tokens,n.length))),t.return_token_timestamps){if(!t.alignment_heads)throw new Error("Model generation config has no `alignment_heads`, token-level timestamps not available. See https://gist.github.com/hollance/42e32852f24243b748ae6bc1f985b13a on how to add this property to the generation config.");"translate"===t.task&&console.warn("Token-level timestamps may not be reliable for task 'translate'."),t.output_attentions=!0,t.return_dict_in_generate=!0}const a=await super.generate({inputs:e,generation_config:t,logits_processor:s,decoder_input_ids:n,...o});return t.return_token_timestamps&&(a.token_timestamps=this._extract_token_timestamps(a,t.alignment_heads,t.num_frames)),a}_extract_token_timestamps(e,t,s=null,r=.02){if(!e.cross_attentions)throw new Error("Model outputs must contain cross attentions to extract timestamps. This is most likely because the model was not exported with `output_attentions=True`.");null==s&&console.warn("`num_frames` has not been set, meaning the entire audio will be analyzed. This may lead to inaccurate token-level timestamps for short audios (< 30 seconds).");let o=this.config.median_filter_width;void 0===o&&(console.warn("Model config has no `median_filter_width`, using default value of 7."),o=7);const n=e.cross_attentions,a=Array.from({length:this.config.decoder_layers},((e,t)=>(0,_.cat)(n.map((e=>e[t])),2))),l=(0,_.stack)(t.map((([e,t])=>{if(e>=a.length)throw new Error(`Layer index ${e} is out of bounds for cross attentions (length ${a.length}).`);return s?a[e].slice(null,t,null,[0,s]):a[e].slice(null,t)}))).transpose(1,0,2,3),[c,d]=(0,_.std_mean)(l,-2,0,!0),u=l.clone();for(let e=0;e<u.dims[0];++e){const t=u[e];for(let s=0;s<t.dims[0];++s){const r=t[s],n=c[e][s][0].data,a=d[e][s][0].data;for(let e=0;e<r.dims[0];++e){let t=r[e].data;for(let e=0;e<t.length;++e)t[e]=(t[e]-a[e])/n[e];t.set((0,p.medianFilter)(t,o))}}}const m=[(0,_.mean)(u,1)],h=e.sequences.dims,g=new _.Tensor("float32",new Float32Array(h[0]*h[1]),h);for(let e=0;e<h[0];++e){const t=m[e].neg().squeeze_(0),[s,o]=(0,p.dynamic_time_warping)(t.tolist()),n=Array.from({length:s.length-1},((e,t)=>s[t+1]-s[t])),a=(0,i.mergeArrays)([1],n).map((e=>!!e)),l=[];for(let e=0;e<a.length;++e)a[e]&&l.push(o[e]*r);g[e].data.set(l,1)}return g}}class Vs extends js{}class Ns extends oe{requires_attention_mask=!1;main_input_name="input_values";forward_params=["input_values","decoder_input_ids","past_key_values"]}class Os extends Ns{}class Bs extends Ns{}class Gs extends oe{main_input_name="pixel_values";forward_params=["pixel_values","decoder_input_ids","encoder_hidden_states","past_key_values"]}class Rs extends oe{forward_params=["input_ids","attention_mask","pixel_values","position_ids","past_key_values"]}class qs extends Rs{_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_index,...e,image_features:s})}}class $s extends qs{}class Ws extends qs{}class Us extends oe{forward_params=["input_ids","inputs_embeds","attention_mask","pixel_values","encoder_outputs","decoder_input_ids","decoder_inputs_embeds","decoder_attention_mask","past_key_values"];main_input_name="inputs_embeds"}class Qs extends Us{_merge_input_ids_with_image_features({inputs_embeds:e,image_features:t,input_ids:s,attention_mask:r}){return{inputs_embeds:(0,_.cat)([t,e],1),attention_mask:(0,_.cat)([(0,_.ones)(t.dims.slice(0,2)),r],1)}}async _prepare_inputs_embeds({input_ids:e,pixel_values:t,inputs_embeds:s,attention_mask:r}){if(!e&&!t)throw new Error("Either `input_ids` or `pixel_values` should be provided.");let o,n;return e&&(o=await this.encode_text({input_ids:e})),t&&(n=await this.encode_image({pixel_values:t})),o&&n?({inputs_embeds:s,attention_mask:r}=this._merge_input_ids_with_image_features({inputs_embeds:o,image_features:n,input_ids:e,attention_mask:r})):s=o||n,{inputs_embeds:s,attention_mask:r}}async forward({input_ids:e,pixel_values:t,attention_mask:s,decoder_input_ids:r,decoder_attention_mask:o,encoder_outputs:n,past_key_values:a,inputs_embeds:i,decoder_inputs_embeds:l}){if(i||({inputs_embeds:i,attention_mask:s}=await this._prepare_inputs_embeds({input_ids:e,pixel_values:t,inputs_embeds:i,attention_mask:s})),!n){let{last_hidden_state:e}=await $(this,{inputs_embeds:i,attention_mask:s});n=e}if(!l){if(!r)throw new Error("Either `decoder_input_ids` or `decoder_inputs_embeds` should be provided.");l=await this.encode_text({input_ids:r})}const c={inputs_embeds:l,attention_mask:o,encoder_attention_mask:s,encoder_hidden_states:n,past_key_values:a};return await U(this,c,!0)}}class Xs extends oe{forward_params=["input_ids","attention_mask","pixel_values","position_ids","past_key_values"]}class Hs extends Xs{_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_index,...e,image_features:s})}}class Js extends Rs{_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_index,...e,image_features:s})}}class Ys extends oe{forward_params=["input_ids","attention_mask","inputs_embeds","per_layer_inputs","position_ids","pixel_values","input_features","input_features_mask","past_key_values"]}class Ks extends Ys{async forward({input_ids:e=null,attention_mask:t=null,pixel_values:s=null,input_features:r=null,input_features_mask:o=null,position_ids:n=null,inputs_embeds:a=null,per_layer_inputs:i=null,past_key_values:l=null,generation_config:c=null,logits_processor:d=null,...u}){if(!(a&&i||(({inputs_embeds:a,per_layer_inputs:i}=await O(this.sessions.embed_tokens,{input_ids:e})),1===e.dims[1]))){if(s){const{image_features:r}=await O(this.sessions.vision_encoder,{pixel_values:s});({inputs_embeds:a,attention_mask:t}=this._merge_input_ids_with_image_features({image_features:r,inputs_embeds:a,input_ids:e,attention_mask:t}))}if(r){const{audio_features:s}=await O(this.sessions.audio_encoder,{input_features:r,input_features_mask:o});({inputs_embeds:a,attention_mask:t}=this._merge_input_ids_with_audio_features({audio_features:s,inputs_embeds:a,input_ids:e,attention_mask:t}))}}return await U(this,{inputs_embeds:a,per_layer_inputs:i,past_key_values:l,attention_mask:t,position_ids:n,generation_config:c,logits_processor:d},!0)}_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_id,...e,image_features:s})}_merge_input_ids_with_audio_features(e){const t=e.audio_features.dims.at(-1),s=e.audio_features.view(-1,t);return H({audio_token_id:this.config.audio_token_id,...e,audio_features:s})}}class Zs extends oe{forward_params=["input_ids","attention_mask","pixel_values","pixel_attention_mask","position_ids","past_key_values"]}class er extends Zs{async encode_image({pixel_values:e,pixel_attention_mask:t}){return(await O(this.sessions.vision_encoder,{pixel_values:e,pixel_attention_mask:t})).image_features}_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_id,...e,image_features:s})}}class tr extends er{}class sr extends oe{forward_params=["input_ids","inputs_embeds","attention_mask","position_ids","pixel_values","image_sizes","past_key_values"]}class rr extends sr{async forward({input_ids:e=null,attention_mask:t=null,pixel_values:s=null,image_sizes:r=null,position_ids:o=null,inputs_embeds:n=null,past_key_values:a=null,generation_config:i=null,logits_processor:l=null,...c}){if(!n){let t;if(s&&1!==e.dims[1]){if(!r)throw new Error("`image_sizes` must be provided when `pixel_values` is provided.");({image_features:t}=await O(this.sessions.vision_encoder,{pixel_values:s,image_sizes:r}))}else{const e=this.config.normalized_config.hidden_size;t=new _.Tensor("float32",[],[0,e])}({inputs_embeds:n}=await O(this.sessions.prepare_inputs_embeds,{input_ids:e,image_features:t}))}return await U(this,{inputs_embeds:n,past_key_values:a,attention_mask:t,position_ids:o,generation_config:i,logits_processor:l},!1)}}class or extends oe{}class nr extends or{}class ar extends or{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class ir extends or{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class lr extends or{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class cr extends or{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class dr extends oe{}class ur extends dr{}class _r extends dr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class mr extends or{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class pr extends oe{}class hr extends pr{}class gr extends oe{}class fr extends gr{async forward(e){const t=!e.input_ids,s=!e.pixel_values;if(t&&s)throw new Error("Either `input_ids` or `pixel_values` should be provided.");if(t&&(e.input_ids=(0,_.ones)([e.pixel_values.dims[0],1])),s){const{image_size:t}=this.config.vision_config;e.pixel_values=(0,_.full)([0,3,t,t],0)}const{text_embeddings:r,image_embeddings:o,l2norm_text_embeddings:n,l2norm_image_embeddings:a}=await super.forward(e),i={};return t||(i.text_embeddings=r,i.l2norm_text_embeddings=n),s||(i.image_embeddings=o,i.l2norm_image_embeddings=a),i}}class Mr extends gr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class wr extends gr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class Tr extends oe{}class br extends Tr{}class xr extends Tr{}class Pr extends oe{}class kr extends Pr{}class Fr extends Pr{}class vr extends oe{}class yr extends vr{}class Cr extends vr{}class Sr extends oe{}class Ar extends Sr{}class Er extends Sr{}class Lr extends oe{}class Ir extends Lr{}class zr extends Lr{}class Dr extends oe{}class jr extends Dr{}class Vr extends Dr{}class Nr extends oe{}class Or extends Nr{}class Br extends Nr{}class Gr extends oe{}class Rr extends Gr{}class qr extends Gr{}class $r extends oe{}class Wr extends $r{}class Ur extends $r{}class Qr extends oe{}class Xr extends Qr{}class Hr extends Qr{}class Jr extends oe{}class Yr extends Jr{}class Kr extends Jr{}class Zr extends oe{}class eo extends Zr{}class to extends Zr{}class so extends oe{}class ro extends so{}class oo extends so{}class no extends oe{}class ao extends no{}class io extends no{}class lo extends oe{}class co extends lo{}class uo extends lo{}class _o extends oe{}class mo extends _o{}class po extends _o{}class ho extends oe{}class go extends ho{}class fo extends ho{}class Mo extends oe{}class wo extends Mo{}class To extends Mo{}class bo extends oe{}class xo extends bo{}class Po extends bo{}class ko extends oe{}class Fo extends ko{}class vo extends ko{}class yo extends oe{}class Co extends yo{}class So extends yo{}class Ao extends oe{}class Eo extends Ao{}class Lo extends Ao{}class Io extends oe{}class zo extends Io{}class Do extends Io{}class jo extends oe{}class Vo extends jo{}class No extends jo{}class Oo extends oe{}class Bo extends Oo{}class Go extends Oo{}class Ro extends oe{}class qo extends Ro{}class $o extends Ro{}class Wo extends oe{forward_params=["input_ids","attention_mask","position_ids","past_key_values","pixel_values","image_grid_thw"]}class Uo extends Wo{get_rope_index(e,t,s,r){const{vision_config:o,image_token_id:n,video_token_id:a,vision_start_token_id:i}=this.config,l=o.spatial_merge_size??2,c=[];if(t||s){let o=e.tolist();r||(r=(0,_.ones_like)(e));const d=r.tolist(),u=Array.from({length:3},(t=>Array.from({length:e.dims[0]},(t=>Array.from({length:e.dims[1]},(e=>1)))))),m=t?t.tolist():[],h=s?s.tolist():[];let g=0,f=0;for(let e=0;e<o.length;++e){const t=o[e].filter(((t,s)=>1==d[e][s])),s=t.reduce(((e,t,s)=>(t==i&&e.push(s),e)),[]).map((e=>t[e+1])),r=s.filter((e=>e==n)).length,_=s.filter((e=>e==a)).length;let M=[],w=0,T=r,b=_;for(let e=0;e<s.length;++e){const e=t.findIndex(((e,t)=>t>w&&e==n)),s=t.findIndex(((e,t)=>t>w&&e==a)),r=T>0&&-1!==e?e:t.length+1,o=b>0&&-1!==s?s:t.length+1;let i,c,d,u;r<o?([c,d,u]=m[g],++g,--T,i=r):([c,d,u]=h[f],++f,--b,i=o);const[_,x,P]=[Number(c),Math.floor(Number(d)/l),Math.floor(Number(u)/l)],k=i-w,F=M.length>0?(0,p.max)(M.at(-1))[0]+1:0;M.push(Array.from({length:3*k},((e,t)=>F+t%k)));const v=k+F,y=_*x*P,C=Array.from({length:y},((e,t)=>v+Math.floor(t/(x*P)))),S=Array.from({length:y},((e,t)=>v+Math.floor(t/P)%x)),A=Array.from({length:y},((e,t)=>v+t%P));M.push([C,S,A].flat()),w=i+y}if(w<t.length){const e=M.length>0?(0,p.max)(M.at(-1))[0]+1:0,s=t.length-w;M.push(Array.from({length:3*s},((t,r)=>e+r%s)))}const x=M.reduce(((e,t)=>e+t.length),0),P=new Array(x);let k=0;for(let e=0;e<3;++e)for(let t=0;t<M.length;++t){const s=M[t],r=s.length/3;for(let t=e*r;t<(e+1)*r;++t)P[k++]=s[t]}let F=0;const v=d[e];for(let t=0;t<v.length;++t)if(1==v[t]){for(let s=0;s<3;++s)u[s][e][t]=P[s*x/3+F];++F}const y=(0,p.max)(P)[0];c.push(y+1-o[e].length)}return[new _.Tensor("int64",u.flat(1/0),[3,e.dims[0],e.dims[1]]),new _.Tensor("int64",c,[c.length,1])]}if(r){const{data:e,dims:t}=Z(r),s=BigInt64Array.from({length:3*e.length},((t,s)=>e[s%e.length])),o=Array.from({length:t[0]},((s,r)=>(0,p.max)(e.subarray(t[1]*r,t[1]*(r+1)))[0]+1n+BigInt(t[1])));return[new _.Tensor("int64",s,[3,...t]),new _.Tensor("int64",o,[o.length,1])]}{const[t,s]=e.dims,r=BigInt64Array.from({length:3*t*s},((e,r)=>BigInt(Math.floor(r%s/t))));return[new _.Tensor("int64",r,[3,...e.dims]),(0,_.zeros)([t,1])]}}async encode_image({pixel_values:e,image_grid_thw:t}){return(await O(this.sessions.vision_encoder,{pixel_values:e,grid_thw:t})).image_features}_merge_input_ids_with_image_features(e){return X({image_token_id:this.config.image_token_id,...e})}prepare_inputs_for_generation(e,t,s){if(t.attention_mask&&!t.position_ids)if(t.past_key_values){t.pixel_values=null;const e=BigInt(Object.values(t.past_key_values)[0].dims.at(-2)),s=t.rope_deltas.map((t=>e+t));t.position_ids=(0,_.stack)([s,s,s],0)}else[t.position_ids,t.rope_deltas]=this.get_rope_index(t.input_ids,t.image_grid_thw,t.video_grid_thw,t.attention_mask);return t}}class Qo extends oe{}class Xo extends Qo{}class Ho extends Qo{}class Jo extends oe{}class Yo extends Jo{}class Ko extends Jo{}class Zo extends oe{}class en extends Zo{}class tn extends Zo{}class sn extends oe{}class rn extends sn{}class on extends sn{}class nn extends oe{}class an extends nn{}class ln extends nn{}class cn extends oe{}class dn extends cn{}class un extends cn{async _call(e){return new _u(await super._call(e))}}class _n extends oe{}class mn extends _n{}class pn extends _n{async _call(e){return new _u(await super._call(e))}}class hn extends oe{}class gn extends hn{}class fn extends oe{}class Mn extends fn{}class wn extends fn{async _call(e){return new _u(await super._call(e))}}class Tn extends oe{}class bn extends Tn{}class xn extends oe{}class Pn extends xn{}class kn extends xn{async _call(e){return new _u(await super._call(e))}}class Fn extends oe{}class vn extends Fn{}class yn extends oe{}class Cn extends yn{}class Sn extends yn{async _call(e){return new _u(await super._call(e))}}class An extends oe{}class En extends An{async _call(e){return new wu(await super._call(e))}}class Ln extends oe{}class In extends Ln{}class zn extends Ln{async _call(e){return new _u(await super._call(e))}}class Dn extends oe{}class jn extends Dn{}class Vn extends Dn{async _call(e){return new _u(await super._call(e))}}class Nn extends oe{}class On extends Nn{}class Bn extends Nn{}class Gn extends oe{}class Rn extends Gn{}class qn extends Gn{}class $n extends oe{}class Wn extends $n{}class Un extends $n{async _call(e){return new _u(await super._call(e))}}class Qn extends oe{}class Xn extends Qn{}class Hn extends Qn{async _call(e){return new Yn(await super._call(e))}}class Jn extends Qn{async _call(e){return new Kn(await super._call(e))}}class Yn extends ne{constructor({logits:e,pred_boxes:t}){super(),this.logits=e,this.pred_boxes=t}}class Kn extends ne{constructor({logits:e,pred_boxes:t,pred_masks:s}){super(),this.logits=e,this.pred_boxes=t,this.pred_masks=s}}class Zn extends oe{}class ea extends Zn{}class ta extends Zn{async _call(e){return new sa(await super._call(e))}}class sa extends ne{constructor({logits:e,pred_boxes:t}){super(),this.logits=e,this.pred_boxes=t}}class ra extends oe{}class oa extends ra{}class na extends ra{async _call(e){return new aa(await super._call(e))}}class aa extends sa{}class ia extends oe{}class la extends ia{}class ca extends ia{async _call(e){return new da(await super._call(e))}}class da extends sa{}class ua extends oe{}class _a extends ua{}class ma extends ua{async _call(e){return new sa(await super._call(e))}}class pa extends oe{}class ha extends pa{}class ga extends pa{async _call(e){return new fa(await super._call(e))}}class fa extends Yn{}class Ma extends oe{}class wa extends Ma{}class Ta extends Ma{async _call(e){return new _u(await super._call(e))}}class ba extends oe{}class xa extends ba{}class Pa extends ba{async _call(e){return new _u(await super._call(e))}}class ka extends oe{}class Fa extends ka{}class va extends ka{async _call(e){return new _u(await super._call(e))}}class ya extends oe{}class Ca extends ya{}class Sa extends ya{async _call(e){return new _u(await super._call(e))}}class Aa extends ya{}class Ea extends oe{}class La extends Ea{}class Ia extends Ea{}class za extends oe{}class Da extends za{}class ja extends za{}class Va extends oe{}class Na extends Va{}class Oa extends oe{}class Ba extends Oa{}class Ga extends Oa{}class Ra extends Oa{}class qa extends oe{}class $a extends qa{}class Wa extends oe{}class Ua extends Wa{}class Qa extends oe{}class Xa extends Qa{}class Ha extends oe{}class Ja extends Ha{}class Ya extends Ha{}class Ka extends oe{}class Za extends Ka{}class ei extends Ka{}class ti extends oe{}class si extends ti{}class ri extends oe{}class oi extends ri{}class ni extends ri{async _call(e){return new _u(await super._call(e))}}class ai extends oe{}class ii extends ai{}class li extends ai{async _call(e){return new _u(await super._call(e))}}class ci extends oe{}class di extends ci{}class ui extends ci{async _call(e){return new _u(await super._call(e))}}class _i extends oe{}class mi extends _i{}class pi extends _i{async _call(e){return new _u(await super._call(e))}}class hi extends oe{}class gi extends hi{}class fi extends oe{}class Mi extends fi{}class wi extends oe{}class Ti extends wi{}class bi extends oe{}class xi extends bi{}class Pi extends bi{async _call(e){return new ki(await super._call(e))}}class ki extends ne{constructor({logits:e,pred_boxes:t}){super(),this.logits=e,this.pred_boxes=t}}class Fi extends oe{}class vi extends Fi{async get_image_embeddings({pixel_values:e}){return await $(this,{pixel_values:e})}async forward(e){if(e.image_embeddings&&e.image_positional_embeddings||(e={...e,...await this.get_image_embeddings(e)}),!e.input_labels&&e.input_points){const t=e.input_points.dims.slice(0,-1),s=t.reduce(((e,t)=>e*t),1);e.input_labels=new _.Tensor("int64",new BigInt64Array(s).fill(1n),t)}const t={image_embeddings:e.image_embeddings,image_positional_embeddings:e.image_positional_embeddings};return e.input_points&&(t.input_points=e.input_points),e.input_labels&&(t.input_labels=e.input_labels),e.input_boxes&&(t.input_boxes=e.input_boxes),await O(this.sessions.prompt_encoder_mask_decoder,t)}async _call(e){return new yi(await super._call(e))}}class yi extends ne{constructor({iou_scores:e,pred_masks:t}){super(),this.iou_scores=e,this.pred_masks=t}}class Ci extends oe{}class Si extends Ci{}class Ai extends Ci{}class Ei extends oe{}class Li extends Ei{}class Ii extends Ei{}class zi extends oe{}class Di extends zi{}class ji extends zi{async _call(e){return new fu(await super._call(e))}}class Vi extends zi{async _call(e){return new _u(await super._call(e))}}class Ni extends zi{async _call(e){return new pu(await super._call(e))}}class Oi extends oe{}class Bi extends Oi{}class Gi extends Oi{async _call(e){return new pu(await super._call(e))}}class Ri extends oe{}class qi extends Ri{}class $i extends oe{}class Wi extends $i{}class Ui extends $i{async _call(e){return new fu(await super._call(e))}}class Qi extends $i{async _call(e){return new _u(await super._call(e))}}class Xi extends oe{}class Hi extends Xi{}class Ji extends Xi{async _call(e){return new fu(await super._call(e))}}class Yi extends Xi{async _call(e){return new _u(await super._call(e))}}class Ki extends Xi{async _call(e){return new pu(await super._call(e))}}class Zi extends oe{}class el extends Zi{}class tl extends Zi{async _call(e){return new fu(await super._call(e))}}class sl extends Zi{async _call(e){return new _u(await super._call(e))}}class rl extends oe{}class ol extends zi{}class nl extends zi{async _call(e){return new fu(await super._call(e))}}class al extends zi{async _call(e){return new _u(await super._call(e))}}class il extends oe{}class ll extends il{}class cl extends il{async _call(e){return new fu(await super._call(e))}}class dl extends il{async _call(e){return new _u(await super._call(e))}}class ul extends il{async _call(e){return new mu(await super._call(e))}}class _l extends il{async _call(e){return new pu(await super._call(e))}}class ml extends oe{}class pl extends ml{}class hl extends oe{}class gl extends hl{}class fl extends hl{}class Ml extends hl{async generate_speech(e,t,{threshold:s=.5,minlenratio:r=0,maxlenratio:o=20,vocoder:n=null}={}){const a={input_ids:e},{encoder_outputs:i,encoder_attention_mask:l}=await $(this,a),c=i.dims[1]/this.config.reduction_factor,d=Math.floor(c*o),u=Math.floor(c*r),m=this.config.num_mel_bins;let p=[],h=null,g=null,f=0;for(;;){++f;const e=R(!!g);let r;r=g?g.output_sequence_out:new _.Tensor("float32",new Float32Array(m),[1,1,m]);let o={use_cache_branch:e,output_sequence:r,encoder_attention_mask:l,speaker_embeddings:t,encoder_hidden_states:i};this.addPastKeyValues(o,h),g=await O(this.sessions.decoder_model_merged,o),h=this.getPastKeyValues(g,h);const{prob:n,spectrum:a}=g;if(p.push(a),f>=u&&(Array.from(n.data).filter((e=>e>=s)).length>0||f>=d))break}const M=(0,_.cat)(p),{waveform:w}=await O(n.sessions.model,{spectrogram:M});return{spectrogram:M,waveform:w}}}class wl extends oe{main_input_name="spectrogram"}class Tl extends oe{}class bl extends Tl{}class xl extends oe{}class Pl extends xl{}class kl extends xl{}class Fl extends oe{}class vl extends Fl{}class yl extends Fl{}class Cl extends oe{}class Sl extends Cl{}class Al extends Cl{}class El extends oe{}class Ll extends El{}class Il extends El{}class zl extends oe{}class Dl extends zl{}class jl extends zl{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class Vl extends zl{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"audio_model"})}}class Nl extends oe{}class Ol extends Nl{async _call(e){return new Tu(await super._call(e))}}class Bl extends oe{}class Gl extends Bl{}class Rl extends Bl{}class ql extends Bl{}class $l extends oe{}class Wl extends $l{}class Ul extends $l{}class Ql extends oe{}class Xl extends Ql{}class Hl extends Ql{async _call(e){return new _u(await super._call(e))}}class Jl extends oe{}class Yl extends Jl{}class Kl extends Jl{}class Zl extends oe{forward_params=["input_ids","attention_mask","encoder_outputs","decoder_input_ids","decoder_attention_mask","past_key_values"];_apply_and_filter_by_delay_pattern_mask(e){const[t,s]=e.dims,r=this.config.decoder.num_codebooks,o=s-r;let n=0;for(let t=0;t<e.size;++t){if(e.data[t]===this.config.decoder.pad_token_id)continue;const a=t%s-Math.floor(t/s)%r;a>0&&a<=o&&(e.data[n++]=e.data[t])}const a=Math.floor(t/r),i=n/(a*r);return new _.Tensor(e.type,e.data.slice(0,n),[a,r,i])}prepare_inputs_for_generation(e,t,s){let r=structuredClone(e);for(let e=0;e<r.length;++e)for(let t=0;t<r[e].length;++t)e%this.config.decoder.num_codebooks>=t&&(r[e][t]=BigInt(this.config.decoder.pad_token_id));null!==s.guidance_scale&&s.guidance_scale>1&&(r=r.concat(r));return super.prepare_inputs_for_generation(r,t,s)}async generate(e){const t=await super.generate(e),s=this._apply_and_filter_by_delay_pattern_mask(t).unsqueeze_(0),{audio_values:r}=await O(this.sessions.encodec_decode,{audio_codes:s});return r}}class ec extends oe{}class tc extends ec{}class sc extends ec{async _call(e){return new _u(await super._call(e))}}class rc extends ec{}class oc extends oe{}class nc extends oc{}class ac extends oc{async _call(e){return new _u(await super._call(e))}}class ic extends oc{}class lc extends oe{}class cc extends lc{}class dc extends lc{async _call(e){return new _u(await super._call(e))}}class uc extends lc{}class _c extends oe{}class mc extends _c{}class pc extends _c{async _call(e){return new _u(await super._call(e))}}class hc extends _c{}class gc extends oe{}class fc extends gc{}class Mc extends oe{}class wc extends Mc{forward_params=["input_ids","pixel_values","images_seq_mask","images_emb_mask","attention_mask","position_ids","past_key_values"];constructor(...e){super(...e),this._generation_mode="text"}async forward(e){const t=this._generation_mode??"text";let s;if("text"!==t&&e.past_key_values){const t=this.sessions.gen_img_embeds,r=(0,i.pick)({image_ids:e.input_ids},t.inputNames);s=await O(t,r)}else{const t=this.sessions.prepare_inputs_embeds,r=(0,i.pick)(e,t.inputNames);s=await O(t,r)}const r={...e,...s},o=await U(this,r),n=this.sessions["text"===t?"lm_head":"gen_head"];if(!n)throw new Error(`Unable to find "${n}" generation head`);const a=await O(n,(0,i.pick)(o,n.inputNames));return{...s,...o,...a}}async generate(e){return this._generation_mode="text",super.generate(e)}async generate_images(e){this._generation_mode="image";const t=(e.inputs??e[this.main_input_name]).dims[1],s=(await super.generate(e)).slice(null,[t,null]),r=this.sessions.image_decode,{decoded_image:o}=await O(r,{generated_tokens:s}),n=o.add_(1).mul_(127.5).clamp_(0,255).to("uint8"),a=[];for(const e of n){const t=m.RawImage.fromTensor(e);a.push(t)}return a}}class Tc extends ne{constructor({char_logits:e,bpe_logits:t,wp_logits:s}){super(),this.char_logits=e,this.bpe_logits=t,this.wp_logits=s}get logits(){return[this.char_logits,this.bpe_logits,this.wp_logits]}}class bc extends oe{}class xc extends bc{async _call(e){return new Tc(await super._call(e))}}class Pc extends oe{}class kc extends Pc{}class Fc extends Pc{}class vc extends oe{}class yc extends vc{}class Cc extends vc{}class Sc extends oe{forward_params=["input_ids","attention_mask","position_ids","audio_values","past_key_values"]}class Ac extends Sc{_merge_input_ids_with_audio_features(e){const t=e.audio_features.dims.at(-1),s=e.audio_features.view(-1,t);return H({audio_token_id:this.config.ignore_index??this.config.audio_token_id,...e,audio_features:s})}}class Ec extends Ac{}class Lc extends oe{main_input_name="input_values";forward_params=["input_values"]}class Ic extends ne{constructor({audio_codes:e}){super(),this.audio_codes=e}}class zc extends ne{constructor({audio_values:e}){super(),this.audio_values=e}}class Dc extends Lc{async encode(e){return new Ic(await O(this.sessions.encoder_model,e))}async decode(e){return new zc(await O(this.sessions.decoder_model,e))}}class jc extends Lc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"encoder_model"})}}class Vc extends Lc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"decoder_model"})}}class Nc extends oe{main_input_name="input_values";forward_params=["input_values"]}class Oc extends ne{constructor({audio_codes:e}){super(),this.audio_codes=e}}class Bc extends ne{constructor({audio_values:e}){super(),this.audio_values=e}}class Gc extends Nc{async encode(e){return new Oc(await O(this.sessions.encoder_model,e))}async decode(e){return new Bc(await O(this.sessions.decoder_model,e))}}class Rc extends Nc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"encoder_model"})}}class qc extends Nc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"decoder_model"})}}class $c extends oe{main_input_name="input_values";forward_params=["input_values"]}class Wc extends $c{async encode(e){return await O(this.sessions.encoder_model,e)}async decode(e){return await O(this.sessions.decoder_model,e)}}class Uc extends $c{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"encoder_model"})}}class Qc extends $c{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"decoder_model"})}}class Xc{static MODEL_CLASS_MAPPINGS=null;static BASE_IF_FAIL=!1;static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:o=null,local_files_only:n=!1,revision:a="main",model_file_name:i=null,subfolder:l="onnx",device:c=null,dtype:d=null,use_external_data_format:u=null,session_options:_={}}={}){const m={progress_callback:t,config:s,cache_dir:o,local_files_only:n,revision:a,model_file_name:i,subfolder:l,device:c,dtype:d,use_external_data_format:u,session_options:_};if(m.config=await r.AutoConfig.from_pretrained(e,m),!this.MODEL_CLASS_MAPPINGS)throw new Error("`MODEL_CLASS_MAPPINGS` not implemented for this type of `AutoClass`: "+this.name);const p=m.config.model_type;for(const t of this.MODEL_CLASS_MAPPINGS){let s=t.get(p);if(!s){for(const e of t.values())if(e[0]===p){s=e;break}if(!s)continue}return await s[1].from_pretrained(e,m)}if(this.BASE_IF_FAIL)return Id.has(p)||console.warn(`Unknown model class "${p}", attempting to construct from base class.`),await oe.from_pretrained(e,m);throw Error(`Unsupported model type: ${p}`)}}const Hc=new Map([["bert",["BertModel",le]],["neobert",["NeoBertModel",pe]],["modernbert",["ModernBertModel",Te]],["nomic_bert",["NomicBertModel",Ce]],["roformer",["RoFormerModel",Ae]],["electra",["ElectraModel",Re]],["esm",["EsmModel",Mt]],["convbert",["ConvBertModel",je]],["camembert",["CamembertModel",Xe]],["deberta",["DebertaModel",et]],["deberta-v2",["DebertaV2Model",at]],["mpnet",["MPNetModel",Ct]],["albert",["AlbertModel",Ot]],["distilbert",["DistilBertModel",_t]],["roberta",["RobertaModel",ps]],["xlm",["XLMModel",Ts]],["xlm-roberta",["XLMRobertaModel",vs]],["clap",["ClapModel",Dl]],["clip",["CLIPModel",nr]],["clipseg",["CLIPSegModel",br]],["chinese_clip",["ChineseCLIPModel",hr]],["siglip",["SiglipModel",ur]],["jina_clip",["JinaCLIPModel",fr]],["mobilebert",["MobileBertModel",Pt]],["squeezebert",["SqueezeBertModel",zt]],["wav2vec2",["Wav2Vec2Model",Di]],["wav2vec2-bert",["Wav2Vec2BertModel",el]],["unispeech",["UniSpeechModel",Wi]],["unispeech-sat",["UniSpeechSatModel",Hi]],["hubert",["HubertModel",ol]],["wavlm",["WavLMModel",ll]],["audio-spectrogram-transformer",["ASTModel",Ls]],["vits",["VitsModel",Ol]],["pyannote",["PyAnnoteModel",Bi]],["wespeaker-resnet",["WeSpeakerResNetModel",qi]],["detr",["DetrModel",Xn]],["rt_detr",["RTDetrModel",ea]],["rt_detr_v2",["RTDetrV2Model",oa]],["rf_detr",["RFDetrModel",la]],["d_fine",["DFineModel",_a]],["table-transformer",["TableTransformerModel",ha]],["vit",["ViTModel",dn]],["ijepa",["IJepaModel",mn]],["pvt",["PvtModel",Mn]],["vit_msn",["ViTMSNModel",Pn]],["vit_mae",["ViTMAEModel",bn]],["groupvit",["GroupViTModel",vn]],["fastvit",["FastViTModel",Cn]],["mobilevit",["MobileViTModel",In]],["mobilevitv2",["MobileViTV2Model",jn]],["owlvit",["OwlViTModel",On]],["owlv2",["Owlv2Model",Rn]],["beit",["BeitModel",Wn]],["deit",["DeiTModel",wa]],["hiera",["HieraModel",xa]],["convnext",["ConvNextModel",oi]],["convnextv2",["ConvNextV2Model",ii]],["dinov2",["Dinov2Model",di]],["dinov2_with_registers",["Dinov2WithRegistersModel",mi]],["dinov3_vit",["DINOv3ViTModel",gi]],["dinov3_convnext",["DINOv3ConvNextModel",Mi]],["resnet",["ResNetModel",Fa]],["swin",["SwinModel",Ca]],["swin2sr",["Swin2SRModel",La]],["donut-swin",["DonutSwinModel",si]],["yolos",["YolosModel",xi]],["dpt",["DPTModel",Da]],["glpn",["GLPNModel",Za]],["hifigan",["SpeechT5HifiGan",wl]],["efficientnet",["EfficientNetModel",Xl]],["decision_transformer",["DecisionTransformerModel",fc]],["patchtst",["PatchTSTForPrediction",kc]],["patchtsmixer",["PatchTSMixerForPrediction",yc]],["mobilenet_v1",["MobileNetV1Model",tc]],["mobilenet_v2",["MobileNetV2Model",nc]],["mobilenet_v3",["MobileNetV3Model",cc]],["mobilenet_v4",["MobileNetV4Model",mc]],["maskformer",["MaskFormerModel",Ja]],["mgp-str",["MgpstrForSceneTextRecognition",xc]],["style_text_to_speech_2",["StyleTextToSpeech2Model",pl]]]),Jc=new Map([["t5",["T5Model",$t]],["longt5",["LongT5Model",Qt]],["mt5",["MT5Model",Jt]],["bart",["BartModel",Zt]],["mbart",["MBartModel",rs]],["marian",["MarianModel",Si]],["whisper",["WhisperModel",Ds]],["m2m_100",["M2M100Model",Li]],["blenderbot",["BlenderbotModel",ls]],["blenderbot-small",["BlenderbotSmallModel",us]]]),Yc=new Map([["mimi",["MimiModel",Dc]],["dac",["DacModel",Gc]],["snac",["SnacModel",Wc]]]),Kc=new Map([["bloom",["BloomModel",en]],["jais",["JAISModel",yr]],["gpt2",["GPT2Model",kr]],["gptj",["GPTJModel",jr]],["gpt_bigcode",["GPTBigCodeModel",Or]],["gpt_neo",["GPTNeoModel",Ar]],["gpt_neox",["GPTNeoXModel",Ir]],["codegen",["CodeGenModel",Rr]],["llama",["LlamaModel",Wr]],["arcee",["ArceeModel",Xr]],["lfm2",["Lfm2Model",Yr]],["smollm3",["SmolLM3Model",eo]],["exaone",["ExaoneModel",co]],["olmo",["OlmoModel",go]],["olmo2",["Olmo2Model",wo]],["mobilellm",["MobileLLMModel",mo]],["granite",["GraniteModel",xo]],["cohere",["CohereModel",Fo]],["gemma",["GemmaModel",Co]],["gemma2",["Gemma2Model",Eo]],["gemma3_text",["Gemma3Model",zo]],["helium",["HeliumModel",ro]],["glm",["GlmModel",ao]],["openelm",["OpenELMModel",Vo]],["qwen2",["Qwen2Model",Bo]],["qwen3",["Qwen3Model",qo]],["phi",["PhiModel",Xo]],["phi3",["Phi3Model",Yo]],["mpt",["MptModel",rn]],["opt",["OPTModel",an]],["mistral",["MistralModel",Pl]],["ernie4_5",["Ernie4_5_Model",vl]],["starcoder2",["Starcoder2Model",Sl]],["falcon",["FalconModel",Ll]],["stablelm",["StableLmModel",Wl]],["modernbert-decoder",["ModernBertDecoderModel",Fe]]]),Zc=new Map([["speecht5",["SpeechT5ForSpeechToText",fl]],["whisper",["WhisperForConditionalGeneration",js]],["lite-whisper",["LiteWhisperForConditionalGeneration",Vs]],["moonshine",["MoonshineForConditionalGeneration",Bs]]]),ed=new Map([["speecht5",["SpeechT5ForTextToSpeech",Ml]]]),td=new Map([["vits",["VitsModel",Ol]],["musicgen",["MusicgenForConditionalGeneration",Zl]]]),sd=new Map([["bert",["BertForSequenceClassification",de]],["neobert",["NeoBertForSequenceClassification",ge]],["modernbert",["ModernBertForSequenceClassification",xe]],["roformer",["RoFormerForSequenceClassification",Le]],["electra",["ElectraForSequenceClassification",$e]],["esm",["EsmForSequenceClassification",Tt]],["convbert",["ConvBertForSequenceClassification",Ne]],["camembert",["CamembertForSequenceClassification",Je]],["deberta",["DebertaForSequenceClassification",st]],["deberta-v2",["DebertaV2ForSequenceClassification",lt]],["mpnet",["MPNetForSequenceClassification",At]],["albert",["AlbertForSequenceClassification",Bt]],["distilbert",["DistilBertForSequenceClassification",mt]],["roberta",["RobertaForSequenceClassification",gs]],["xlm",["XLMForSequenceClassification",xs]],["xlm-roberta",["XLMRobertaForSequenceClassification",Cs]],["bart",["BartForSequenceClassification",ts]],["mbart",["MBartForSequenceClassification",ns]],["mobilebert",["MobileBertForSequenceClassification",Ft]],["squeezebert",["SqueezeBertForSequenceClassification",jt]]]),rd=new Map([["bert",["BertForTokenClassification",ue]],["neobert",["NeoBertForTokenClassification",fe]],["modernbert",["ModernBertForTokenClassification",Pe]],["roformer",["RoFormerForTokenClassification",Ie]],["electra",["ElectraForTokenClassification",We]],["esm",["EsmForTokenClassification",bt]],["convbert",["ConvBertForTokenClassification",Oe]],["camembert",["CamembertForTokenClassification",Ye]],["deberta",["DebertaForTokenClassification",rt]],["deberta-v2",["DebertaV2ForTokenClassification",ct]],["mpnet",["MPNetForTokenClassification",Et]],["distilbert",["DistilBertForTokenClassification",pt]],["roberta",["RobertaForTokenClassification",fs]],["xlm",["XLMForTokenClassification",Ps]],["xlm-roberta",["XLMRobertaForTokenClassification",Ss]]]),od=new Map([["t5",["T5ForConditionalGeneration",Wt]],["longt5",["LongT5ForConditionalGeneration",Xt]],["mt5",["MT5ForConditionalGeneration",Yt]],["bart",["BartForConditionalGeneration",es]],["mbart",["MBartForConditionalGeneration",os]],["marian",["MarianMTModel",Ai]],["m2m_100",["M2M100ForConditionalGeneration",Ii]],["blenderbot",["BlenderbotForConditionalGeneration",cs]],["blenderbot-small",["BlenderbotSmallForConditionalGeneration",_s]]]),nd=new Map([["bloom",["BloomForCausalLM",tn]],["gpt2",["GPT2LMHeadModel",Fr]],["jais",["JAISLMHeadModel",Cr]],["gptj",["GPTJForCausalLM",Vr]],["gpt_bigcode",["GPTBigCodeForCausalLM",Br]],["gpt_neo",["GPTNeoForCausalLM",Er]],["gpt_neox",["GPTNeoXForCausalLM",zr]],["codegen",["CodeGenForCausalLM",qr]],["llama",["LlamaForCausalLM",Ur]],["arcee",["ArceeForCausalLM",Hr]],["lfm2",["Lfm2ForCausalLM",Kr]],["smollm3",["SmolLM3ForCausalLM",to]],["exaone",["ExaoneForCausalLM",uo]],["olmo",["OlmoForCausalLM",fo]],["olmo2",["Olmo2ForCausalLM",To]],["mobilellm",["MobileLLMForCausalLM",po]],["granite",["GraniteForCausalLM",Po]],["cohere",["CohereForCausalLM",vo]],["gemma",["GemmaForCausalLM",So]],["gemma2",["Gemma2ForCausalLM",Lo]],["gemma3_text",["Gemma3ForCausalLM",Do]],["helium",["HeliumForCausalLM",oo]],["glm",["GlmForCausalLM",io]],["openelm",["OpenELMForCausalLM",No]],["qwen2",["Qwen2ForCausalLM",Go]],["qwen3",["Qwen3ForCausalLM",$o]],["phi",["PhiForCausalLM",Ho]],["phi3",["Phi3ForCausalLM",Ko]],["mpt",["MptForCausalLM",on]],["opt",["OPTForCausalLM",ln]],["mbart",["MBartForCausalLM",as]],["mistral",["MistralForCausalLM",kl]],["ernie4_5",["Ernie4_5_ForCausalLM",yl]],["starcoder2",["Starcoder2ForCausalLM",Al]],["falcon",["FalconForCausalLM",Il]],["trocr",["TrOCRForCausalLM",bl]],["stablelm",["StableLmForCausalLM",Ul]],["modernbert-decoder",["ModernBertDecoderForCausalLM",ve]],["phi3_v",["Phi3VForCausalLM",rr]]]),ad=new Map([["multi_modality",["MultiModalityCausalLM",wc]]]),id=new Map([["bert",["BertForMaskedLM",ce]],["neobert",["NeoBertForMaskedLM",he]],["modernbert",["ModernBertForMaskedLM",be]],["roformer",["RoFormerForMaskedLM",Ee]],["electra",["ElectraForMaskedLM",qe]],["esm",["EsmForMaskedLM",wt]],["convbert",["ConvBertForMaskedLM",Ve]],["camembert",["CamembertForMaskedLM",He]],["deberta",["DebertaForMaskedLM",tt]],["deberta-v2",["DebertaV2ForMaskedLM",it]],["mpnet",["MPNetForMaskedLM",St]],["albert",["AlbertForMaskedLM",Rt]],["distilbert",["DistilBertForMaskedLM",gt]],["roberta",["RobertaForMaskedLM",hs]],["xlm",["XLMWithLMHeadModel",bs]],["xlm-roberta",["XLMRobertaForMaskedLM",ys]],["mobilebert",["MobileBertForMaskedLM",kt]],["squeezebert",["SqueezeBertForMaskedLM",Dt]]]),ld=new Map([["bert",["BertForQuestionAnswering",_e]],["neobert",["NeoBertForQuestionAnswering",Me]],["roformer",["RoFormerForQuestionAnswering",ze]],["electra",["ElectraForQuestionAnswering",Ue]],["convbert",["ConvBertForQuestionAnswering",Be]],["camembert",["CamembertForQuestionAnswering",Ke]],["deberta",["DebertaForQuestionAnswering",ot]],["deberta-v2",["DebertaV2ForQuestionAnswering",dt]],["mpnet",["MPNetForQuestionAnswering",Lt]],["albert",["AlbertForQuestionAnswering",Gt]],["distilbert",["DistilBertForQuestionAnswering",ht]],["roberta",["RobertaForQuestionAnswering",Ms]],["xlm",["XLMForQuestionAnswering",ks]],["xlm-roberta",["XLMRobertaForQuestionAnswering",As]],["mobilebert",["MobileBertForQuestionAnswering",vt]],["squeezebert",["SqueezeBertForQuestionAnswering",Vt]]]),cd=new Map([["vision-encoder-decoder",["VisionEncoderDecoderModel",Gs]],["idefics3",["Idefics3ForConditionalGeneration",er]],["smolvlm",["SmolVLMForConditionalGeneration",tr]]]),dd=new Map([["llava",["LlavaForConditionalGeneration",qs]],["llava_onevision",["LlavaOnevisionForConditionalGeneration",$s]],["moondream1",["Moondream1ForConditionalGeneration",Ws]],["florence2",["Florence2ForConditionalGeneration",Qs]],["qwen2-vl",["Qwen2VLForConditionalGeneration",Uo]],["idefics3",["Idefics3ForConditionalGeneration",er]],["smolvlm",["SmolVLMForConditionalGeneration",tr]],["paligemma",["PaliGemmaForConditionalGeneration",Hs]],["llava_qwen2",["LlavaQwen2ForCausalLM",Js]],["gemma3n",["Gemma3nForConditionalGeneration",Ks]]]),ud=new Map([["ultravox",["UltravoxModel",Ac]],["voxtral",["VoxtralForConditionalGeneration",Ec]]]),_d=new Map([["vision-encoder-decoder",["VisionEncoderDecoderModel",Gs]]]),md=new Map([["vit",["ViTForImageClassification",un]],["ijepa",["IJepaForImageClassification",pn]],["pvt",["PvtForImageClassification",wn]],["vit_msn",["ViTMSNForImageClassification",kn]],["fastvit",["FastViTForImageClassification",Sn]],["mobilevit",["MobileViTForImageClassification",zn]],["mobilevitv2",["MobileViTV2ForImageClassification",Vn]],["beit",["BeitForImageClassification",Un]],["deit",["DeiTForImageClassification",Ta]],["hiera",["HieraForImageClassification",Pa]],["convnext",["ConvNextForImageClassification",ni]],["convnextv2",["ConvNextV2ForImageClassification",li]],["dinov2",["Dinov2ForImageClassification",ui]],["dinov2_with_registers",["Dinov2WithRegistersForImageClassification",pi]],["resnet",["ResNetForImageClassification",va]],["swin",["SwinForImageClassification",Sa]],["segformer",["SegformerForImageClassification",Rl]],["efficientnet",["EfficientNetForImageClassification",Hl]],["mobilenet_v1",["MobileNetV1ForImageClassification",sc]],["mobilenet_v2",["MobileNetV2ForImageClassification",ac]],["mobilenet_v3",["MobileNetV3ForImageClassification",dc]],["mobilenet_v4",["MobileNetV4ForImageClassification",pc]]]),pd=new Map([["detr",["DetrForObjectDetection",Hn]],["rt_detr",["RTDetrForObjectDetection",ta]],["rt_detr_v2",["RTDetrV2ForObjectDetection",na]],["rf_detr",["RFDetrForObjectDetection",ca]],["d_fine",["DFineForObjectDetection",ma]],["table-transformer",["TableTransformerForObjectDetection",ga]],["yolos",["YolosForObjectDetection",Pi]]]),hd=new Map([["owlvit",["OwlViTForObjectDetection",Bn]],["owlv2",["Owlv2ForObjectDetection",qn]],["grounding-dino",["GroundingDinoForObjectDetection",Ti]]]),gd=new Map([["detr",["DetrForSegmentation",Jn]],["clipseg",["CLIPSegForImageSegmentation",xr]]]),fd=new Map([["segformer",["SegformerForSemanticSegmentation",ql]],["sapiens",["SapiensForSemanticSegmentation",Ba]],["swin",["SwinForSemanticSegmentation",Aa]],["mobilenet_v1",["MobileNetV1ForSemanticSegmentation",rc]],["mobilenet_v2",["MobileNetV2ForSemanticSegmentation",ic]],["mobilenet_v3",["MobileNetV3ForSemanticSegmentation",uc]],["mobilenet_v4",["MobileNetV4ForSemanticSegmentation",hc]]]),Md=new Map([["detr",["DetrForSegmentation",Jn]],["maskformer",["MaskFormerForInstanceSegmentation",Ya]]]),wd=new Map([["sam",["SamModel",vi]]]),Td=new Map([["wav2vec2",["Wav2Vec2ForCTC",ji]],["wav2vec2-bert",["Wav2Vec2BertForCTC",tl]],["unispeech",["UniSpeechForCTC",Ui]],["unispeech-sat",["UniSpeechSatForCTC",Ji]],["wavlm",["WavLMForCTC",cl]],["hubert",["HubertForCTC",nl]]]),bd=new Map([["wav2vec2",["Wav2Vec2ForSequenceClassification",Vi]],["wav2vec2-bert",["Wav2Vec2BertForSequenceClassification",sl]],["unispeech",["UniSpeechForSequenceClassification",Qi]],["unispeech-sat",["UniSpeechSatForSequenceClassification",Yi]],["wavlm",["WavLMForSequenceClassification",dl]],["hubert",["HubertForSequenceClassification",al]],["audio-spectrogram-transformer",["ASTForAudioClassification",Is]]]),xd=new Map([["wavlm",["WavLMForXVector",ul]]]),Pd=new Map([["unispeech-sat",["UniSpeechSatForAudioFrameClassification",Ki]],["wavlm",["WavLMForAudioFrameClassification",_l]],["wav2vec2",["Wav2Vec2ForAudioFrameClassification",Ni]],["pyannote",["PyAnnoteForAudioFrameClassification",Gi]]]),kd=new Map([["vitmatte",["VitMatteForImageMatting",En]]]),Fd=new Map([["patchtst",["PatchTSTForPrediction",Fc]],["patchtsmixer",["PatchTSMixerForPrediction",Cc]]]),vd=new Map([["swin2sr",["Swin2SRForImageSuperResolution",Ia]]]),yd=new Map([["dpt",["DPTForDepthEstimation",ja]],["depth_anything",["DepthAnythingForDepthEstimation",Na]],["glpn",["GLPNForDepthEstimation",ei]],["sapiens",["SapiensForDepthEstimation",Ga]],["depth_pro",["DepthProForDepthEstimation",$a]],["metric3d",["Metric3DForDepthEstimation",Ua]],["metric3dv2",["Metric3Dv2ForDepthEstimation",Xa]]]),Cd=new Map([["sapiens",["SapiensForNormalEstimation",Ra]]]),Sd=new Map([["vitpose",["VitPoseForPoseEstimation",gn]]]),Ad=new Map([["clip",["CLIPVisionModelWithProjection",cr]],["siglip",["SiglipVisionModel",mr]],["jina_clip",["JinaCLIPVisionModel",wr]]]),Ed=[[Hc,T],[Jc,b],[Kc,k],[Yc,E],[sd,T],[rd,T],[od,x],[Zc,x],[nd,k],[ad,C],[id,T],[ld,T],[cd,P],[dd,v],[ud,A],[md,T],[gd,T],[Md,T],[fd,T],[kd,T],[Fd,T],[vd,T],[yd,T],[Cd,T],[Sd,T],[pd,T],[hd,T],[wd,F],[Td,T],[bd,T],[ed,x],[td,T],[xd,T],[Pd,T],[Ad,T]];for(const[e,t]of Ed)for(const[s,r]of e.values())I.set(s,t),D.set(r,s),z.set(s,r);const Ld=[["MusicgenForConditionalGeneration",Zl,y],["Phi3VForCausalLM",rr,S],["CLIPTextModelWithProjection",ir,T],["SiglipTextModel",_r,T],["JinaCLIPTextModel",Mr,T],["ClapTextModelWithProjection",jl,T],["ClapAudioModelWithProjection",Vl,T],["DacEncoderModel",Rc,T],["DacDecoderModel",qc,T],["MimiEncoderModel",jc,T],["MimiDecoderModel",Vc,T],["SnacEncoderModel",Uc,T],["SnacDecoderModel",Qc,T],["Gemma3nForConditionalGeneration",Ks,L]];for(const[e,t,s]of Ld)I.set(e,s),D.set(t,e),z.set(e,t);const Id=new Map([["modnet",gd],["birefnet",gd],["isnet",gd],["ben",gd]]);for(const[e,t]of Id.entries())t.set(e,["PreTrainedModel",oe]),I.set(e,T),D.set(oe,e),z.set(e,oe);class zd extends Xc{static MODEL_CLASS_MAPPINGS=Ed.map((e=>e[0]));static BASE_IF_FAIL=!0}class Dd extends Xc{static MODEL_CLASS_MAPPINGS=[sd]}class jd extends Xc{static MODEL_CLASS_MAPPINGS=[rd]}class Vd extends Xc{static MODEL_CLASS_MAPPINGS=[od]}class Nd extends Xc{static MODEL_CLASS_MAPPINGS=[Zc]}class Od extends Xc{static MODEL_CLASS_MAPPINGS=[ed]}class Bd extends Xc{static MODEL_CLASS_MAPPINGS=[td]}class Gd extends Xc{static MODEL_CLASS_MAPPINGS=[nd]}class Rd extends Xc{static MODEL_CLASS_MAPPINGS=[id]}class qd extends Xc{static MODEL_CLASS_MAPPINGS=[ld]}class $d extends Xc{static MODEL_CLASS_MAPPINGS=[cd]}class Wd extends Xc{static MODEL_CLASS_MAPPINGS=[md]}class Ud extends Xc{static MODEL_CLASS_MAPPINGS=[gd]}class Qd extends Xc{static MODEL_CLASS_MAPPINGS=[fd]}class Xd extends Xc{static MODEL_CLASS_MAPPINGS=[Md]}class Hd extends Xc{static MODEL_CLASS_MAPPINGS=[pd]}class Jd extends Xc{static MODEL_CLASS_MAPPINGS=[hd]}class Yd extends Xc{static MODEL_CLASS_MAPPINGS=[wd]}class Kd extends Xc{static MODEL_CLASS_MAPPINGS=[Td]}class Zd extends Xc{static MODEL_CLASS_MAPPINGS=[bd]}class eu extends Xc{static MODEL_CLASS_MAPPINGS=[xd]}class tu extends Xc{static MODEL_CLASS_MAPPINGS=[Pd]}class su extends Xc{static MODEL_CLASS_MAPPINGS=[_d]}class ru extends Xc{static MODEL_CLASS_MAPPINGS=[kd]}class ou extends Xc{static MODEL_CLASS_MAPPINGS=[vd]}class nu extends Xc{static MODEL_CLASS_MAPPINGS=[yd]}class au extends Xc{static MODEL_CLASS_MAPPINGS=[Cd]}class iu extends Xc{static MODEL_CLASS_MAPPINGS=[Sd]}class lu extends Xc{static MODEL_CLASS_MAPPINGS=[Ad]}class cu extends Xc{static MODEL_CLASS_MAPPINGS=[dd]}class du extends Xc{static MODEL_CLASS_MAPPINGS=[ud]}class uu extends ne{constructor({logits:e,past_key_values:t,encoder_outputs:s,decoder_attentions:r=null,cross_attentions:o=null}){super(),this.logits=e,this.past_key_values=t,this.encoder_outputs=s,this.decoder_attentions=r,this.cross_attentions=o}}class _u extends ne{constructor({logits:e,...t}){super(),this.logits=e;const s=Object.values(t);s.length>0&&(this.attentions=s)}}class mu extends ne{constructor({logits:e,embeddings:t}){super(),this.logits=e,this.embeddings=t}}class pu extends ne{constructor({logits:e}){super(),this.logits=e}}class hu extends ne{constructor({logits:e}){super(),this.logits=e}}class gu extends ne{constructor({start_logits:e,end_logits:t}){super(),this.start_logits=e,this.end_logits=t}}class fu extends ne{constructor({logits:e}){super(),this.logits=e}}class Mu extends ne{constructor({logits:e,past_key_values:t}){super(),this.logits=e,this.past_key_values=t}}class wu extends ne{constructor({alphas:e}){super(),this.alphas=e}}class Tu extends ne{constructor({waveform:e,spectrogram:t}){super(),this.waveform=e,this.spectrogram=t}}},"./src/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.js":(e,t,s)=>{s.r(t),s.d(t,{ASTFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js"));class n extends r.FeatureExtractor{constructor(e){super(e);const t=this.config.sampling_rate,s=(0,o.mel_filter_bank)(257,this.config.num_mel_bins,20,Math.floor(t/2),t,null,"kaldi",!0);this.mel_filters=s,this.window=(0,o.window_function)(400,"hann",{periodic:!1}),this.mean=this.config.mean,this.std=this.config.std}async _extract_fbank_features(e,t){return(0,o.spectrogram)(e,this.window,400,160,{fft_length:512,power:2,center:!1,preemphasis:.97,mel_filters:this.mel_filters,log_mel:"log",mel_floor:1.192092955078125e-7,remove_dc_offset:!0,max_num_frames:t,transpose:!0})}async _call(e){(0,r.validate_audio_inputs)(e,"ASTFeatureExtractor");const t=await this._extract_fbank_features(e,this.config.max_length);if(this.config.do_normalize){const e=2*this.std,s=t.data;for(let t=0;t<s.length;++t)s[t]=(s[t]-this.mean)/e}return{input_values:t.unsqueeze_(0)}}}},"./src/models/auto/feature_extraction_auto.js":(e,t,s)=>{s.r(t),s.d(t,{AutoFeatureExtractor:()=>a});var r=s("./src/utils/constants.js"),o=s("./src/utils/hub.js"),n=(s("./src/base/feature_extraction_utils.js"),s("./src/models/feature_extractors.js"));class a{static async from_pretrained(e,t={}){const s=await(0,o.getModelJSON)(e,r.FEATURE_EXTRACTOR_NAME,!0,t),a=s.feature_extractor_type,i=n[a];if(!i)throw new Error(`Unknown feature_extractor_type: '${a}'. Please report this at ${r.GITHUB_ISSUE_URL}.`);return new i(s)}}},"./src/models/auto/image_processing_auto.js":(e,t,s)=>{s.r(t),s.d(t,{AutoImageProcessor:()=>i});var r=s("./src/utils/constants.js"),o=s("./src/utils/hub.js"),n=s("./src/base/image_processors_utils.js"),a=s("./src/models/image_processors.js");class i{static async from_pretrained(e,t={}){const s=await(0,o.getModelJSON)(e,r.IMAGE_PROCESSOR_NAME,!0,t),i=s.image_processor_type??s.feature_extractor_type;let l=a[i?.replace(/Fast$/,"")];return l||(void 0!==i&&console.warn(`Image processor type '${i}' not found, assuming base ImageProcessor. Please report this at ${r.GITHUB_ISSUE_URL}.`),l=n.ImageProcessor),new l(s)}}},"./src/models/auto/processing_auto.js":(e,t,s)=>{s.r(t),s.d(t,{AutoProcessor:()=>c});var r=s("./src/utils/constants.js"),o=s("./src/utils/hub.js"),n=s("./src/base/processing_utils.js"),a=s("./src/models/processors.js"),i=s("./src/models/image_processors.js"),l=s("./src/models/feature_extractors.js");class c{static async from_pretrained(e,t={}){const s=await(0,o.getModelJSON)(e,r.IMAGE_PROCESSOR_NAME,!0,t),{image_processor_type:c,feature_extractor_type:d,processor_class:u}=s;if(u&&a[u])return a[u].from_pretrained(e,t);if(!c&&!d)throw new Error("No `image_processor_type` or `feature_extractor_type` found in the config.");const _={};if(c){const e=i[c.replace(/Fast$/,"")];if(!e)throw new Error(`Unknown image_processor_type: '${c}'.`);_.image_processor=new e(s)}if(d){const e=i[d];if(e)_.image_processor=new e(s);else{const e=l[d];if(!e)throw new Error(`Unknown feature_extractor_type: '${d}'.`);_.feature_extractor=new e(s)}}return new n.Processor({},_,null)}}},"./src/models/beit/image_processing_beit.js":(e,t,s)=>{s.r(t),s.d(t,{BeitFeatureExtractor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/bit/image_processing_bit.js":(e,t,s)=>{s.r(t),s.d(t,{BitImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/chinese_clip/image_processing_chinese_clip.js":(e,t,s)=>{s.r(t),s.d(t,{ChineseCLIPFeatureExtractor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/clap/feature_extraction_clap.js":(e,t,s)=>{s.r(t),s.d(t,{ClapFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js"));class n extends r.FeatureExtractor{constructor(e){super(e),this.mel_filters=(0,o.mel_filter_bank)(this.config.nb_frequency_bins,this.config.feature_size,this.config.frequency_min,this.config.frequency_max,this.config.sampling_rate,null,"htk"),this.mel_filters_slaney=(0,o.mel_filter_bank)(this.config.nb_frequency_bins,this.config.feature_size,this.config.frequency_min,this.config.frequency_max,this.config.sampling_rate,"slaney","slaney"),this.window=(0,o.window_function)(this.config.fft_window_size,"hann")}async _get_input_mel(e,t,s,r){let o,n=!1;const a=e.length-t;if(a>0){if("rand_trunc"!==s)throw new Error(`Truncation strategy "${s}" not implemented`);{n=!0;const s=Math.floor(Math.random()*(a+1));e=e.subarray(s,s+t),o=await this._extract_fbank_features(e,this.mel_filters_slaney,this.config.nb_max_samples)}}else{if(a<0){let s=new Float64Array(t);if(s.set(e),"repeat"===r)for(let r=e.length;r<t;r+=e.length)s.set(e.subarray(0,Math.min(e.length,t-r)),r);else if("repeatpad"===r)for(let t=e.length;t<-a;t+=e.length)s.set(e,t);e=s}if("fusion"===s)throw new Error(`Truncation strategy "${s}" not implemented`);o=await this._extract_fbank_features(e,this.mel_filters_slaney,this.config.nb_max_samples)}return o.unsqueeze_(0)}async _extract_fbank_features(e,t,s=null){return(0,o.spectrogram)(e,this.window,this.config.fft_window_size,this.config.hop_length,{power:2,mel_filters:t,log_mel:"dB",max_num_frames:s,do_pad:!1,transpose:!0})}async _call(e,{max_length:t=null}={}){(0,r.validate_audio_inputs)(e,"ClapFeatureExtractor");return{input_features:(await this._get_input_mel(e,t??this.config.nb_max_samples,this.config.truncation,this.config.padding)).unsqueeze_(0)}}}},"./src/models/clip/image_processing_clip.js":(e,t,s)=>{s.r(t),s.d(t,{CLIPFeatureExtractor:()=>n,CLIPImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/convnext/image_processing_convnext.js":(e,t,s)=>{s.r(t),s.d(t,{ConvNextFeatureExtractor:()=>n,ConvNextImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){super(e),this.crop_pct=this.config.crop_pct??.875}async resize(e){const t=this.size?.shortest_edge;if(void 0===t)throw new Error("Size dictionary must contain 'shortest_edge' key.");if(t<384){const s=Math.floor(t/this.crop_pct),[r,o]=this.get_resize_output_image_size(e,{shortest_edge:s});e=await e.resize(r,o,{resample:this.resample}),e=await e.center_crop(t,t)}else e=await e.resize(t,t,{resample:this.resample});return e}}class n extends o{}},"./src/models/dac/feature_extraction_dac.js":(e,t,s)=>{s.r(t),s.d(t,{DacFeatureExtractor:()=>o});var r=s("./src/models/encodec/feature_extraction_encodec.js");class o extends r.EncodecFeatureExtractor{}},"./src/models/deit/image_processing_deit.js":(e,t,s)=>{s.r(t),s.d(t,{DeiTFeatureExtractor:()=>n,DeiTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/detr/image_processing_detr.js":(e,t,s)=>{s.r(t),s.d(t,{DetrFeatureExtractor:()=>a,DetrImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e){const t=await super._call(e),s=[t.pixel_values.dims[0],64,64],r=(0,o.full)(s,1n);return{...t,pixel_mask:r}}post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}post_process_panoptic_segmentation(...e){return(0,r.post_process_panoptic_segmentation)(...e)}post_process_instance_segmentation(...e){return(0,r.post_process_instance_segmentation)(...e)}}class a extends n{}},"./src/models/dinov3_vit/image_processing_dinov3_vit.js":(e,t,s)=>{s.r(t),s.d(t,{DINOv3ViTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/donut/image_processing_donut.js":(e,t,s)=>{s.r(t),s.d(t,{DonutFeatureExtractor:()=>n,DonutImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{pad_image(e,t,s,r={}){const[o,n,a]=t;let i=this.image_mean;Array.isArray(this.image_mean)||(i=new Array(a).fill(i));let l=this.image_std;Array.isArray(l)||(l=new Array(a).fill(i));const c=i.map(((e,t)=>-e/l[t]));return super.pad_image(e,t,s,{center:!0,constant_values:c,...r})}}class n extends o{}},"./src/models/dpt/image_processing_dpt.js":(e,t,s)=>{s.r(t),s.d(t,{DPTFeatureExtractor:()=>n,DPTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/efficientnet/image_processing_efficientnet.js":(e,t,s)=>{s.r(t),s.d(t,{EfficientNetImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){super(e),this.include_top=this.config.include_top??!0,this.include_top&&(this.image_std=this.image_std.map((e=>e*e)))}}},"./src/models/encodec/feature_extraction_encodec.js":(e,t,s)=>{s.r(t),s.d(t,{EncodecFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js");class n extends r.FeatureExtractor{async _call(e){(0,r.validate_audio_inputs)(e,"EncodecFeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));const t=this.config.feature_size;if(e.length%t!=0)throw new Error(`The length of the audio data must be a multiple of the number of channels (${t}).`);const s=[1,t,e.length/t];return{input_values:new o.Tensor("float32",e,s)}}}},"./src/models/feature_extractors.js":(e,t,s)=>{s.r(t),s.d(t,{ASTFeatureExtractor:()=>r.ASTFeatureExtractor,ClapFeatureExtractor:()=>n.ClapFeatureExtractor,DacFeatureExtractor:()=>a.DacFeatureExtractor,EncodecFeatureExtractor:()=>o.EncodecFeatureExtractor,Gemma3nAudioFeatureExtractor:()=>i.Gemma3nAudioFeatureExtractor,ImageFeatureExtractor:()=>g.ImageProcessor,MoonshineFeatureExtractor:()=>l.MoonshineFeatureExtractor,PyAnnoteFeatureExtractor:()=>c.PyAnnoteFeatureExtractor,SeamlessM4TFeatureExtractor:()=>d.SeamlessM4TFeatureExtractor,SnacFeatureExtractor:()=>u.SnacFeatureExtractor,SpeechT5FeatureExtractor:()=>_.SpeechT5FeatureExtractor,Wav2Vec2FeatureExtractor:()=>m.Wav2Vec2FeatureExtractor,WeSpeakerFeatureExtractor:()=>p.WeSpeakerFeatureExtractor,WhisperFeatureExtractor:()=>h.WhisperFeatureExtractor});var r=s("./src/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.js"),o=s("./src/models/encodec/feature_extraction_encodec.js"),n=s("./src/models/clap/feature_extraction_clap.js"),a=s("./src/models/dac/feature_extraction_dac.js"),i=s("./src/models/gemma3n/feature_extraction_gemma3n.js"),l=s("./src/models/moonshine/feature_extraction_moonshine.js"),c=s("./src/models/pyannote/feature_extraction_pyannote.js"),d=s("./src/models/seamless_m4t/feature_extraction_seamless_m4t.js"),u=s("./src/models/snac/feature_extraction_snac.js"),_=s("./src/models/speecht5/feature_extraction_speecht5.js"),m=s("./src/models/wav2vec2/feature_extraction_wav2vec2.js"),p=s("./src/models/wespeaker/feature_extraction_wespeaker.js"),h=s("./src/models/whisper/feature_extraction_whisper.js"),g=s("./src/base/image_processors_utils.js")},"./src/models/florence2/processing_florence2.js":(e,t,s)=>{s.r(t),s.d(t,{Florence2Processor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class a extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;constructor(e,t,s){super(e,t,s);const{tasks_answer_post_processing_type:r,task_prompts_without_inputs:o,task_prompts_with_input:n}=this.image_processor.config;this.tasks_answer_post_processing_type=new Map(Object.entries(r??{})),this.task_prompts_without_inputs=new Map(Object.entries(o??{})),this.task_prompts_with_input=new Map(Object.entries(n??{})),this.regexes={quad_boxes:/(.+?)<loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)>/gm,bboxes:/([^<]+)?<loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)>/gm},this.size_per_bin=1e3}construct_prompts(e){"string"==typeof e&&(e=[e]);const t=[];for(const s of e)if(this.task_prompts_without_inputs.has(s))t.push(this.task_prompts_without_inputs.get(s));else{for(const[e,r]of this.task_prompts_with_input)if(s.includes(e)){t.push(r.replaceAll("{input}",s).replaceAll(e,""));break}t.length!==e.length&&t.push(s)}return t}post_process_generation(e,t,s){const r=this.tasks_answer_post_processing_type.get(t)??"pure_text";let o;switch(e=e.replaceAll("<s>","").replaceAll("</s>",""),r){case"pure_text":o=e;break;case"description_with_bboxes":case"bboxes":case"phrase_grounding":case"ocr":const n="ocr"===r?"quad_boxes":"bboxes",a=e.matchAll(this.regexes[n]),i=[],l=[];for(const[e,t,...r]of a)i.push(t?t.trim():i.at(-1)??""),l.push(r.map(((e,t)=>(Number(e)+.5)/this.size_per_bin*s[t%2])));o={labels:i,[n]:l};break;default:throw new Error(`Task "${t}" (of type "${r}") not yet implemented.`)}return{[t]:o}}async _call(e,t=null,s={}){if(!e&&!t)throw new Error("Either text or images must be provided");return{...await this.image_processor(e,s),...t?this.tokenizer(this.construct_prompts(t),s):{}}}}},"./src/models/gemma3n/feature_extraction_gemma3n.js":(e,t,s)=>{s.r(t),s.d(t,{Gemma3nAudioFeatureExtractor:()=>a});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/audio.js");class a extends r.FeatureExtractor{constructor(e){super(e);const{fft_length:t,feature_size:s,min_frequency:r,max_frequency:o,sampling_rate:a,frame_length:i}=this.config,l=(0,n.mel_filter_bank)(Math.floor(1+t/2),s,r,o,a,null,"htk",!1);this.mel_filters=l,this.window=(0,n.window_function)(i,"hann")}async _extract_fbank_features(e,t){return(0,n.spectrogram)(e,this.window,this.config.frame_length,this.config.hop_length,{fft_length:this.config.fft_length,center:!1,onesided:!0,preemphasis:this.config.preemphasis,preemphasis_htk_flavor:this.config.preemphasis_htk_flavor,mel_filters:this.mel_filters,log_mel:"log",mel_floor:this.config.mel_floor,remove_dc_offset:!1,transpose:!0})}async _call(e,{max_length:t=48e4,truncation:s=!0,padding:n=!0,pad_to_multiple_of:a=128}={}){if((0,r.validate_audio_inputs)(e,"Gemma3nAudioFeatureExtractor"),s&&e.length>t&&(e=e.slice(0,t)),n&&e.length%a!=0){const t=a-e.length%a,s=new Float64Array(e.length+t);s.set(e),0!==this.config.padding_value&&s.fill(this.config.padding_value,e.length),e=s}const i=await this._extract_fbank_features(e,this.config.max_length),l=(0,o.full)([1,i.dims[0]],!0);return{input_features:i.unsqueeze_(0),input_features_mask:l}}}},"./src/models/gemma3n/processing_gemma3n.js":(e,t,s)=>{s.r(t),s.d(t,{Gemma3nProcessor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/models/auto/feature_extraction_auto.js"),a=s("./src/tokenizers.js");s("./src/utils/image.js"),s("./src/utils/audio.js");class i extends r.Processor{static image_processor_class=o.AutoImageProcessor;static feature_extractor_class=n.AutoFeatureExtractor;static tokenizer_class=a.AutoTokenizer;static uses_processor_config=!0;static uses_chat_template_file=!0;constructor(e,t,s){super(e,t,s),this.audio_seq_length=this.config.audio_seq_length,this.image_seq_length=this.config.image_seq_length;const{audio_token_id:r,boa_token:o,audio_token:n,eoa_token:a,image_token_id:i,boi_token:l,image_token:c,eoi_token:d}=this.tokenizer.config;this.audio_token_id=r,this.boa_token=o,this.audio_token=n;const u=n.repeat(this.audio_seq_length);this.full_audio_sequence=`\n\n${o}${u}${a}\n\n`,this.image_token_id=i,this.boi_token=l,this.image_token=c;const _=c.repeat(this.image_seq_length);this.full_image_sequence=`\n\n${l}${_}${d}\n\n`}async _call(e,t=null,s=null,r={}){let o,n;return"string"==typeof e&&(e=[e]),s&&(o=await this.feature_extractor(s,r),e=e.map((e=>e.replaceAll(this.audio_token,this.full_audio_sequence)))),t&&(n=await this.image_processor(t,r),e=e.map((e=>e.replaceAll(this.image_token,this.full_image_sequence)))),{...this.tokenizer(e,r),...n,...o}}}},"./src/models/glpn/image_processing_glpn.js":(e,t,s)=>{s.r(t),s.d(t,{GLPNFeatureExtractor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/grounding_dino/image_processing_grounding_dino.js":(e,t,s)=>{s.r(t),s.d(t,{GroundingDinoImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e){const t=await super._call(e),s=t.pixel_values.dims,r=(0,o.ones)([s[0],s[2],s[3]]);return{...t,pixel_mask:r}}}},"./src/models/grounding_dino/processing_grounding_dino.js":(e,t,s)=>{s.r(t),s.d(t,{GroundingDinoProcessor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),a=s("./src/base/image_processors_utils.js");function i(e,t){const s=e.dims.at(-1)-1,r=e.tolist();r.fill(!1,0,1),r.fill(!1,s);const o=t.tolist();return r.map(((e,t)=>e?t:null)).filter((e=>null!==e)).map((e=>o[e]))}class l extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;async _call(e,t,s={}){const r=e?await this.image_processor(e,s):{};return{...t?this.tokenizer(t,s):{},...r}}post_process_grounded_object_detection(e,t,{box_threshold:s=.25,text_threshold:r=.25,target_sizes:o=null}={}){const{logits:n,pred_boxes:l}=e,c=n.dims[0];if(null!==o&&o.length!==c)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");const d=n.dims.at(1),u=n.sigmoid(),_=u.max(-1).tolist(),m=l.tolist().map((e=>e.map((e=>(0,a.center_to_corners_format)(e))))),p=[];for(let e=0;e<c;++e){const n=null!==o?o[e]:null;null!==n&&(m[e]=m[e].map((e=>e.map(((e,t)=>e*n[(t+1)%2])))));const a=_[e],l=[],c=[],h=[];for(let o=0;o<d;++o){const n=a[o];if(n<=s)continue;const d=m[e][o],_=u[e][o];l.push(n),h.push(d);const p=i(_.gt(r),t[e]);c.push(p)}p.push({scores:l,boxes:h,labels:this.batch_decode(c)})}return p}}},"./src/models/idefics3/image_processing_idefics3.js":(e,t,s)=>{s.r(t),s.d(t,{Idefics3ImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{constructor(e){super(e),this.do_image_splitting=e.do_image_splitting??!0,this.max_image_size=e.max_image_size}get_resize_for_vision_encoder(e,t){let[s,r]=e.dims.slice(-2);const o=r/s;return r>=s?(r=Math.ceil(r/t)*t,s=Math.floor(r/o),s=Math.ceil(s/t)*t):(s=Math.ceil(s/t)*t,r=Math.floor(s*o),r=Math.ceil(r/t)*t),{height:s,width:r}}async _call(e,{do_image_splitting:t=null,return_row_col_info:s=!1}={}){let r;if(Array.isArray(e)){if(0===e.length||!e[0])throw new Error("No images provided.");r=Array.isArray(e[0])?e:[e]}else r=[[e]];let n=[],a=[],i=[];const l=[],c=[];for(const e of r){let s=await Promise.all(e.map((e=>this.preprocess(e))));l.push(...s.map((e=>e.original_size))),c.push(...s.map((e=>e.reshaped_input_size))),s.forEach((e=>e.pixel_values.unsqueeze_(0)));const{longest_edge:r}=this.max_image_size;let d;if(t??this.do_image_splitting){let e=new Array(s.length),t=new Array(s.length);d=await Promise.all(s.map((async(s,n)=>{const a=this.get_resize_for_vision_encoder(s.pixel_values,r),i=await(0,o.interpolate_4d)(s.pixel_values,{size:[a.height,a.width]}),{frames:l,num_splits_h:c,num_splits_w:d}=await this.split_image(i,this.max_image_size);return e[n]=c,t[n]=d,(0,o.cat)(l,0)}))),a.push(e),i.push(t)}else{const e=[r,r];d=await Promise.all(s.map((t=>(0,o.interpolate_4d)(t.pixel_values,{size:e})))),a.push(new Array(s.length).fill(0)),i.push(new Array(s.length).fill(0))}n.push((0,o.cat)(d,0))}const d=n.length,[u,_,m,p]=n[0].dims;let h,g;if(1===d)h=n[0].unsqueeze_(0),g=(0,o.full)([d,u,m,p],!0);else{const e=Math.max(...n.map((e=>e.dims.at(0))));g=(0,o.full)([d,e,m,p],!0);const t=g.data,s=e*m*p;for(let r=0;r<d;++r){const a=n[r].dims[0];if(a<e){n[r]=(0,o.cat)([n[r],(0,o.full)([e-a,_,m,p],0)],0);const i=r*s+a*m*p,l=(r+1)*s;t.fill(!1,i,l)}}h=(0,o.stack)(n,0)}return{pixel_values:h,pixel_attention_mask:g,original_sizes:l,reshaped_input_sizes:c,...s?{rows:a,cols:i}:{}}}async split_image(e,{longest_edge:t}){const s=t,r=t,n=[],[a,i]=e.dims.slice(-2);let l=0,c=0;if(a>s||i>r){l=Math.ceil(a/s),c=Math.ceil(i/r);const t=Math.ceil(a/l),d=Math.ceil(i/c);for(let s=0;s<l;++s)for(let r=0;r<c;++r){let u,_,m,p;s===l-1?(_=a-t,p=a):(_=s*t,p=(s+1)*t),r===c-1?(u=i-d,m=i):(u=r*d,m=(r+1)*d);const h=[_,u],g=[p,m],f=await(0,o.slice)(e,h,g,[2,3]);n.push(f)}const u=s,_=r;a===u&&i===_||(e=await(0,o.interpolate_4d)(e,{size:[u,_]}))}return n.push(e),{frames:n,num_splits_h:l,num_splits_w:c}}}},"./src/models/idefics3/processing_idefics3.js":(e,t,s)=>{s.r(t),s.d(t,{Idefics3Processor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),a=(s("./src/utils/image.js"),s("./src/utils/core.js"));function i(e,t,s,r,o,n){return 0===e&&0===t?function(e,t,s,r){return`${t}${r}`+s.repeat(e)+`${t}`}(s,r,o,n):function(e,t,s,r,o,n){let a="";for(let n=0;n<t;++n){for(let t=0;t<s;++t)a+=r+`<row_${n+1}_col_${t+1}>`+o.repeat(e);a+="\n"}return a+=`\n${r}${n}`+o.repeat(e)+`${r}`,a}(s,e,t,r,o,n)}class l extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;static uses_processor_config=!0;fake_image_token="<fake_token_around_image>";image_token="<image>";global_img_token="<global-img>";async _call(e,t=null,s={}){let r;s.return_row_col_info??=!0,t&&(r=await this.image_processor(t,s)),Array.isArray(e)||(e=[e]);const o=r.rows??[new Array(e.length).fill(0)],n=r.cols??[new Array(e.length).fill(0)],l=this.config.image_seq_len,c=[],d=[];for(let t=0;t<e.length;++t){const s=e[t],r=o[t],u=n[t];c.push((0,a.count)(s,this.image_token));const _=r.map(((e,t)=>i(e,u[t],l,this.fake_image_token,this.image_token,this.global_img_token))),m=s.split(this.image_token);if(0===m.length)throw new Error("The image token should be present in the text.");let p=m[0];for(let e=0;e<_.length;++e)p+=_[e]+m[e+1];d.push(p)}return{...this.tokenizer(d),...r}}}},"./src/models/image_processors.js":(e,t,s)=>{s.r(t),s.d(t,{BeitFeatureExtractor:()=>r.BeitFeatureExtractor,BitImageProcessor:()=>o.BitImageProcessor,CLIPFeatureExtractor:()=>a.CLIPFeatureExtractor,CLIPImageProcessor:()=>a.CLIPImageProcessor,ChineseCLIPFeatureExtractor:()=>n.ChineseCLIPFeatureExtractor,ConvNextFeatureExtractor:()=>i.ConvNextFeatureExtractor,ConvNextImageProcessor:()=>i.ConvNextImageProcessor,DINOv3ViTImageProcessor:()=>d.DINOv3ViTImageProcessor,DPTFeatureExtractor:()=>_.DPTFeatureExtractor,DPTImageProcessor:()=>_.DPTImageProcessor,DeiTFeatureExtractor:()=>l.DeiTFeatureExtractor,DeiTImageProcessor:()=>l.DeiTImageProcessor,DetrFeatureExtractor:()=>c.DetrFeatureExtractor,DetrImageProcessor:()=>c.DetrImageProcessor,DonutFeatureExtractor:()=>u.DonutFeatureExtractor,DonutImageProcessor:()=>u.DonutImageProcessor,EfficientNetImageProcessor:()=>m.EfficientNetImageProcessor,GLPNFeatureExtractor:()=>p.GLPNFeatureExtractor,GroundingDinoImageProcessor:()=>h.GroundingDinoImageProcessor,Idefics3ImageProcessor:()=>g.Idefics3ImageProcessor,JinaCLIPImageProcessor:()=>M.JinaCLIPImageProcessor,LlavaOnevisionImageProcessor:()=>w.LlavaOnevisionImageProcessor,Mask2FormerImageProcessor:()=>T.Mask2FormerImageProcessor,MaskFormerFeatureExtractor:()=>b.MaskFormerFeatureExtractor,MaskFormerImageProcessor:()=>b.MaskFormerImageProcessor,MobileNetV1FeatureExtractor:()=>x.MobileNetV1FeatureExtractor,MobileNetV1ImageProcessor:()=>x.MobileNetV1ImageProcessor,MobileNetV2FeatureExtractor:()=>P.MobileNetV2FeatureExtractor,MobileNetV2ImageProcessor:()=>P.MobileNetV2ImageProcessor,MobileNetV3FeatureExtractor:()=>k.MobileNetV3FeatureExtractor,MobileNetV3ImageProcessor:()=>k.MobileNetV3ImageProcessor,MobileNetV4FeatureExtractor:()=>F.MobileNetV4FeatureExtractor,MobileNetV4ImageProcessor:()=>F.MobileNetV4ImageProcessor,MobileViTFeatureExtractor:()=>v.MobileViTFeatureExtractor,MobileViTImageProcessor:()=>v.MobileViTImageProcessor,NougatImageProcessor:()=>y.NougatImageProcessor,OwlViTFeatureExtractor:()=>S.OwlViTFeatureExtractor,OwlViTImageProcessor:()=>S.OwlViTImageProcessor,Owlv2ImageProcessor:()=>C.Owlv2ImageProcessor,Phi3VImageProcessor:()=>A.Phi3VImageProcessor,PvtImageProcessor:()=>E.PvtImageProcessor,Qwen2VLImageProcessor:()=>L.Qwen2VLImageProcessor,RTDetrImageProcessor:()=>I.RTDetrImageProcessor,SamImageProcessor:()=>z.SamImageProcessor,SegformerFeatureExtractor:()=>D.SegformerFeatureExtractor,SegformerImageProcessor:()=>D.SegformerImageProcessor,SiglipImageProcessor:()=>j.SiglipImageProcessor,SmolVLMImageProcessor:()=>V.SmolVLMImageProcessor,Swin2SRImageProcessor:()=>N.Swin2SRImageProcessor,VLMImageProcessor:()=>f.VLMImageProcessor,ViTFeatureExtractor:()=>O.ViTFeatureExtractor,ViTImageProcessor:()=>O.ViTImageProcessor,VitMatteImageProcessor:()=>B.VitMatteImageProcessor,VitPoseImageProcessor:()=>G.VitPoseImageProcessor,YolosFeatureExtractor:()=>R.YolosFeatureExtractor,YolosImageProcessor:()=>R.YolosImageProcessor});var r=s("./src/models/beit/image_processing_beit.js"),o=s("./src/models/bit/image_processing_bit.js"),n=s("./src/models/chinese_clip/image_processing_chinese_clip.js"),a=s("./src/models/clip/image_processing_clip.js"),i=s("./src/models/convnext/image_processing_convnext.js"),l=s("./src/models/deit/image_processing_deit.js"),c=s("./src/models/detr/image_processing_detr.js"),d=s("./src/models/dinov3_vit/image_processing_dinov3_vit.js"),u=s("./src/models/donut/image_processing_donut.js"),_=s("./src/models/dpt/image_processing_dpt.js"),m=s("./src/models/efficientnet/image_processing_efficientnet.js"),p=s("./src/models/glpn/image_processing_glpn.js"),h=s("./src/models/grounding_dino/image_processing_grounding_dino.js"),g=s("./src/models/idefics3/image_processing_idefics3.js"),f=s("./src/models/janus/image_processing_janus.js"),M=s("./src/models/jina_clip/image_processing_jina_clip.js"),w=s("./src/models/llava_onevision/image_processing_llava_onevision.js"),T=s("./src/models/mask2former/image_processing_mask2former.js"),b=s("./src/models/maskformer/image_processing_maskformer.js"),x=s("./src/models/mobilenet_v1/image_processing_mobilenet_v1.js"),P=s("./src/models/mobilenet_v2/image_processing_mobilenet_v2.js"),k=s("./src/models/mobilenet_v3/image_processing_mobilenet_v3.js"),F=s("./src/models/mobilenet_v4/image_processing_mobilenet_v4.js"),v=s("./src/models/mobilevit/image_processing_mobilevit.js"),y=s("./src/models/nougat/image_processing_nougat.js"),C=s("./src/models/owlv2/image_processing_owlv2.js"),S=s("./src/models/owlvit/image_processing_owlvit.js"),A=s("./src/models/phi3_v/image_processing_phi3_v.js"),E=s("./src/models/pvt/image_processing_pvt.js"),L=s("./src/models/qwen2_vl/image_processing_qwen2_vl.js"),I=s("./src/models/rt_detr/image_processing_rt_detr.js"),z=s("./src/models/sam/image_processing_sam.js"),D=s("./src/models/segformer/image_processing_segformer.js"),j=s("./src/models/siglip/image_processing_siglip.js"),V=s("./src/models/smolvlm/image_processing_smolvlm.js"),N=s("./src/models/swin2sr/image_processing_swin2sr.js"),O=s("./src/models/vit/image_processing_vit.js"),B=s("./src/models/vitmatte/image_processing_vitmatte.js"),G=s("./src/models/vitpose/image_processing_vitpose.js"),R=s("./src/models/yolos/image_processing_yolos.js")},"./src/models/janus/image_processing_janus.js":(e,t,s)=>{s.r(t),s.d(t,{VLMImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){super({do_pad:!0,pad_size:{width:e.image_size,height:e.image_size},...e}),this.constant_values=this.config.background_color.map((e=>e*this.rescale_factor))}pad_image(e,t,s,r){return super.pad_image(e,t,s,{constant_values:this.constant_values,center:!0,...r})}}},"./src/models/janus/processing_janus.js":(e,t,s)=>{s.r(t),s.d(t,{VLChatProcessor:()=>c});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),a=s("./src/utils/core.js"),i=s("./src/utils/tensor.js"),l=s("./src/utils/image.js");class c extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;static uses_processor_config=!0;constructor(e,t,s){super(e,t,s),this.image_tag=this.config.image_tag,this.image_start_tag=this.config.image_start_tag,this.image_end_tag=this.config.image_end_tag,this.num_image_tokens=this.config.num_image_tokens}async _call(e,{images:t=null,chat_template:s="default"}={}){t?Array.isArray(t)||(t=[t]):t=await Promise.all(e.filter((e=>e.images)).flatMap((e=>e.images)).map((e=>l.RawImage.read(e))));const r=this.tokenizer,o=e=>r.encode(e,{add_special_tokens:!1}),n=r.apply_chat_template(e,{tokenize:!1,add_generation_prompt:!0,chat_template:s}).split(this.image_tag),c=n.length-1;if(t.length!==c)throw new Error(`Number of images provided (${t.length}) does not match number of "${this.image_tag}" image tags (${c})`);const[d,u,_]=r.model.convert_tokens_to_ids([this.image_tag,this.image_start_tag,this.image_end_tag]);let m=o(n[0]),p=new Array(m.length).fill(!1);for(let e=1;e<n.length;++e){const t=new Array(this.num_image_tokens).fill(d),s=o(n[e]);m=(0,a.mergeArrays)(m,[u],t,[_],s);const r=new Array(this.num_image_tokens).fill(!0);p=(0,a.mergeArrays)(p,[!1],r,[!1],new Array(s.length).fill(!1))}const h=[1,m.length],g={input_ids:new i.Tensor("int64",m,h),attention_mask:new i.Tensor("int64",new Array(m.length).fill(1),h),images_seq_mask:new i.Tensor("bool",p,h),images_emb_mask:new i.Tensor("bool",new Array(c*this.num_image_tokens).fill(!0),[1,c,this.num_image_tokens])};if(t&&t.length>0){const e=await this.image_processor(t);return e.pixel_values.unsqueeze_(0),{...g,...e}}return g}}},"./src/models/jina_clip/image_processing_jina_clip.js":(e,t,s)=>{s.r(t),s.d(t,{JinaCLIPImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){const{resize_mode:t,fill_color:s,interpolation:r,size:o,...n}=e;super({...n,size:"squash"===t?{width:o,height:o}:"shortest"===t?{shortest_edge:o}:{longest_edge:o},resample:"bicubic"===r?3:2,do_center_crop:!0,crop_size:o,do_normalize:!0})}}},"./src/models/jina_clip/processing_jina_clip.js":(e,t,s)=>{s.r(t),s.d(t,{JinaCLIPProcessor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class a extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;async _call(e=null,t=null,s={}){if(!e&&!t)throw new Error("Either text or images must be provided");return{...e?this.tokenizer(e,s):{},...t?await this.image_processor(t,s):{}}}}},"./src/models/llava/processing_llava.js":(e,t,s)=>{s.r(t),s.d(t,{LlavaProcessor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class a extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;static uses_processor_config=!0;async _call(e,t=null,s={}){const r=await this.image_processor(e,s);if(t){const[e,s]=r.pixel_values.dims.slice(-2),{image_token:o,patch_size:n,num_additional_image_tokens:a}=this.config,i=Math.floor(e/n)*Math.floor(s/n)+a;t=structuredClone(t),Array.isArray(t)||(t=[t]);for(let e=0;e<t.length;++e)t[e]=t[e].replace(o,o.repeat(i))}const o=t?this.tokenizer(t,s):{};return{...r,...o}}}},"./src/models/llava_onevision/image_processing_llava_onevision.js":(e,t,s)=>{s.r(t),s.d(t,{LlavaOnevisionImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/mask2former/image_processing_mask2former.js":(e,t,s)=>{s.r(t),s.d(t,{Mask2FormerImageProcessor:()=>o});var r=s("./src/models/maskformer/image_processing_maskformer.js");class o extends r.MaskFormerImageProcessor{}},"./src/models/maskformer/image_processing_maskformer.js":(e,t,s)=>{s.r(t),s.d(t,{MaskFormerFeatureExtractor:()=>n,MaskFormerImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_panoptic_segmentation(...e){return(0,r.post_process_panoptic_segmentation)(...e)}post_process_instance_segmentation(...e){return(0,r.post_process_instance_segmentation)(...e)}}class n extends o{}},"./src/models/mgp_str/processing_mgp_str.js":(e,t,s)=>{s.r(t),s.d(t,{MgpstrProcessor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),a=s("./src/utils/maths.js");const i={char:["char_decode",1],bpe:["bpe_decode",2],wp:["wp_decode",102]};class l extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;get char_tokenizer(){return this.components.char_tokenizer}get bpe_tokenizer(){return this.components.bpe_tokenizer}get wp_tokenizer(){return this.components.wp_tokenizer}_decode_helper(e,t){if(!i.hasOwnProperty(t))throw new Error(`Format ${t} is not supported.`);const[s,r]=i[t],o=this[s].bind(this),[n,l]=e.dims,c=[],d=[],u=e.tolist();for(let e=0;e<n;++e){const t=u[e],s=[],o=[];for(let e=1;e<l;++e){const[n,i]=(0,a.max)((0,a.softmax)(t[e]));if(o.push(n),i==r)break;s.push(i)}const n=o.length>0?o.reduce(((e,t)=>e*t),1):0;d.push(s),c.push(n)}return[o(d),c]}char_decode(e){return this.char_tokenizer.batch_decode(e).map((e=>e.replaceAll(" ","")))}bpe_decode(e){return this.bpe_tokenizer.batch_decode(e)}wp_decode(e){return this.wp_tokenizer.batch_decode(e).map((e=>e.replaceAll(" ","")))}batch_decode([e,t,s]){const[r,o]=this._decode_helper(e,"char"),[n,i]=this._decode_helper(t,"bpe"),[l,c]=this._decode_helper(s,"wp"),d=[],u=[];for(let e=0;e<r.length;++e){const[t,s]=(0,a.max)([o[e],i[e],c[e]]);d.push([r[e],n[e],l[e]][s]),u.push(t)}return{generated_text:d,scores:u,char_preds:r,bpe_preds:n,wp_preds:l}}static async from_pretrained(...e){const t=await super.from_pretrained(...e),s=await n.AutoTokenizer.from_pretrained("Xenova/gpt2"),r=await n.AutoTokenizer.from_pretrained("Xenova/bert-base-uncased");return t.components={image_processor:t.image_processor,char_tokenizer:t.tokenizer,bpe_tokenizer:s,wp_tokenizer:r},t}async _call(e,t=null){const s=await this.image_processor(e);return t&&(s.labels=this.tokenizer(t).input_ids),s}}},"./src/models/mobilenet_v1/image_processing_mobilenet_v1.js":(e,t,s)=>{s.r(t),s.d(t,{MobileNetV1FeatureExtractor:()=>n,MobileNetV1ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilenet_v2/image_processing_mobilenet_v2.js":(e,t,s)=>{s.r(t),s.d(t,{MobileNetV2FeatureExtractor:()=>n,MobileNetV2ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilenet_v3/image_processing_mobilenet_v3.js":(e,t,s)=>{s.r(t),s.d(t,{MobileNetV3FeatureExtractor:()=>n,MobileNetV3ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilenet_v4/image_processing_mobilenet_v4.js":(e,t,s)=>{s.r(t),s.d(t,{MobileNetV4FeatureExtractor:()=>n,MobileNetV4ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilevit/image_processing_mobilevit.js":(e,t,s)=>{s.r(t),s.d(t,{MobileViTFeatureExtractor:()=>n,MobileViTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/moonshine/feature_extraction_moonshine.js":(e,t,s)=>{s.r(t),s.d(t,{MoonshineFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js");class n extends r.FeatureExtractor{async _call(e){(0,r.validate_audio_inputs)(e,"MoonshineFeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));const t=[1,e.length];return{input_values:new o.Tensor("float32",e,t)}}}},"./src/models/moonshine/processing_moonshine.js":(e,t,s)=>{s.r(t),s.d(t,{MoonshineProcessor:()=>a});var r=s("./src/models/auto/feature_extraction_auto.js"),o=s("./src/tokenizers.js"),n=s("./src/base/processing_utils.js");class a extends n.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=r.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/nougat/image_processing_nougat.js":(e,t,s)=>{s.r(t),s.d(t,{NougatImageProcessor:()=>o});var r=s("./src/models/donut/image_processing_donut.js");class o extends r.DonutImageProcessor{}},"./src/models/owlv2/image_processing_owlv2.js":(e,t,s)=>{s.r(t),s.d(t,{Owlv2ImageProcessor:()=>o});var r=s("./src/models/owlvit/image_processing_owlvit.js");class o extends r.OwlViTImageProcessor{}},"./src/models/owlvit/image_processing_owlvit.js":(e,t,s)=>{s.r(t),s.d(t,{OwlViTFeatureExtractor:()=>n,OwlViTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}}class n extends o{}},"./src/models/owlvit/processing_owlvit.js":(e,t,s)=>{s.r(t),s.d(t,{OwlViTProcessor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class a extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor}},"./src/models/paligemma/processing_paligemma.js":(e,t,s)=>{s.r(t),s.d(t,{PaliGemmaProcessor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");const a="<image>";class i extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;static uses_processor_config=!1;async _call(e,t=null,s={}){t||(console.warn("You are using PaliGemma without a text prefix. It will perform as a picture-captioning model."),t=""),Array.isArray(e)||(e=[e]),Array.isArray(t)||(t=[t]);const r=this.tokenizer.bos_token,o=this.image_processor.config.image_seq_length;let n;t.some((e=>e.includes(a)))?n=t.map((e=>{const t=e.replaceAll(a,a.repeat(o)),s=t.lastIndexOf(a),n=-1===s?0:s+7;return t.slice(0,n)+r+t.slice(n)+"\n"})):(console.warn("You are passing both `text` and `images` to `PaliGemmaProcessor`. The processor expects special image tokens in the text, as many tokens as there are images per each text. It is recommended to add `<image>` tokens in the very beginning of your text. For this call, we will infer how many images each text has and add special tokens."),n=t.map((t=>function(e,t,s,r,o){return`${r.repeat(s*o)}${t}${e}\n`}(t,r,o,a,e.length))));const i=this.tokenizer(n,s);return{...await this.image_processor(e,s),...i}}}},"./src/models/phi3_v/image_processing_phi3_v.js":(e,t,s)=>{s.r(t),s.d(t,{Phi3VImageProcessor:()=>d});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");const n=336,a=[2,3],{ceil:i,floor:l,sqrt:c}=Math;class d extends r.ImageProcessor{constructor(e){super({...e,do_normalize:!0,do_pad:!0,pad_size:"custom",do_convert_rgb:!0,do_resize:!0}),this._num_crops=e.num_crops}calc_num_image_tokens_from_image_size(e,t){const{num_img_tokens:s}=this.config;return l((l(t/n)*l(e/n)+1)*s+1+(l(t/n)+1)*c(s))}get_resize_output_image_size(e,t){const s=this._num_crops,[r,o]=e.size;let n=r/o,a=1;for(;a*Math.ceil(a/n)<=s;)a+=1;a-=1;const i=Math.floor(336*a);return[i,Math.floor(i/n)]}pad_image(e,t,s,r={}){const[o,a]=t,l=n*i(o/n),c=n*i(a/n),d=[1,1,1].map(((e,t)=>(e-this.image_mean[t])/this.image_std[t]));return super.pad_image(e,t,{width:c,height:l},{center:!0,constant_values:d,...r})}async _call(e,{num_crops:t=null}={}){if(this._num_crops=t??=this.config.num_crops,t<4||c(t)%1!=0)throw new Error("num_crops must be a square number >= 4");Array.isArray(e)||(e=[e]);const s=e.length,r=await Promise.all(e.map((e=>this.preprocess(e)))),d=r.map((e=>e.original_size)),u=r.map((e=>e.reshaped_input_size)),_=[];for(const{pixel_values:e}of r){e.unsqueeze_(0);const[s,r]=e.dims.slice(-2),i=await(0,o.interpolate_4d)(e,{size:[n,n],mode:"bicubic"});if(t>0){const d=[],u=c(t),m=l(r/u),p=l(s/u);for(let t=0;t<u;++t)for(let n=0;n<u;++n){let i,l,c,_;t===u-1?(l=s-p,_=s):(l=t*p,_=(t+1)*p),n===u-1?(i=r-m,c=r):(i=n*m,c=(n+1)*m);const h=[l,i],g=[_,c],f=await(0,o.slice)(e,h,g,a);d.push(f)}const h=await(0,o.interpolate_4d)((0,o.cat)(d,0),{size:[n,n],mode:"bicubic"});_.push((0,o.cat)([i,h],0))}else _.push(i)}const m=(0,o.stack)(_,0),p=u.map((e=>e.map((e=>n*i(e/n)))));return{pixel_values:m,original_sizes:d,reshaped_input_sizes:u,image_sizes:new o.Tensor("int64",p.flat(),[s,2]),num_img_tokens:p.map((([e,t])=>this.calc_num_image_tokens_from_image_size(t,e)))}}}},"./src/models/phi3_v/processing_phi3_v.js":(e,t,s)=>{s.r(t),s.d(t,{Phi3VProcessor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");s("./src/utils/image.js");const a="<|image|>",i=/<\|image_\d+\|>/g;class l extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;async _call(e,t=null,{padding:s=!0,truncation:r=!0,num_crops:o=null}={}){let n,l;if(Array.isArray(e)||(e=[e]),t){l=await this.image_processor(t,{num_crops:o});const{num_img_tokens:c}=l,d=e.map(((e,t)=>e.split(i).join(a.repeat(c[t]))));n=this.tokenizer(d,{padding:s,truncation:r});const u=this.tokenizer.model.convert_tokens_to_ids([a])[0];n.input_ids.map_((e=>e==u?-e:e))}else n=this.tokenizer(e);return{...n,...l}}}},"./src/models/processors.js":(e,t,s)=>{s.r(t),s.d(t,{Florence2Processor:()=>r.Florence2Processor,Gemma3nProcessor:()=>o.Gemma3nProcessor,GroundingDinoProcessor:()=>n.GroundingDinoProcessor,Idefics3Processor:()=>a.Idefics3Processor,JinaCLIPProcessor:()=>l.JinaCLIPProcessor,LlavaProcessor:()=>c.LlavaProcessor,MgpstrProcessor:()=>d.MgpstrProcessor,MoonshineProcessor:()=>u.MoonshineProcessor,OwlViTProcessor:()=>_.OwlViTProcessor,PaliGemmaProcessor:()=>p.PaliGemmaProcessor,Phi3VProcessor:()=>m.Phi3VProcessor,PyAnnoteProcessor:()=>h.PyAnnoteProcessor,Qwen2VLProcessor:()=>g.Qwen2VLProcessor,SamProcessor:()=>f.SamProcessor,SmolVLMProcessor:()=>M.SmolVLMProcessor,SpeechT5Processor:()=>w.SpeechT5Processor,UltravoxProcessor:()=>T.UltravoxProcessor,VLChatProcessor:()=>i.VLChatProcessor,VoxtralProcessor:()=>b.VoxtralProcessor,Wav2Vec2Processor:()=>x.Wav2Vec2Processor,Wav2Vec2ProcessorWithLM:()=>P.Wav2Vec2ProcessorWithLM,WhisperProcessor:()=>k.WhisperProcessor});var r=s("./src/models/florence2/processing_florence2.js"),o=s("./src/models/gemma3n/processing_gemma3n.js"),n=s("./src/models/grounding_dino/processing_grounding_dino.js"),a=s("./src/models/idefics3/processing_idefics3.js"),i=s("./src/models/janus/processing_janus.js"),l=s("./src/models/jina_clip/processing_jina_clip.js"),c=s("./src/models/llava/processing_llava.js"),d=s("./src/models/mgp_str/processing_mgp_str.js"),u=s("./src/models/moonshine/processing_moonshine.js"),_=s("./src/models/owlvit/processing_owlvit.js"),m=s("./src/models/phi3_v/processing_phi3_v.js"),p=s("./src/models/paligemma/processing_paligemma.js"),h=s("./src/models/pyannote/processing_pyannote.js"),g=s("./src/models/qwen2_vl/processing_qwen2_vl.js"),f=s("./src/models/sam/processing_sam.js"),M=s("./src/models/smolvlm/processing_smolvlm.js"),w=s("./src/models/speecht5/processing_speecht5.js"),T=s("./src/models/ultravox/processing_ultravox.js"),b=s("./src/models/voxtral/processing_voxtral.js"),x=s("./src/models/wav2vec2/processing_wav2vec2.js"),P=s("./src/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.js"),k=s("./src/models/whisper/processing_whisper.js")},"./src/models/pvt/image_processing_pvt.js":(e,t,s)=>{s.r(t),s.d(t,{PvtImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/pyannote/feature_extraction_pyannote.js":(e,t,s)=>{s.r(t),s.d(t,{PyAnnoteFeatureExtractor:()=>a});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/maths.js");class a extends r.FeatureExtractor{async _call(e){(0,r.validate_audio_inputs)(e,"PyAnnoteFeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));const t=[1,1,e.length];return{input_values:new o.Tensor("float32",e,t)}}samples_to_frames(e){return(e-this.config.offset)/this.config.step}post_process_speaker_diarization(e,t){const s=t/this.samples_to_frames(t)/this.config.sampling_rate,r=[];for(const t of e.tolist()){const e=[];let o=-1;for(let s=0;s<t.length;++s){const r=(0,n.softmax)(t[s]),[a,i]=(0,n.max)(r),[l,c]=[s,s+1];i!==o?(o=i,e.push({id:i,start:l,end:c,score:a})):(e.at(-1).end=c,e.at(-1).score+=a)}r.push(e.map((({id:e,start:t,end:r,score:o})=>({id:e,start:t*s,end:r*s,confidence:o/(r-t)}))))}return r}}},"./src/models/pyannote/processing_pyannote.js":(e,t,s)=>{s.r(t),s.d(t,{PyAnnoteProcessor:()=>n});var r=s("./src/base/processing_utils.js"),o=s("./src/models/pyannote/feature_extraction_pyannote.js");class n extends r.Processor{static feature_extractor_class=o.PyAnnoteFeatureExtractor;async _call(e){return await this.feature_extractor(e)}post_process_speaker_diarization(...e){return this.feature_extractor.post_process_speaker_diarization(...e)}get sampling_rate(){return this.feature_extractor.config.sampling_rate}}},"./src/models/qwen2_vl/image_processing_qwen2_vl.js":(e,t,s)=>{s.r(t),s.d(t,{Qwen2VLImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e,...t){const{pixel_values:s,original_sizes:r,reshaped_input_sizes:n}=await super._call(e,...t);let a=s;const{temporal_patch_size:i,merge_size:l,patch_size:c}=this.config;1===a.dims[0]&&(a=(0,o.cat)(Array.from({length:i},(()=>a)),0));const d=a.dims[0]/i,u=a.dims[1],_=Math.floor(a.dims[2]/c),m=Math.floor(a.dims[3]/c);return{pixel_values:a.view(d,i,u,Math.floor(_/l),l,c,Math.floor(m/l),l,c).permute(0,3,6,4,7,2,1,5,8).view(d*_*m,u*i*c*c),image_grid_thw:new o.Tensor("int64",[d,_,m],[1,3]),original_sizes:r,reshaped_input_sizes:n}}}},"./src/models/qwen2_vl/processing_qwen2_vl.js":(e,t,s)=>{s.r(t),s.d(t,{Qwen2VLProcessor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");s("./src/utils/image.js");class a extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;async _call(e,t=null,...s){let r,o;if(Array.isArray(e)||(e=[e]),t&&(r=await this.image_processor(t),o=r.image_grid_thw),o){let t=this.image_processor.config.merge_size**2,s=0;const r=o.tolist();e=e.map((e=>{for(;e.includes("<|image_pad|>");){const o=Number(r[s++].reduce(((e,t)=>e*t),1n));e=e.replace("<|image_pad|>","<|placeholder|>".repeat(Math.floor(o/t)))}return e.replaceAll("<|placeholder|>","<|image_pad|>")}))}return{...this.tokenizer(e),...r}}}},"./src/models/rt_detr/image_processing_rt_detr.js":(e,t,s)=>{s.r(t),s.d(t,{RTDetrImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}}},"./src/models/sam/image_processing_sam.js":(e,t,s)=>{s.r(t),s.d(t,{SamImageProcessor:()=>a});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/core.js"),n=s("./src/utils/tensor.js");class a extends r.ImageProcessor{reshape_input_points(e,t,s,r=!1){e=structuredClone(e);let a=(0,o.calculateDimensions)(e);if(3===a.length)r||(a=[1,...a]),e=[e];else if(4!==a.length)throw Error("The input_points must be a 4D tensor of shape `batch_size`, `point_batch_size`, `nb_points_per_image`, `2`.");for(let r=0;r<e.length;++r){let o=t[r],n=s[r],a=[n[0]/o[0],n[1]/o[1]];for(let t=0;t<e[r].length;++t)for(let s=0;s<e[r][t].length;++s)for(let o=0;o<e[r][t][s].length;++o)e[r][t][s][o]*=a[o%2]}return new n.Tensor("float32",Float32Array.from(e.flat(1/0)),a)}add_input_labels(e,t){let s=(0,o.calculateDimensions)(e);if(2===s.length)s=[1,...s],e=[e];else if(3!==s.length)throw Error("The input_points must be a 4D tensor of shape `batch_size`, `point_batch_size`, `nb_points_per_image`, `2`.");if(s.some(((e,s)=>e!==t.dims[s])))throw Error(`The first ${s.length} dimensions of 'input_points' and 'input_labels' must be the same.`);return new n.Tensor("int64",e.flat(1/0).map(BigInt),s)}async _call(e,{input_points:t=null,input_labels:s=null,input_boxes:r=null}={}){const o=await super._call(e);if(t&&(o.input_points=this.reshape_input_points(t,o.original_sizes,o.reshaped_input_sizes)),s){if(!o.input_points)throw Error("`input_points` must be provided if `input_labels` are provided.");o.input_labels=this.add_input_labels(s,o.input_points)}return r&&(o.input_boxes=this.reshape_input_points(r,o.original_sizes,o.reshaped_input_sizes,!0)),o}async post_process_masks(e,t,s,{mask_threshold:r=0,binarize:o=!0,pad_size:a=null}={}){const i=[],l=[(a=a??this.pad_size).height,a.width];for(let a=0;a<t.length;++a){const c=t[a],d=s[a];let u=await(0,n.interpolate_4d)(e[a],{mode:"bilinear",size:l});if(u=u.slice(null,null,[0,d[0]],[0,d[1]]),u=await(0,n.interpolate_4d)(u,{mode:"bilinear",size:c}),o){const e=u.data,t=new Uint8Array(e.length);for(let s=0;s<e.length;++s)e[s]>r&&(t[s]=1);u=new n.Tensor("bool",t,u.dims)}i.push(u)}return i}generate_crop_boxes(e,t,{crop_n_layers:s=0,overlap_ratio:r=512/1500,points_per_crop:o=32,crop_n_points_downscale_factor:n=1}={}){}}},"./src/models/sam/processing_sam.js":(e,t,s)=>{s.r(t),s.d(t,{SamProcessor:()=>n});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js");class n extends r.Processor{static image_processor_class=o.AutoImageProcessor;async _call(...e){return await this.image_processor(...e)}post_process_masks(...e){return this.image_processor.post_process_masks(...e)}reshape_input_points(...e){return this.image_processor.reshape_input_points(...e)}}},"./src/models/seamless_m4t/feature_extraction_seamless_m4t.js":(e,t,s)=>{s.r(t),s.d(t,{SeamlessM4TFeatureExtractor:()=>a});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/audio.js");class a extends r.FeatureExtractor{constructor(e){super(e);const t=this.config.sampling_rate,s=(0,n.mel_filter_bank)(257,this.config.num_mel_bins,20,Math.floor(t/2),t,null,"kaldi",!0);this.mel_filters=s,this.window=(0,n.window_function)(400,"povey",{periodic:!1})}async _extract_fbank_features(e,t){return e=e.map((e=>32768*e)),(0,n.spectrogram)(e,this.window,400,160,{fft_length:512,power:2,center:!1,preemphasis:.97,mel_filters:this.mel_filters,log_mel:"log",mel_floor:1.192092955078125e-7,remove_dc_offset:!0,max_num_frames:t,transpose:!0})}async _call(e,{padding:t=!0,pad_to_multiple_of:s=2,do_normalize_per_mel_bins:n=!0,return_attention_mask:a=!0}={}){(0,r.validate_audio_inputs)(e,"SeamlessM4TFeatureExtractor");let i,l=await this._extract_fbank_features(e,this.config.max_length);if(n){const[e,t]=l.dims,s=l.data;for(let r=0;r<t;++r){let o=0;for(let n=0;n<e;++n)o+=s[n*t+r];const n=o/e;let a=0;for(let o=0;o<e;++o)a+=(s[o*t+r]-n)**2;a/=e-1;const i=Math.sqrt(a+1e-7);for(let o=0;o<e;++o){const e=o*t+r;s[e]=(s[e]-n)/i}}}if(t){const[e,t]=l.dims,r=l.data,n=e%s;if(n>0){const s=new Float32Array(t*(e+n));s.set(r),s.fill(this.config.padding_value,r.length);const c=e+n;l=new o.Tensor(l.type,s,[c,t]),a&&(i=new o.Tensor("int64",new BigInt64Array(c),[1,c]),i.data.fill(1n,0,e))}}const[c,d]=l.dims,u=this.config.stride;if(0!==c%u)throw new Error(`The number of frames (${c}) must be a multiple of the stride (${u}).`);const _=l.view(1,Math.floor(c/u),d*u),m={input_features:_};if(a){const e=_.dims[1],t=new BigInt64Array(e);if(i){const e=i.data;for(let s=1,r=0;s<c;s+=u,++r)t[r]=e[s]}else t.fill(1n);m.attention_mask=new o.Tensor("int64",t,[1,e])}return m}}},"./src/models/segformer/image_processing_segformer.js":(e,t,s)=>{s.r(t),s.d(t,{SegformerFeatureExtractor:()=>n,SegformerImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_semantic_segmentation(...e){return(0,r.post_process_semantic_segmentation)(...e)}}class n extends o{}},"./src/models/siglip/image_processing_siglip.js":(e,t,s)=>{s.r(t),s.d(t,{SiglipImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/smolvlm/image_processing_smolvlm.js":(e,t,s)=>{s.r(t),s.d(t,{SmolVLMImageProcessor:()=>r.Idefics3ImageProcessor});var r=s("./src/models/idefics3/image_processing_idefics3.js")},"./src/models/smolvlm/processing_smolvlm.js":(e,t,s)=>{s.r(t),s.d(t,{SmolVLMProcessor:()=>r.Idefics3Processor});var r=s("./src/models/idefics3/processing_idefics3.js")},"./src/models/snac/feature_extraction_snac.js":(e,t,s)=>{s.r(t),s.d(t,{SnacFeatureExtractor:()=>o});var r=s("./src/models/dac/feature_extraction_dac.js");class o extends r.DacFeatureExtractor{}},"./src/models/speecht5/feature_extraction_speecht5.js":(e,t,s)=>{s.r(t),s.d(t,{SpeechT5FeatureExtractor:()=>o});var r=s("./src/base/feature_extraction_utils.js");class o extends r.FeatureExtractor{}},"./src/models/speecht5/processing_speecht5.js":(e,t,s)=>{s.r(t),s.d(t,{SpeechT5Processor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/tokenizers.js"),n=s("./src/models/auto/feature_extraction_auto.js");class a extends r.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=n.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/swin2sr/image_processing_swin2sr.js":(e,t,s)=>{s.r(t),s.d(t,{Swin2SRImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{pad_image(e,t,s,r={}){const[o,n,a]=t;return super.pad_image(e,t,{width:n+(s-n%s)%s,height:o+(s-o%s)%s},{mode:"symmetric",center:!1,constant_values:-1,...r})}}},"./src/models/ultravox/processing_ultravox.js":(e,t,s)=>{s.r(t),s.d(t,{UltravoxProcessor:()=>a});var r=s("./src/models/auto/feature_extraction_auto.js"),o=s("./src/tokenizers.js"),n=s("./src/base/processing_utils.js");class a extends n.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=r.AutoFeatureExtractor;static uses_processor_config=!0;async _call(e,t=null,s={}){if(Array.isArray(e))throw new Error("Batched inputs are not supported yet.");let r={};if(t){const o=t.length,{input_features:n}=await this.feature_extractor(t,{...s,max_length:o}),a=Math.round(o/this.config.encoder_ds_factor+1e-4),i=1+Math.ceil(a/this.config.stack_factor);r.audio_token_len=[i],r.audio_values=n;const l=this.config.audio_placeholder;if(!e.includes(l))throw new Error(`The input text does not contain the image token ${l}.`);e=e.replaceAll(l,l.repeat(i))}return{...this.tokenizer(e,{add_special_tokens:!1,...s}),...r}}}},"./src/models/vit/image_processing_vit.js":(e,t,s)=>{s.r(t),s.d(t,{ViTFeatureExtractor:()=>n,ViTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/vitmatte/image_processing_vitmatte.js":(e,t,s)=>{s.r(t),s.d(t,{VitMatteImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e,t){Array.isArray(e)||(e=[e]),Array.isArray(t)||(t=[t]);const s=await Promise.all(e.map((e=>this.preprocess(e)))),r=await Promise.all(t.map((e=>this.preprocess(e,{do_normalize:!1,do_convert_rgb:!1,do_convert_grayscale:!0}))));return{pixel_values:(0,o.stack)(s.map(((e,t)=>(0,o.cat)([e.pixel_values,r[t].pixel_values],0))),0),original_sizes:s.map((e=>e.original_size)),reshaped_input_sizes:s.map((e=>e.reshaped_input_size))}}}},"./src/models/vitpose/image_processing_vitpose.js":(e,t,s)=>{s.r(t),s.d(t,{VitPoseImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_pose_estimation(e,t,{threshold:s=null}={}){const r=e.tolist(),[o,n,a,i]=e.dims,l=[];for(let e=0;e<o;++e){const o=r[e],n=t[e],c=[];for(let e=0;e<n.length;++e){const t=n[e],r=[],l=[],d=[],u=t.at(-2)/i,_=t.at(-1)/a;for(let e=0;e<o.length;++e){let[t,n]=[0,0],a=0,i=-1/0;const c=o[e];for(let e=0;e<c.length;++e){const s=c[e];for(let r=0;r<s.length;++r){const o=s[r];a+=o,i=Math.max(i,o),t+=(r+.5)*o,n+=e*o}}if(null!=s&&i<s)continue;const m=[u*t/a,_*n/a];r.push(m),d.push(e),l.push(i)}c.push({bbox:t,scores:l,labels:d,keypoints:r})}l.push(c)}return l}}},"./src/models/voxtral/processing_voxtral.js":(e,t,s)=>{s.r(t),s.d(t,{VoxtralProcessor:()=>l});var r=s("./src/models/auto/feature_extraction_auto.js"),o=s("./src/tokenizers.js"),n=s("./src/base/processing_utils.js"),a=s("./src/utils/tensor.js");const i="[AUDIO]";class l extends n.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=r.AutoFeatureExtractor;static uses_processor_config=!1;async _call(e,t=null,s={}){if(Array.isArray(e))throw new Error("Batched inputs are not supported yet.");const r={};if(t){if(!e.includes(i))throw new Error(`The input text does not contain the audio token ${i}.`);Array.isArray(t)||(t=[t]);const o=e.split(i),n=o.length-1;if(n!==t.length)throw new Error(`The number of audio inputs (${t.length}) does not match the number of audio tokens in the text (${n}).`);const l=this.feature_extractor.config.n_samples,c=t.map((e=>function(e,t){const s=[];for(let r=0;r<e.length;r+=t)s.push(e.subarray(r,Math.min(r+t,e.length)));return s}(e,l))),d=c.map((e=>e.length)),u=c.flat(),_=(await Promise.all(u.map((e=>this.feature_extractor(e,s))))).map((e=>e.input_features));r.audio_values=_.length>1?(0,a.cat)(_,0):_[0];let m=o[0];for(let e=0;e<d.length;++e){m+="[BEGIN_AUDIO]";for(let t=0;t<d[e];++t)m+=i.repeat(375);m+=o[e+1]}e=m}return{...this.tokenizer(e,{add_special_tokens:!1,...s}),...r}}}},"./src/models/wav2vec2/feature_extraction_wav2vec2.js":(e,t,s)=>{s.r(t),s.d(t,{Wav2Vec2FeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js");class n extends r.FeatureExtractor{_zero_mean_unit_var_norm(e){const t=e.reduce(((e,t)=>e+t),0)/e.length,s=e.reduce(((e,s)=>e+(s-t)**2),0)/e.length;return e.map((e=>(e-t)/Math.sqrt(s+1e-7)))}async _call(e){(0,r.validate_audio_inputs)(e,"Wav2Vec2FeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));let t=e;this.config.do_normalize&&(t=this._zero_mean_unit_var_norm(t));const s=[1,t.length];return{input_values:new o.Tensor("float32",t,s),attention_mask:new o.Tensor("int64",new BigInt64Array(t.length).fill(1n),s)}}}},"./src/models/wav2vec2/processing_wav2vec2.js":(e,t,s)=>{s.r(t),s.d(t,{Wav2Vec2Processor:()=>a});var r=s("./src/tokenizers.js"),o=s("./src/models/auto/feature_extraction_auto.js"),n=s("./src/base/processing_utils.js");class a extends n.Processor{static tokenizer_class=r.AutoTokenizer;static feature_extractor_class=o.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.js":(e,t,s)=>{s.r(t),s.d(t,{Wav2Vec2ProcessorWithLM:()=>a});var r=s("./src/tokenizers.js"),o=s("./src/models/auto/feature_extraction_auto.js"),n=s("./src/base/processing_utils.js");class a extends n.Processor{static tokenizer_class=r.AutoTokenizer;static feature_extractor_class=o.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/wespeaker/feature_extraction_wespeaker.js":(e,t,s)=>{s.r(t),s.d(t,{WeSpeakerFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js"));class n extends r.FeatureExtractor{constructor(e){super(e);const t=this.config.sampling_rate,s=(0,o.mel_filter_bank)(257,this.config.num_mel_bins,20,Math.floor(t/2),t,null,"kaldi",!0);this.mel_filters=s,this.window=(0,o.window_function)(400,"hamming",{periodic:!1}),this.min_num_frames=this.config.min_num_frames}async _extract_fbank_features(e){return e=e.map((e=>32768*e)),(0,o.spectrogram)(e,this.window,400,160,{fft_length:512,power:2,center:!1,preemphasis:.97,mel_filters:this.mel_filters,log_mel:"log",mel_floor:1.192092955078125e-7,remove_dc_offset:!0,transpose:!0,min_num_frames:this.min_num_frames})}async _call(e){(0,r.validate_audio_inputs)(e,"WeSpeakerFeatureExtractor");const t=(await this._extract_fbank_features(e)).unsqueeze_(0);if(null===this.config.fbank_centering_span){const e=t.mean(1).data,s=t.data,[r,o,n]=t.dims;for(let t=0;t<r;++t){const r=t*o*n,a=t*n;for(let t=0;t<o;++t){const o=r+t*n;for(let t=0;t<n;++t)s[o+t]-=e[a+t]}}}return{input_features:t}}}},"./src/models/whisper/common_whisper.js":(e,t,s)=>{s.r(t),s.d(t,{WHISPER_LANGUAGE_MAPPING:()=>o,WHISPER_TO_LANGUAGE_CODE_MAPPING:()=>n,whisper_language_to_code:()=>a});const r=[["en","english"],["zh","chinese"],["de","german"],["es","spanish"],["ru","russian"],["ko","korean"],["fr","french"],["ja","japanese"],["pt","portuguese"],["tr","turkish"],["pl","polish"],["ca","catalan"],["nl","dutch"],["ar","arabic"],["sv","swedish"],["it","italian"],["id","indonesian"],["hi","hindi"],["fi","finnish"],["vi","vietnamese"],["he","hebrew"],["uk","ukrainian"],["el","greek"],["ms","malay"],["cs","czech"],["ro","romanian"],["da","danish"],["hu","hungarian"],["ta","tamil"],["no","norwegian"],["th","thai"],["ur","urdu"],["hr","croatian"],["bg","bulgarian"],["lt","lithuanian"],["la","latin"],["mi","maori"],["ml","malayalam"],["cy","welsh"],["sk","slovak"],["te","telugu"],["fa","persian"],["lv","latvian"],["bn","bengali"],["sr","serbian"],["az","azerbaijani"],["sl","slovenian"],["kn","kannada"],["et","estonian"],["mk","macedonian"],["br","breton"],["eu","basque"],["is","icelandic"],["hy","armenian"],["ne","nepali"],["mn","mongolian"],["bs","bosnian"],["kk","kazakh"],["sq","albanian"],["sw","swahili"],["gl","galician"],["mr","marathi"],["pa","punjabi"],["si","sinhala"],["km","khmer"],["sn","shona"],["yo","yoruba"],["so","somali"],["af","afrikaans"],["oc","occitan"],["ka","georgian"],["be","belarusian"],["tg","tajik"],["sd","sindhi"],["gu","gujarati"],["am","amharic"],["yi","yiddish"],["lo","lao"],["uz","uzbek"],["fo","faroese"],["ht","haitian creole"],["ps","pashto"],["tk","turkmen"],["nn","nynorsk"],["mt","maltese"],["sa","sanskrit"],["lb","luxembourgish"],["my","myanmar"],["bo","tibetan"],["tl","tagalog"],["mg","malagasy"],["as","assamese"],["tt","tatar"],["haw","hawaiian"],["ln","lingala"],["ha","hausa"],["ba","bashkir"],["jw","javanese"],["su","sundanese"]],o=new Map(r),n=new Map([...r.map((([e,t])=>[t,e])),["burmese","my"],["valencian","ca"],["flemish","nl"],["haitian","ht"],["letzeburgesch","lb"],["pushto","ps"],["panjabi","pa"],["moldavian","ro"],["moldovan","ro"],["sinhalese","si"],["castilian","es"]]);function a(e){e=e.toLowerCase();let t=n.get(e);if(void 0===t){const s=e.match(/^<\|([a-z]{2})\|>$/);if(s&&(e=s[1]),!o.has(e)){const t=2===e.length?o.keys():o.values();throw new Error(`Language "${e}" is not supported. Must be one of: ${JSON.stringify(Array.from(t))}`)}t=e}return t}},"./src/models/whisper/feature_extraction_whisper.js":(e,t,s)=>{s.r(t),s.d(t,{WhisperFeatureExtractor:()=>a});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js")),n=s("./src/utils/maths.js");class a extends r.FeatureExtractor{constructor(e){super(e),this.config.mel_filters??=(0,o.mel_filter_bank)(Math.floor(1+this.config.n_fft/2),this.config.feature_size,0,8e3,this.config.sampling_rate,"slaney","slaney"),this.window=(0,o.window_function)(this.config.n_fft,"hann")}async _extract_fbank_features(e){const t=await(0,o.spectrogram)(e,this.window,this.config.n_fft,this.config.hop_length,{power:2,mel_filters:this.config.mel_filters,log_mel:"log10",max_num_frames:Math.min(Math.floor(e.length/this.config.hop_length),this.config.nb_max_frames)}),s=t.data,r=(0,n.max)(s)[0];for(let e=0;e<s.length;++e)s[e]=(Math.max(s[e],r-8)+4)/4;return t}async _call(e,{max_length:t=null}={}){let s;(0,r.validate_audio_inputs)(e,"WhisperFeatureExtractor");const o=t??this.config.n_samples;e.length>o?(e.length>this.config.n_samples&&console.warn("Attempting to extract features for audio longer than 30 seconds. If using a pipeline to extract transcript from a long audio clip, remember to specify `chunk_length_s` and/or `stride_length_s`."),s=e.slice(0,o)):(s=new Float32Array(o),s.set(e));return{input_features:(await this._extract_fbank_features(s)).unsqueeze_(0)}}}},"./src/models/whisper/generation_whisper.js":(e,t,s)=>{s.r(t),s.d(t,{WhisperGenerationConfig:()=>o});var r=s("./src/generation/configuration_utils.js");class o extends r.GenerationConfig{return_timestamps=null;return_token_timestamps=null;num_frames=null;alignment_heads=null;task=null;language=null;no_timestamps_token_id=null;prompt_ids=null;is_multilingual=null;lang_to_id=null;task_to_id=null;max_initial_timestamp_index=1}},"./src/models/whisper/processing_whisper.js":(e,t,s)=>{s.r(t),s.d(t,{WhisperProcessor:()=>a});var r=s("./src/models/auto/feature_extraction_auto.js"),o=s("./src/tokenizers.js"),n=s("./src/base/processing_utils.js");class a extends n.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=r.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/yolos/image_processing_yolos.js":(e,t,s)=>{s.r(t),s.d(t,{YolosFeatureExtractor:()=>n,YolosImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}}class n extends o{}},"./src/ops/registry.js":(e,t,s)=>{s.r(t),s.d(t,{TensorOpRegistry:()=>l});var r=s("./src/backends/onnx.js"),o=s("./src/utils/tensor.js"),n=s("./src/env.js");const a=n.apis.IS_BROWSER_ENV||n.apis.IS_WEBWORKER_ENV,i=async(e,t,s)=>{const n=await(0,r.createInferenceSession)(new Uint8Array(e),t);let i=Promise.resolve();return async e=>{const t=(0,r.isONNXProxy)(),l=Object.fromEntries(Object.entries(e).map((([e,s])=>[e,(t?s.clone():s).ort_tensor]))),c=await(i=a?i.then((()=>n.run(l))):n.run(l));return Array.isArray(s)?s.map((e=>new o.Tensor(c[e]))):new o.Tensor(c[s])}};class l{static session_options={};static get nearest_interpolate_4d(){return this._nearest_interpolate_4d||(this._nearest_interpolate_4d=i([8,10,18,0,58,129,1,10,41,10,1,120,10,0,10,0,10,1,115,18,1,121,34,6,82,101,115,105,122,101,42,18,10,4,109,111,100,101,34,7,110,101,97,114,101,115,116,160,1,3,18,1,114,90,31,10,1,120,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,90,15,10,1,115,18,10,10,8,8,7,18,4,10,2,8,4,98,31,10,1,121,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,66,2,16,21],this.session_options,"y")),this._nearest_interpolate_4d}static get bilinear_interpolate_4d(){return this._bilinear_interpolate_4d||(this._bilinear_interpolate_4d=i([8,9,18,0,58,128,1,10,40,10,1,120,10,0,10,0,10,1,115,18,1,121,34,6,82,101,115,105,122,101,42,17,10,4,109,111,100,101,34,6,108,105,110,101,97,114,160,1,3,18,1,114,90,31,10,1,120,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,90,15,10,1,115,18,10,10,8,8,7,18,4,10,2,8,4,98,31,10,1,121,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,66,2,16,20],this.session_options,"y")),this._bilinear_interpolate_4d}static get bicubic_interpolate_4d(){return this._bicubic_interpolate_4d||(this._bicubic_interpolate_4d=i([8,9,18,0,58,127,10,39,10,1,120,10,0,10,0,10,1,115,18,1,121,34,6,82,101,115,105,122,101,42,16,10,4,109,111,100,101,34,5,99,117,98,105,99,160,1,3,18,1,114,90,31,10,1,120,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,90,15,10,1,115,18,10,10,8,8,7,18,4,10,2,8,4,98,31,10,1,121,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,66,2,16,20],this.session_options,"y")),this._bicubic_interpolate_4d}static get matmul(){return this._matmul||(this._matmul=i([8,9,18,0,58,55,10,17,10,1,97,10,1,98,18,1,99,34,6,77,97,116,77,117,108,18,1,114,90,9,10,1,97,18,4,10,2,8,1,90,9,10,1,98,18,4,10,2,8,1,98,9,10,1,99,18,4,10,2,8,1,66,2,16,20],this.session_options,"c")),this._matmul}static get stft(){return this._stft||(this._stft=i([8,7,18,0,58,148,1,10,38,10,1,115,10,1,106,10,1,119,10,1,108,18,1,111,34,4,83,84,70,84,42,15,10,8,111,110,101,115,105,100,101,100,24,1,160,1,2,18,1,115,90,26,10,1,115,18,21,10,19,8,1,18,15,10,3,18,1,98,10,3,18,1,115,10,3,18,1,99,90,11,10,1,106,18,6,10,4,8,7,18,0,90,16,10,1,119,18,11,10,9,8,1,18,5,10,3,18,1,119,90,11,10,1,108,18,6,10,4,8,7,18,0,98,31,10,1,111,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,102,10,3,18,1,100,10,3,18,1,99,66,2,16,17],this.session_options,"o")),this._stft}static get rfft(){return this._rfft||(this._rfft=i([8,9,18,0,58,97,10,33,10,1,120,10,0,10,1,97,18,1,121,34,3,68,70,84,42,15,10,8,111,110,101,115,105,100,101,100,24,1,160,1,2,18,1,100,90,21,10,1,120,18,16,10,14,8,1,18,10,10,3,18,1,115,10,3,18,1,99,90,11,10,1,97,18,6,10,4,8,7,18,0,98,21,10,1,121,18,16,10,14,8,1,18,10,10,3,18,1,115,10,3,18,1,99,66,2,16,20],this.session_options,"y")),this._rfft}static get top_k(){return this._top_k||(this._top_k=i([8,10,18,0,58,73,10,18,10,1,120,10,1,107,18,1,118,18,1,105,34,4,84,111,112,75,18,1,116,90,9,10,1,120,18,4,10,2,8,1,90,15,10,1,107,18,10,10,8,8,7,18,4,10,2,8,1,98,9,10,1,118,18,4,10,2,8,1,98,9,10,1,105,18,4,10,2,8,7,66,2,16,21],this.session_options,["v","i"])),this._top_k}static get slice(){return this._slice||(this._slice=i([8,7,18,0,58,96,10,25,10,1,120,10,1,115,10,1,101,10,1,97,10,1,116,18,1,121,34,5,83,108,105,99,101,18,1,114,90,9,10,1,120,18,4,10,2,8,1,90,9,10,1,115,18,4,10,2,8,7,90,9,10,1,101,18,4,10,2,8,7,90,9,10,1,97,18,4,10,2,8,7,90,9,10,1,116,18,4,10,2,8,7,98,9,10,1,121,18,4,10,2,8,1,66,2,16,13],this.session_options,"y")),this._slice}}},"./src/pipelines.js":(e,t,s)=>{s.r(t),s.d(t,{AudioClassificationPipeline:()=>C,AutomaticSpeechRecognitionPipeline:()=>A,BackgroundRemovalPipeline:()=>z,DepthEstimationPipeline:()=>G,DocumentQuestionAnsweringPipeline:()=>N,FeatureExtractionPipeline:()=>v,FillMaskPipeline:()=>w,ImageClassificationPipeline:()=>L,ImageFeatureExtractionPipeline:()=>y,ImageSegmentationPipeline:()=>I,ImageToImagePipeline:()=>B,ImageToTextPipeline:()=>E,ObjectDetectionPipeline:()=>j,Pipeline:()=>h,QuestionAnsweringPipeline:()=>M,SummarizationPipeline:()=>b,Text2TextGenerationPipeline:()=>T,TextClassificationPipeline:()=>g,TextGenerationPipeline:()=>k,TextToAudioPipeline:()=>O,TokenClassificationPipeline:()=>f,TranslationPipeline:()=>x,ZeroShotAudioClassificationPipeline:()=>S,ZeroShotClassificationPipeline:()=>F,ZeroShotImageClassificationPipeline:()=>D,ZeroShotObjectDetectionPipeline:()=>V,pipeline:()=>$});var r=s("./src/tokenizers.js"),o=s("./src/models.js"),n=s("./src/models/auto/processing_auto.js"),a=(s("./src/base/processing_utils.js"),s("./src/utils/generic.js")),i=s("./src/utils/core.js"),l=s("./src/utils/maths.js"),c=s("./src/utils/audio.js"),d=s("./src/utils/tensor.js"),u=s("./src/utils/image.js");async function _(e){return Array.isArray(e)||(e=[e]),await Promise.all(e.map((e=>u.RawImage.read(e))))}async function m(e,t){return Array.isArray(e)||(e=[e]),await Promise.all(e.map((e=>"string"==typeof e||e instanceof URL?(0,c.read_audio)(e,t):e instanceof Float64Array?new Float32Array(e):e)))}function p(e,t){t&&(e=e.map((e=>0|e)));const[s,r,o,n]=e;return{xmin:s,ymin:r,xmax:o,ymax:n}}class h extends a.Callable{constructor({task:e,model:t,tokenizer:s=null,processor:r=null}){super(),this.task=e,this.model=t,this.tokenizer=s,this.processor=r}async dispose(){await this.model.dispose()}}class g extends h{constructor(e){super(e)}async _call(e,{top_k:t=1}={}){const s=this.tokenizer(e,{padding:!0,truncation:!0}),r=await this.model(s),o="multi_label_classification"===this.model.config.problem_type?e=>e.sigmoid():e=>new d.Tensor("float32",(0,l.softmax)(e.data),e.dims),n=this.model.config.id2label,a=[];for(const e of r.logits){const s=o(e),r=await(0,d.topk)(s,t),i=r[0].tolist(),l=r[1].tolist().map(((e,t)=>({label:n?n[e]:`LABEL_${e}`,score:i[t]})));1===t?a.push(...l):a.push(l)}return Array.isArray(e)||1===t?a:a[0]}}class f extends h{constructor(e){super(e)}async _call(e,{ignore_labels:t=["O"]}={}){const s=Array.isArray(e),r=this.tokenizer(s?e:[e],{padding:!0,truncation:!0}),o=(await this.model(r)).logits,n=this.model.config.id2label,a=[];for(let e=0;e<o.dims[0];++e){const s=r.input_ids[e],i=o[e],c=[];for(let e=0;e<i.dims[0];++e){const r=i[e],o=(0,l.max)(r.data)[1],a=n?n[o]:`LABEL_${o}`;if(t.includes(a))continue;const d=this.tokenizer.decode([s[e].item()],{skip_special_tokens:!0});if(""===d)continue;const u=(0,l.softmax)(r.data);c.push({entity:a,score:u[o],index:e,word:d})}a.push(c)}return s?a:a[0]}}class M extends h{constructor(e){super(e)}async _call(e,t,{top_k:s=1}={}){const r=this.tokenizer(e,{text_pair:t,padding:!0,truncation:!0}),{start_logits:o,end_logits:n}=await this.model(r),a=r.input_ids.tolist(),c=r.attention_mask.tolist(),d=this.tokenizer.all_special_ids,u=[];for(let e=0;e<o.dims[0];++e){const t=a[e],r=t.findIndex((e=>e==this.tokenizer.sep_token_id)),_=(c[e].map(((e,s)=>1==e&&(0===s||s>r&&-1===d.findIndex((e=>e==t[s]))))),o[e].tolist()),m=n[e].tolist();for(let s=1;s<_.length;++s)(0==c[e]||s<=r||-1!==d.findIndex((e=>e==t[s])))&&(_[s]=-1/0,m[s]=-1/0);const p=(0,l.softmax)(_).map(((e,t)=>[e,t])),h=(0,l.softmax)(m).map(((e,t)=>[e,t]));p[0][0]=0,h[0][0]=0;const g=(0,i.product)(p,h).filter((e=>e[0][1]<=e[1][1])).map((e=>[e[0][1],e[1][1],e[0][0]*e[1][0]])).sort(((e,t)=>t[2]-e[2]));for(let e=0;e<Math.min(g.length,s);++e){const[s,r,o]=g[e],n=t.slice(s,r+1),a=this.tokenizer.decode(n,{skip_special_tokens:!0});u.push({answer:a,score:o})}}return 1===s?u[0]:u}}class w extends h{constructor(e){super(e)}async _call(e,{top_k:t=5}={}){const s=this.tokenizer(e,{padding:!0,truncation:!0}),{logits:r}=await this.model(s),o=[],n=s.input_ids.tolist();for(let e=0;e<n.length;++e){const s=n[e],a=s.findIndex((e=>e==this.tokenizer.mask_token_id));if(-1===a)throw Error(`Mask token (${this.tokenizer.mask_token}) not found in text.`);const i=r[e][a],c=await(0,d.topk)(new d.Tensor("float32",(0,l.softmax)(i.data),i.dims),t),u=c[0].tolist(),_=c[1].tolist();o.push(_.map(((e,t)=>{const r=s.slice();return r[a]=e,{score:u[t],token:Number(e),token_str:this.tokenizer.decode([e]),sequence:this.tokenizer.decode(r,{skip_special_tokens:!0})}})))}return Array.isArray(e)?o:o[0]}}class T extends h{_key="generated_text";constructor(e){super(e)}async _call(e,t={}){Array.isArray(e)||(e=[e]),this.model.config.prefix&&(e=e.map((e=>this.model.config.prefix+e)));const s=this.model.config.task_specific_params;s&&s[this.task]&&s[this.task].prefix&&(e=e.map((e=>s[this.task].prefix+e)));const r=this.tokenizer,o={padding:!0,truncation:!0};let n;n=this instanceof x&&"_build_translation_inputs"in r?r._build_translation_inputs(e,o,t):r(e,o);const a=await this.model.generate({...n,...t});return r.batch_decode(a,{skip_special_tokens:!0}).map((e=>({[this._key]:e})))}}class b extends T{_key="summary_text";constructor(e){super(e)}}class x extends T{_key="translation_text";constructor(e){super(e)}}function P(e){return Array.isArray(e)&&e.every((e=>"role"in e&&"content"in e))}class k extends h{constructor(e){super(e)}async _call(e,t={}){let s,r=!1,o=!1,n=t.add_special_tokens??(this.tokenizer.add_bos_token||this.tokenizer.add_eos_token)??!1;if("string"==typeof e)s=e=[e];else if(Array.isArray(e)&&e.every((e=>"string"==typeof e)))r=!0,s=e;else{if(P(e))e=[e];else{if(!Array.isArray(e)||!e.every(P))throw new Error("Input must be a string, an array of strings, a Chat, or an array of Chats");r=!0}o=!0,s=e.map((e=>this.tokenizer.apply_chat_template(e,{tokenize:!1,add_generation_prompt:!0}))),n=!1}const a=!o&&(t.return_full_text??!0);this.tokenizer.padding_side="left";const i=this.tokenizer(s,{add_special_tokens:n,padding:!0,truncation:!0}),l=await this.model.generate({...i,...t}),c=this.tokenizer.batch_decode(l,{skip_special_tokens:!0});let d;!a&&i.input_ids.dims.at(-1)>0&&(d=this.tokenizer.batch_decode(i.input_ids,{skip_special_tokens:!0}).map((e=>e.length)));const u=Array.from({length:e.length},(e=>[]));for(let t=0;t<c.length;++t){const s=Math.floor(t/l.dims[0]*e.length);d&&(c[t]=c[t].slice(d[s])),u[s].push({generated_text:o?[...e[s],{role:"assistant",content:c[t]}]:c[t]})}return r||1!==u.length?u:u[0]}}class F extends h{constructor(e){super(e),this.label2id=Object.fromEntries(Object.entries(this.model.config.label2id).map((([e,t])=>[e.toLowerCase(),t]))),this.entailment_id=this.label2id.entailment,void 0===this.entailment_id&&(console.warn("Could not find 'entailment' in label2id mapping. Using 2 as entailment_id."),this.entailment_id=2),this.contradiction_id=this.label2id.contradiction??this.label2id.not_entailment,void 0===this.contradiction_id&&(console.warn("Could not find 'contradiction' in label2id mapping. Using 0 as contradiction_id."),this.contradiction_id=0)}async _call(e,t,{hypothesis_template:s="This example is {}.",multi_label:r=!1}={}){const o=Array.isArray(e);o||(e=[e]),Array.isArray(t)||(t=[t]);const n=t.map((e=>s.replace("{}",e))),a=r||1===t.length,i=[];for(const s of e){const e=[];for(const t of n){const r=this.tokenizer(s,{text_pair:t,padding:!0,truncation:!0}),o=await this.model(r);a?e.push([o.logits.data[this.contradiction_id],o.logits.data[this.entailment_id]]):e.push(o.logits.data[this.entailment_id])}const r=(a?e.map((e=>(0,l.softmax)(e)[1])):(0,l.softmax)(e)).map(((e,t)=>[e,t])).sort(((e,t)=>t[0]-e[0]));i.push({sequence:s,labels:r.map((e=>t[e[1]])),scores:r.map((e=>e[0]))})}return o?i:i[0]}}class v extends h{constructor(e){super(e)}async _call(e,{pooling:t="none",normalize:s=!1,quantize:r=!1,precision:o="binary"}={}){const n=this.tokenizer(e,{padding:!0,truncation:!0}),a=await this.model(n);let i=a.last_hidden_state??a.logits??a.token_embeddings;switch(t){case"none":break;case"mean":i=(0,d.mean_pooling)(i,n.attention_mask);break;case"first_token":case"cls":i=i.slice(null,0);break;case"last_token":case"eos":i=i.slice(null,-1);break;default:throw Error(`Pooling method '${t}' not supported.`)}return s&&(i=i.normalize(2,-1)),r&&(i=(0,d.quantize_embeddings)(i,o)),i}}class y extends h{constructor(e){super(e)}async _call(e,{pool:t=null}={}){const s=await _(e),{pixel_values:r}=await this.processor(s),o=await this.model({pixel_values:r});let n;if(t){if(!("pooler_output"in o))throw Error("No pooled output was returned. Make sure the model has a 'pooler' layer when using the 'pool' option.");n=o.pooler_output}else n=o.last_hidden_state??o.logits??o.image_embeds;return n}}class C extends h{constructor(e){super(e)}async _call(e,{top_k:t=5}={}){const s=this.processor.feature_extractor.config.sampling_rate,r=await m(e,s),o=this.model.config.id2label,n=[];for(const e of r){const s=await this.processor(e),r=(await this.model(s)).logits[0],a=await(0,d.topk)(new d.Tensor("float32",(0,l.softmax)(r.data),r.dims),t),i=a[0].tolist(),c=a[1].tolist().map(((e,t)=>({label:o?o[e]:`LABEL_${e}`,score:i[t]})));n.push(c)}return Array.isArray(e)?n:n[0]}}class S extends h{constructor(e){super(e)}async _call(e,t,{hypothesis_template:s="This is a sound of {}."}={}){const r=!Array.isArray(e);r&&(e=[e]);const o=t.map((e=>s.replace("{}",e))),n=this.tokenizer(o,{padding:!0,truncation:!0}),a=this.processor.feature_extractor.config.sampling_rate,i=await m(e,a),c=[];for(const e of i){const s=await this.processor(e),r=await this.model({...n,...s}),o=(0,l.softmax)(r.logits_per_audio.data);c.push([...o].map(((e,s)=>({score:e,label:t[s]}))))}return r?c[0]:c}}class A extends h{constructor(e){super(e)}async _call(e,t={}){switch(this.model.config.model_type){case"whisper":case"lite-whisper":return this._call_whisper(e,t);case"wav2vec2":case"wav2vec2-bert":case"unispeech":case"unispeech-sat":case"hubert":return this._call_wav2vec2(e,t);case"moonshine":return this._call_moonshine(e,t);default:throw new Error(`AutomaticSpeechRecognitionPipeline does not support model type '${this.model.config.model_type}'.`)}}async _call_wav2vec2(e,t){t.language&&console.warn('`language` parameter is not yet supported for `wav2vec2` models, defaulting to "English".'),t.task&&console.warn('`task` parameter is not yet supported for `wav2vec2` models, defaulting to "transcribe".');const s=!Array.isArray(e);s&&(e=[e]);const r=this.processor.feature_extractor.config.sampling_rate,o=await m(e,r),n=[];for(const e of o){const t=await this.processor(e),s=(await this.model(t)).logits[0],r=[];for(const e of s)r.push((0,l.max)(e.data)[1]);const o=this.tokenizer.decode(r);n.push({text:o})}return s?n[0]:n}async _call_whisper(e,t){const s=t.return_timestamps??!1,r=t.chunk_length_s??0,o=t.force_full_sequences??!1;let n=t.stride_length_s??null;const a={...t};"word"===s&&(a.return_token_timestamps=!0,a.return_timestamps=!1);const i=!Array.isArray(e);i&&(e=[e]);const c=this.processor.feature_extractor.config.chunk_length/this.model.config.max_source_positions,d=this.processor.feature_extractor.config.hop_length,u=this.processor.feature_extractor.config.sampling_rate,_=await m(e,u),p=[];for(const e of _){let t=[];if(r>0){if(null===n)n=r/6;else if(r<=n)throw Error("`chunk_length_s` must be larger than `stride_length_s`.");const s=u*r,o=u*n,a=s-2*o;let i=0;for(;;){const r=i+s,n=e.subarray(i,r),l=await this.processor(n),c=0===i,d=r>=e.length;if(t.push({stride:[n.length,c?0:o,d?0:o],input_features:l.input_features,is_last:d}),d)break;i+=a}}else t=[{stride:[e.length,0,0],input_features:(await this.processor(e)).input_features,is_last:!0}];for(const e of t){a.num_frames=Math.floor(e.stride[0]/d);const t=await this.model.generate({inputs:e.input_features,...a});"word"===s?(e.tokens=t.sequences.tolist()[0],e.token_timestamps=t.token_timestamps.tolist()[0].map((e=>(0,l.round)(e,2)))):e.tokens=t[0].tolist(),e.stride=e.stride.map((e=>e/u))}const[i,_]=this.tokenizer._decode_asr(t,{time_precision:c,return_timestamps:s,force_full_sequences:o});p.push({text:i,..._})}return i?p[0]:p}async _call_moonshine(e,t){const s=!Array.isArray(e);s&&(e=[e]);const r=this.processor.feature_extractor.config.sampling_rate,o=await m(e,r),n=[];for(const e of o){const s=await this.processor(e),o=6*Math.floor(e.length/r),a=await this.model.generate({max_new_tokens:o,...t,...s}),i=this.processor.batch_decode(a,{skip_special_tokens:!0})[0];n.push({text:i})}return s?n[0]:n}}class E extends h{constructor(e){super(e)}async _call(e,t={}){const s=Array.isArray(e),r=await _(e),{pixel_values:o}=await this.processor(r),n=[];for(const e of o){e.dims=[1,...e.dims];const s=await this.model.generate({inputs:e,...t}),r=this.tokenizer.batch_decode(s,{skip_special_tokens:!0}).map((e=>({generated_text:e.trim()})));n.push(r)}return s?n:n[0]}}class L extends h{constructor(e){super(e)}async _call(e,{top_k:t=5}={}){const s=await _(e),{pixel_values:r}=await this.processor(s),o=await this.model({pixel_values:r}),n=this.model.config.id2label,a=[];for(const e of o.logits){const s=await(0,d.topk)(new d.Tensor("float32",(0,l.softmax)(e.data),e.dims),t),r=s[0].tolist(),o=s[1].tolist().map(((e,t)=>({label:n?n[e]:`LABEL_${e}`,score:r[t]})));a.push(o)}return Array.isArray(e)?a:a[0]}}class I extends h{constructor(e){super(e),this.subtasks_mapping={panoptic:"post_process_panoptic_segmentation",instance:"post_process_instance_segmentation",semantic:"post_process_semantic_segmentation"}}async _call(e,{threshold:t=.5,mask_threshold:s=.5,overlap_mask_area_threshold:r=.8,label_ids_to_fuse:o=null,target_sizes:n=null,subtask:a=null}={}){if(Array.isArray(e)&&1!==e.length)throw Error("Image segmentation pipeline currently only supports a batch size of 1.");const i=await _(e),l=i.map((e=>[e.height,e.width])),c=await this.processor(i),{inputNames:d,outputNames:m}=this.model.sessions.model;if(!d.includes("pixel_values")){if(1!==d.length)throw Error(`Expected a single input name, but got ${d.length} inputs: ${d}.`);const e=d[0];if(e in c)throw Error(`Input name ${e} already exists in the inputs.`);c[e]=c.pixel_values}const p=await this.model(c);let h=null;if(null!==a)h=this.subtasks_mapping[a];else if(this.processor.image_processor)for(const[e,t]of Object.entries(this.subtasks_mapping))if(t in this.processor.image_processor){h=this.processor.image_processor[t].bind(this.processor.image_processor),a=e;break}const g=this.model.config.id2label,f=[];if(a)if("panoptic"===a||"instance"===a){const e=h(p,t,s,r,o,n??l)[0],a=e.segmentation;for(const t of e.segments_info){const e=new Uint8ClampedArray(a.data.length);for(let s=0;s<a.data.length;++s)a.data[s]===t.id&&(e[s]=255);const s=new u.RawImage(e,a.dims[1],a.dims[0],1);f.push({score:t.score,label:g[t.label_id],mask:s})}}else{if("semantic"!==a)throw Error(`Subtask ${a} not supported.`);{const{segmentation:e,labels:t}=h(p,n??l)[0];for(const s of t){const t=new Uint8ClampedArray(e.data.length);for(let r=0;r<e.data.length;++r)e.data[r]===s&&(t[r]=255);const r=new u.RawImage(t,e.dims[1],e.dims[0],1);f.push({score:null,label:g[s],mask:r})}}}else{const e=1e-5,t=p[m[0]];for(let s=0;s<l.length;++s){const r=l[s],o=t[s];o.data.some((t=>t<-e||t>1+e))&&o.sigmoid_();const n=await u.RawImage.fromTensor(o.mul_(255).to("uint8")).resize(r[1],r[0]);f.push({label:null,score:null,mask:n})}}return f}}class z extends I{constructor(e){super(e)}async _call(e,t={}){if(Array.isArray(e)&&1!==e.length)throw Error("Background removal pipeline currently only supports a batch size of 1.");const s=await _(e),r=await super._call(e,t);return s.map(((e,t)=>{const s=e.clone();return s.putAlpha(r[t].mask),s}))}}class D extends h{constructor(e){super(e)}async _call(e,t,{hypothesis_template:s="This is a photo of {}"}={}){const r=Array.isArray(e),o=await _(e),n=t.map((e=>s.replace("{}",e))),a=this.tokenizer(n,{padding:"siglip"!==this.model.config.model_type||"max_length",truncation:!0}),{pixel_values:i}=await this.processor(o),c=await this.model({...a,pixel_values:i}),d="siglip"===this.model.config.model_type?e=>e.sigmoid().data:e=>(0,l.softmax)(e.data),u=[];for(const e of c.logits_per_image){const s=[...d(e)].map(((e,s)=>({score:e,label:t[s]})));s.sort(((e,t)=>t.score-e.score)),u.push(s)}return r?u:u[0]}}class j extends h{constructor(e){super(e)}async _call(e,{threshold:t=.9,percentage:s=!1}={}){const r=Array.isArray(e);if(r&&1!==e.length)throw Error("Object detection pipeline currently only supports a batch size of 1.");const o=await _(e),n=s?null:o.map((e=>[e.height,e.width])),{pixel_values:a,pixel_mask:i}=await this.processor(o),l=await this.model({pixel_values:a,pixel_mask:i}),c=this.processor.image_processor.post_process_object_detection(l,t,n),d=this.model.config.id2label,u=c.map((e=>e.boxes.map(((t,r)=>({score:e.scores[r],label:d[e.classes[r]],box:p(t,!s)})))));return r?u:u[0]}}class V extends h{constructor(e){super(e)}async _call(e,t,{threshold:s=.1,top_k:r=null,percentage:o=!1}={}){const n=Array.isArray(e),a=await _(e),i=this.tokenizer(t,{padding:!0,truncation:!0}),l=await this.processor(a),c=[];for(let e=0;e<a.length;++e){const n=a[e],d=o?null:[[n.height,n.width]],u=l.pixel_values[e].unsqueeze_(0),_=await this.model({...i,pixel_values:u});let m;if("post_process_grounded_object_detection"in this.processor){const e=this.processor.post_process_grounded_object_detection(_,i.input_ids,{box_threshold:s,text_threshold:s,target_sizes:d})[0];m=e.boxes.map(((t,s)=>({score:e.scores[s],label:e.labels[s],box:p(t,!o)})))}else{const e=this.processor.image_processor.post_process_object_detection(_,s,d,!0)[0];m=e.boxes.map(((s,r)=>({score:e.scores[r],label:t[e.classes[r]],box:p(s,!o)})))}m.sort(((e,t)=>t.score-e.score)),null!==r&&(m=m.slice(0,r)),c.push(m)}return n?c:c[0]}}class N extends h{constructor(e){super(e)}async _call(e,t,s={}){const r=(await _(e))[0],{pixel_values:o}=await this.processor(r),n=`<s_docvqa><s_question>${t}</s_question><s_answer>`,a=this.tokenizer(n,{add_special_tokens:!1,padding:!0,truncation:!0}).input_ids,i=await this.model.generate({inputs:o,max_length:this.model.config.decoder.max_position_embeddings,decoder_input_ids:a,...s}),l=this.tokenizer.batch_decode(i)[0].match(/<s_answer>(.*?)<\/s_answer>/);let c=null;return l&&l.length>=2&&(c=l[1].trim()),[{answer:c}]}}class O extends h{DEFAULT_VOCODER_ID="Xenova/speecht5_hifigan";constructor(e){super(e),this.vocoder=e.vocoder??null}async _call(e,{speaker_embeddings:t=null}={}){return this.processor?this._call_text_to_spectrogram(e,{speaker_embeddings:t}):this._call_text_to_waveform(e)}async _call_text_to_waveform(e){const t=this.tokenizer(e,{padding:!0,truncation:!0}),{waveform:s}=await this.model(t),r=this.model.config.sampling_rate;return new c.RawAudio(s.data,r)}async _call_text_to_spectrogram(e,{speaker_embeddings:t}){if(this.vocoder||(console.log("No vocoder specified, using default HifiGan vocoder."),this.vocoder=await o.AutoModel.from_pretrained(this.DEFAULT_VOCODER_ID,{dtype:"fp32"})),("string"==typeof t||t instanceof URL)&&(t=new Float32Array(await(await fetch(t)).arrayBuffer())),t instanceof Float32Array)t=new d.Tensor("float32",t,[1,t.length]);else if(!(t instanceof d.Tensor))throw new Error("Speaker embeddings must be a `Tensor`, `Float32Array`, `string`, or `URL`.");const{input_ids:s}=this.tokenizer(e,{padding:!0,truncation:!0}),{waveform:r}=await this.model.generate_speech(s,t,{vocoder:this.vocoder}),n=this.processor.feature_extractor.config.sampling_rate;return new c.RawAudio(r.data,n)}}class B extends h{constructor(e){super(e)}async _call(e){const t=await _(e),s=await this.processor(t),r=await this.model(s),o=[];for(const e of r.reconstruction){const t=e.squeeze().clamp_(0,1).mul_(255).round_().to("uint8");o.push(u.RawImage.fromTensor(t))}return o.length>1?o:o[0]}}class G extends h{constructor(e){super(e)}async _call(e){const t=await _(e),s=await this.processor(t),{predicted_depth:r}=await this.model(s),o=[];for(let e=0;e<t.length;++e){const s=r[e],[n,a]=s.dims.slice(-2),[i,l]=t[e].size,c=(await(0,d.interpolate_4d)(s.view(1,1,n,a),{size:[l,i],mode:"bilinear"})).view(l,i),_=c.min().item(),m=c.max().item(),p=c.sub(_).div_(m-_).mul_(255).to("uint8").unsqueeze(0),h=u.RawImage.fromTensor(p);o.push({predicted_depth:c,depth:h})}return o.length>1?o:o[0]}}const R=Object.freeze({"text-classification":{tokenizer:r.AutoTokenizer,pipeline:g,model:o.AutoModelForSequenceClassification,default:{model:"Xenova/distilbert-base-uncased-finetuned-sst-2-english"},type:"text"},"token-classification":{tokenizer:r.AutoTokenizer,pipeline:f,model:o.AutoModelForTokenClassification,default:{model:"Xenova/bert-base-multilingual-cased-ner-hrl"},type:"text"},"question-answering":{tokenizer:r.AutoTokenizer,pipeline:M,model:o.AutoModelForQuestionAnswering,default:{model:"Xenova/distilbert-base-cased-distilled-squad"},type:"text"},"fill-mask":{tokenizer:r.AutoTokenizer,pipeline:w,model:o.AutoModelForMaskedLM,default:{model:"Xenova/bert-base-uncased"},type:"text"},summarization:{tokenizer:r.AutoTokenizer,pipeline:b,model:o.AutoModelForSeq2SeqLM,default:{model:"Xenova/distilbart-cnn-6-6"},type:"text"},translation:{tokenizer:r.AutoTokenizer,pipeline:x,model:o.AutoModelForSeq2SeqLM,default:{model:"Xenova/t5-small"},type:"text"},"text2text-generation":{tokenizer:r.AutoTokenizer,pipeline:T,model:o.AutoModelForSeq2SeqLM,default:{model:"Xenova/flan-t5-small"},type:"text"},"text-generation":{tokenizer:r.AutoTokenizer,pipeline:k,model:o.AutoModelForCausalLM,default:{model:"Xenova/gpt2"},type:"text"},"zero-shot-classification":{tokenizer:r.AutoTokenizer,pipeline:F,model:o.AutoModelForSequenceClassification,default:{model:"Xenova/distilbert-base-uncased-mnli"},type:"text"},"audio-classification":{pipeline:C,model:o.AutoModelForAudioClassification,processor:n.AutoProcessor,default:{model:"Xenova/wav2vec2-base-superb-ks"},type:"audio"},"zero-shot-audio-classification":{tokenizer:r.AutoTokenizer,pipeline:S,model:o.AutoModel,processor:n.AutoProcessor,default:{model:"Xenova/clap-htsat-unfused"},type:"multimodal"},"automatic-speech-recognition":{tokenizer:r.AutoTokenizer,pipeline:A,model:[o.AutoModelForSpeechSeq2Seq,o.AutoModelForCTC],processor:n.AutoProcessor,default:{model:"Xenova/whisper-tiny.en"},type:"multimodal"},"text-to-audio":{tokenizer:r.AutoTokenizer,pipeline:O,model:[o.AutoModelForTextToWaveform,o.AutoModelForTextToSpectrogram],processor:[n.AutoProcessor,null],default:{model:"Xenova/speecht5_tts"},type:"text"},"image-to-text":{tokenizer:r.AutoTokenizer,pipeline:E,model:o.AutoModelForVision2Seq,processor:n.AutoProcessor,default:{model:"Xenova/vit-gpt2-image-captioning"},type:"multimodal"},"image-classification":{pipeline:L,model:o.AutoModelForImageClassification,processor:n.AutoProcessor,default:{model:"Xenova/vit-base-patch16-224"},type:"multimodal"},"image-segmentation":{pipeline:I,model:[o.AutoModelForImageSegmentation,o.AutoModelForSemanticSegmentation,o.AutoModelForUniversalSegmentation],processor:n.AutoProcessor,default:{model:"Xenova/detr-resnet-50-panoptic"},type:"multimodal"},"background-removal":{pipeline:z,model:[o.AutoModelForImageSegmentation,o.AutoModelForSemanticSegmentation,o.AutoModelForUniversalSegmentation],processor:n.AutoProcessor,default:{model:"Xenova/modnet"},type:"image"},"zero-shot-image-classification":{tokenizer:r.AutoTokenizer,pipeline:D,model:o.AutoModel,processor:n.AutoProcessor,default:{model:"Xenova/clip-vit-base-patch32"},type:"multimodal"},"object-detection":{pipeline:j,model:o.AutoModelForObjectDetection,processor:n.AutoProcessor,default:{model:"Xenova/detr-resnet-50"},type:"multimodal"},"zero-shot-object-detection":{tokenizer:r.AutoTokenizer,pipeline:V,model:o.AutoModelForZeroShotObjectDetection,processor:n.AutoProcessor,default:{model:"Xenova/owlvit-base-patch32"},type:"multimodal"},"document-question-answering":{tokenizer:r.AutoTokenizer,pipeline:N,model:o.AutoModelForDocumentQuestionAnswering,processor:n.AutoProcessor,default:{model:"Xenova/donut-base-finetuned-docvqa"},type:"multimodal"},"image-to-image":{pipeline:B,model:o.AutoModelForImageToImage,processor:n.AutoProcessor,default:{model:"Xenova/swin2SR-classical-sr-x2-64"},type:"image"},"depth-estimation":{pipeline:G,model:o.AutoModelForDepthEstimation,processor:n.AutoProcessor,default:{model:"Xenova/dpt-large"},type:"image"},"feature-extraction":{tokenizer:r.AutoTokenizer,pipeline:v,model:o.AutoModel,default:{model:"Xenova/all-MiniLM-L6-v2"},type:"text"},"image-feature-extraction":{processor:n.AutoProcessor,pipeline:y,model:[o.AutoModelForImageFeatureExtraction,o.AutoModel],default:{model:"Xenova/vit-base-patch16-224-in21k"},type:"image"}}),q=Object.freeze({"sentiment-analysis":"text-classification",ner:"token-classification",asr:"automatic-speech-recognition","text-to-speech":"text-to-audio",embeddings:"feature-extraction"});async function $(e,t=null,{progress_callback:s=null,config:r=null,cache_dir:o=null,local_files_only:n=!1,revision:a="main",device:l=null,dtype:c=null,subfolder:d="onnx",use_external_data_format:u=null,model_file_name:_=null,session_options:m={}}={}){e=q[e]??e;const p=R[e.split("_",1)[0]];if(!p)throw Error(`Unsupported pipeline: ${e}. Must be one of [${Object.keys(R)}]`);t||(t=p.default.model,console.log(`No model specified. Using default model: "${t}".`));const h={progress_callback:s,config:r,cache_dir:o,local_files_only:n,revision:a,device:l,dtype:c,subfolder:d,use_external_data_format:u,model_file_name:_,session_options:m},g=new Map([["tokenizer",p.tokenizer],["model",p.model],["processor",p.processor]]),f=await async function(e,t,s){const r=Object.create(null),o=[];for(const[n,a]of e.entries()){if(!a)continue;let e;e=Array.isArray(a)?new Promise((async(e,r)=>{let o;for(const n of a){if(null===n)return void e(null);try{return void e(await n.from_pretrained(t,s))}catch(e){if(e.message?.includes("Unsupported model type"))o=e;else{if(!e.message?.includes("Could not locate file"))return void r(e);o=e}}}r(o)})):a.from_pretrained(t,s),r[n]=e,o.push(e)}await Promise.all(o);for(const[e,t]of Object.entries(r))r[e]=await t;return r}(g,t,h);f.task=e,(0,i.dispatchCallback)(s,{status:"ready",task:e,model:t});return new(0,p.pipeline)(f)}},"./src/tokenizers.js":(e,t,s)=>{s.r(t),s.d(t,{AlbertTokenizer:()=>ke,AutoTokenizer:()=>ft,BartTokenizer:()=>Ne,BertTokenizer:()=>Pe,BlenderbotSmallTokenizer:()=>dt,BlenderbotTokenizer:()=>ct,BloomTokenizer:()=>Re,CLIPTokenizer:()=>nt,CamembertTokenizer:()=>Ie,CodeGenTokenizer:()=>ot,CodeLlamaTokenizer:()=>We,CohereTokenizer:()=>pt,ConvBertTokenizer:()=>Ae,DebertaTokenizer:()=>ye,DebertaV2Tokenizer:()=>Ce,DistilBertTokenizer:()=>Le,ElectraTokenizer:()=>De,Ernie4_5_Tokenizer:()=>gt,EsmTokenizer:()=>Je,FalconTokenizer:()=>Xe,GPT2Tokenizer:()=>Ve,GPTNeoXTokenizer:()=>He,GemmaTokenizer:()=>Ke,Grok1Tokenizer:()=>Ze,HerbertTokenizer:()=>Se,LlamaTokenizer:()=>$e,M2M100Tokenizer:()=>st,MBart50Tokenizer:()=>Be,MBartTokenizer:()=>Oe,MPNetTokenizer:()=>Qe,MarianTokenizer:()=>it,MgpstrTokenizer:()=>ht,MobileBertTokenizer:()=>Fe,NllbTokenizer:()=>tt,NougatTokenizer:()=>_t,PreTrainedTokenizer:()=>xe,Qwen2Tokenizer:()=>Ye,RoFormerTokenizer:()=>Ee,RobertaTokenizer:()=>Ge,SiglipTokenizer:()=>at,SpeechT5Tokenizer:()=>ut,SqueezeBertTokenizer:()=>ve,T5Tokenizer:()=>je,TokenizerModel:()=>P,VitsTokenizer:()=>mt,Wav2Vec2CTCTokenizer:()=>lt,WhisperTokenizer:()=>rt,XLMRobertaTokenizer:()=>Ue,XLMTokenizer:()=>ze,is_chinese_char:()=>f});var r=s("./src/utils/generic.js"),o=s("./src/utils/core.js"),n=s("./src/utils/hub.js"),a=s("./src/utils/maths.js"),i=s("./src/utils/tensor.js"),l=s("./src/utils/data-structures.js"),c=s("./node_modules/@huggingface/jinja/dist/index.js"),d=s("./src/models/whisper/common_whisper.js");async function u(e,t){const s=await Promise.all([(0,n.getModelJSON)(e,"tokenizer.json",!0,t),(0,n.getModelJSON)(e,"tokenizer_config.json",!0,t)]);return null!==t.legacy&&(s[1].legacy=t.legacy),s}function _(e,t=!0){if(void 0!==e.Regex){let t=e.Regex.replace(/\\([#&~])/g,"$1");for(const[e,s]of b)t=t.replaceAll(e,s);return new RegExp(t,"gu")}if(void 0!==e.String){const s=(0,o.escapeRegExp)(e.String);return new RegExp(t?s:`(${s})`,"gu")}return console.warn("Unknown pattern type:",e),null}function m(e){return new Map(Object.entries(e))}function p(e){const t=e.dims;switch(t.length){case 1:return e.tolist();case 2:if(1!==t[0])throw new Error("Unable to decode tensor with `batch size !== 1`. Use `tokenizer.batch_decode(...)` for batched inputs.");return e.tolist()[0];default:throw new Error(`Expected tensor to have 1-2 dimensions, got ${t.length}.`)}}function h(e){return e.replace(/ \./g,".").replace(/ \?/g,"?").replace(/ \!/g,"!").replace(/ ,/g,",").replace(/ \' /g,"'").replace(/ n\'t/g,"n't").replace(/ \'m/g,"'m").replace(/ \'s/g,"'s").replace(/ \'ve/g,"'ve").replace(/ \'re/g,"'re")}function g(e){return e.replace(/\p{M}/gu,"")}function f(e){return e>=19968&&e<=40959||e>=13312&&e<=19903||e>=131072&&e<=173791||e>=173824&&e<=177983||e>=177984&&e<=178207||e>=178208&&e<=183983||e>=63744&&e<=64255||e>=194560&&e<=195103}const M="\\p{P}\\u0021-\\u002F\\u003A-\\u0040\\u005B-\\u0060\\u007B-\\u007E",w=new RegExp(`^[${M}]+$`,"gu"),T=".,!?…。，、।۔،",b=new Map([["(?i:'s|'t|'re|'ve|'m|'ll|'d)","(?:'([sS]|[tT]|[rR][eE]|[vV][eE]|[mM]|[lL][lL]|[dD]))"],[` ?[^(\\s|[${T}])]+`,` ?[^\\s${T}]+`]]);class x{constructor(e){this.content=e.content,this.id=e.id,this.single_word=e.single_word??!1,this.lstrip=e.lstrip??!1,this.rstrip=e.rstrip??!1,this.special=e.special??!1,this.normalized=e.normalized??null}}class P extends r.Callable{constructor(e){super(),this.config=e,this.vocab=[],this.tokens_to_ids=new Map,this.unk_token_id=void 0,this.unk_token=void 0,this.end_of_word_suffix=void 0,this.fuse_unk=this.config.fuse_unk??!1}static fromConfig(e,...t){switch(e.type){case"WordPiece":return new k(e);case"Unigram":return new F(e,...t);case"BPE":return new C(e);default:if(e.vocab)return Array.isArray(e.vocab)?new F(e,...t):Object.hasOwn(e,"continuing_subword_prefix")&&Object.hasOwn(e,"unk_token")?Object.hasOwn(e,"merges")?new C(e):new k(e):new S(e,...t);throw new Error(`Unknown TokenizerModel type: ${e.type}`)}}_call(e){return e=this.encode(e),this.fuse_unk&&(e=function(e,t,s){const r=[];let o=0;for(;o<e.length;)if(r.push(e[o]),(t.get(e[o])??s)===s)for(;++o<e.length&&(t.get(e[o])??s)===s;)t.get(r.at(-1))!==s&&(r[r.length-1]+=e[o]);else++o;return r}(e,this.tokens_to_ids,this.unk_token_id)),e}encode(e){throw Error("encode should be implemented in subclass.")}convert_tokens_to_ids(e){return e.map((e=>this.tokens_to_ids.get(e)??this.unk_token_id))}convert_ids_to_tokens(e){return e.map((e=>this.vocab[e]??this.unk_token))}}class k extends P{constructor(e){super(e),this.tokens_to_ids=m(e.vocab),this.unk_token_id=this.tokens_to_ids.get(e.unk_token),this.unk_token=e.unk_token,this.max_input_chars_per_word=e.max_input_chars_per_word??100,this.vocab=new Array(this.tokens_to_ids.size);for(const[e,t]of this.tokens_to_ids)this.vocab[t]=e}encode(e){const t=[];for(const s of e){const e=[...s];if(e.length>this.max_input_chars_per_word){t.push(this.unk_token);continue}let r=!1,o=0;const n=[];for(;o<e.length;){let t=e.length,s=null;for(;o<t;){let r=e.slice(o,t).join("");if(o>0&&(r=this.config.continuing_subword_prefix+r),this.tokens_to_ids.has(r)){s=r;break}--t}if(null===s){r=!0;break}n.push(s),o=t}r?t.push(this.unk_token):t.push(...n)}return t}}class F extends P{constructor(e,t){super(e);const s=e.vocab.length;this.vocab=new Array(s),this.scores=new Array(s);for(let t=0;t<s;++t)[this.vocab[t],this.scores[t]]=e.vocab[t];this.unk_token_id=e.unk_id,this.unk_token=this.vocab[e.unk_id],this.tokens_to_ids=new Map(this.vocab.map(((e,t)=>[e,t]))),this.bos_token=" ",this.bos_token_id=this.tokens_to_ids.get(this.bos_token),this.eos_token=t.eos_token,this.eos_token_id=this.tokens_to_ids.get(this.eos_token),this.unk_token=this.vocab[this.unk_token_id],this.minScore=(0,a.min)(this.scores)[0],this.unk_score=this.minScore-10,this.scores[this.unk_token_id]=this.unk_score,this.trie=new l.CharTrie,this.trie.extend(this.vocab),this.fuse_unk=!0}populateNodes(e){const t=e.chars;let s=0;for(;s<t.length;){let r=!1;const n=[],a=t.slice(s).join(""),i=this.trie.commonPrefixSearch(a);for(const t of i){n.push(t);const a=this.tokens_to_ids.get(t),i=this.scores[a],l=(0,o.len)(t);e.insert(s,l,i,a),r||1!==l||(r=!0)}r||e.insert(s,1,this.unk_score,this.unk_token_id),s+=1}}tokenize(e){const t=new l.TokenLattice(e,this.bos_token_id,this.eos_token_id);return this.populateNodes(t),t.tokens()}encode(e){const t=[];for(const s of e){const e=this.tokenize(s);t.push(...e)}return t}}const v=(()=>{const e=[...Array.from({length:"~".charCodeAt(0)-"!".charCodeAt(0)+1},((e,t)=>t+"!".charCodeAt(0))),...Array.from({length:"¬".charCodeAt(0)-"¡".charCodeAt(0)+1},((e,t)=>t+"¡".charCodeAt(0))),...Array.from({length:"ÿ".charCodeAt(0)-"®".charCodeAt(0)+1},((e,t)=>t+"®".charCodeAt(0)))],t=e.slice();let s=0;for(let r=0;r<256;++r)e.includes(r)||(e.push(r),t.push(256+s),s+=1);const r=t.map((e=>String.fromCharCode(e)));return Object.fromEntries(e.map(((e,t)=>[e,r[t]])))})(),y=(0,o.reverseDictionary)(v);class C extends P{constructor(e){super(e),this.tokens_to_ids=m(e.vocab),this.unk_token_id=this.tokens_to_ids.get(e.unk_token),this.unk_token=e.unk_token,this.vocab=new Array(this.tokens_to_ids.size);for(const[e,t]of this.tokens_to_ids)this.vocab[t]=e;const t=Array.isArray(e.merges[0]);this.merges=t?e.merges:e.merges.map((e=>e.split(" ",2))),this.bpe_ranks=new Map(this.merges.map(((e,t)=>[JSON.stringify(e),t]))),this.end_of_word_suffix=e.end_of_word_suffix,this.continuing_subword_suffix=e.continuing_subword_suffix??null,this.byte_fallback=this.config.byte_fallback??!1,this.byte_fallback&&(this.text_encoder=new TextEncoder),this.ignore_merges=this.config.ignore_merges??!1,this.max_length_to_cache=256,this.cache_capacity=1e4,this.cache=new l.LRUCache(this.cache_capacity)}clear_cache(){this.cache.clear()}bpe(e){if(0===e.length)return[];const t=this.cache.get(e);if(void 0!==t)return t;const s=Array.from(e);this.end_of_word_suffix&&(s[s.length-1]+=this.end_of_word_suffix);let r=[];if(s.length>1){const e=new l.PriorityQueue(((e,t)=>e.score<t.score));let t={token:s[0],bias:0,prev:null,next:null},o=t;for(let t=1;t<s.length;++t){const r={bias:t/s.length,token:s[t],prev:o,next:null};o.next=r,this._add_node(e,o),o=r}for(;!e.isEmpty();){const s=e.pop();if(s.deleted||!s.next||s.next.deleted)continue;if(s.deleted=!0,s.next.deleted=!0,s.prev){const e={...s.prev};s.prev.deleted=!0,s.prev=e,e.prev?e.prev.next=e:t=e}const r={token:s.token+s.next.token,bias:s.bias,prev:s.prev,next:s.next.next};r.prev?(r.prev.next=r,this._add_node(e,r.prev)):t=r,r.next&&(r.next.prev=r,this._add_node(e,r))}for(let e=t;null!==e;e=e.next)r.push(e.token)}else r=s;if(this.continuing_subword_suffix)for(let e=0;e<r.length-1;++e)r[e]+=this.continuing_subword_suffix;return e.length<this.max_length_to_cache&&this.cache.put(e,r),r}_add_node(e,t){const s=this.bpe_ranks.get(JSON.stringify([t.token,t.next.token]));void 0!==s&&(t.score=s+t.bias,e.push(t))}encode(e){const t=[];for(const s of e){if(this.ignore_merges&&this.tokens_to_ids.has(s)){t.push(s);continue}const e=this.bpe(s);for(const s of e)if(this.tokens_to_ids.has(s))t.push(s);else if(this.byte_fallback){const e=Array.from(this.text_encoder.encode(s)).map((e=>`<0x${e.toString(16).toUpperCase().padStart(2,"0")}>`));e.every((e=>this.tokens_to_ids.has(e)))?t.push(...e):t.push(this.unk_token)}else t.push(this.unk_token)}return t}}class S extends P{constructor(e,t){super(e),this.tokens_to_ids=m(t.target_lang?e.vocab[t.target_lang]:e.vocab),this.bos_token=t.bos_token,this.bos_token_id=this.tokens_to_ids.get(this.bos_token),this.eos_token=t.eos_token,this.eos_token_id=this.tokens_to_ids.get(this.eos_token),this.pad_token=t.pad_token,this.pad_token_id=this.tokens_to_ids.get(this.pad_token),this.unk_token=t.unk_token,this.unk_token_id=this.tokens_to_ids.get(this.unk_token),this.vocab=new Array(this.tokens_to_ids.size);for(const[e,t]of this.tokens_to_ids)this.vocab[t]=e}encode(e){return e}}class A extends r.Callable{constructor(e){super(),this.config=e}static fromConfig(e){if(null===e)return null;switch(e.type){case"BertNormalizer":return new R(e);case"Precompiled":return new pe(e);case"Sequence":return new G(e);case"Replace":return new E(e);case"NFC":return new I(e);case"NFD":return new z(e);case"NFKC":return new D(e);case"NFKD":return new j(e);case"Strip":return new V(e);case"StripAccents":return new N(e);case"Lowercase":return new O(e);case"Prepend":return new B(e);default:throw new Error(`Unknown Normalizer type: ${e.type}`)}}normalize(e){throw Error("normalize should be implemented in subclass.")}_call(e){return this.normalize(e)}}class E extends A{normalize(e){const t=_(this.config.pattern);return null===t?e:e.replaceAll(t,this.config.content)}}class L extends A{form=void 0;normalize(e){return e=e.normalize(this.form)}}class I extends L{form="NFC"}class z extends L{form="NFD"}class D extends L{form="NFKC"}class j extends L{form="NFKD"}class V extends A{normalize(e){return this.config.strip_left&&this.config.strip_right?e=e.trim():(this.config.strip_left&&(e=e.trimStart()),this.config.strip_right&&(e=e.trimEnd())),e}}class N extends A{normalize(e){return e=g(e)}}class O extends A{normalize(e){return e=e.toLowerCase()}}class B extends A{normalize(e){return e=this.config.prepend+e}}class G extends A{constructor(e){super(e),this.normalizers=e.normalizers.map((e=>A.fromConfig(e)))}normalize(e){return this.normalizers.reduce(((e,t)=>t.normalize(e)),e)}}class R extends A{_tokenize_chinese_chars(e){const t=[];for(let s=0;s<e.length;++s){const r=e[s];f(r.charCodeAt(0))?(t.push(" "),t.push(r),t.push(" ")):t.push(r)}return t.join("")}stripAccents(e){return e.normalize("NFD").replace(/\p{Mn}/gu,"")}_is_control(e){switch(e){case"\t":case"\n":case"\r":return!1;default:return/^\p{Cc}|\p{Cf}|\p{Co}|\p{Cs}$/u.test(e)}}_clean_text(e){const t=[];for(const s of e){const e=s.charCodeAt(0);0===e||65533===e||this._is_control(s)||(/^\s$/.test(s)?t.push(" "):t.push(s))}return t.join("")}normalize(e){return this.config.clean_text&&(e=this._clean_text(e)),this.config.handle_chinese_chars&&(e=this._tokenize_chinese_chars(e)),this.config.lowercase?(e=e.toLowerCase(),!1!==this.config.strip_accents&&(e=this.stripAccents(e))):this.config.strip_accents&&(e=this.stripAccents(e)),e}}class q extends r.Callable{static fromConfig(e){if(null===e)return null;switch(e.type){case"BertPreTokenizer":return new $(e);case"Sequence":return new he(e);case"Whitespace":return new ge(e);case"WhitespaceSplit":return new fe(e);case"Metaspace":return new _e(e);case"ByteLevel":return new W(e);case"Split":return new U(e);case"Punctuation":return new Q(e);case"Digits":return new X(e);case"Replace":return new Me(e);default:throw new Error(`Unknown PreTokenizer type: ${e.type}`)}}pre_tokenize_text(e,t){throw Error("pre_tokenize_text should be implemented in subclass.")}pre_tokenize(e,t){return(Array.isArray(e)?e.map((e=>this.pre_tokenize_text(e,t))):this.pre_tokenize_text(e,t)).flat()}_call(e,t){return this.pre_tokenize(e,t)}}class $ extends q{constructor(e){super(),this.pattern=new RegExp(`[^\\s${M}]+|[${M}]`,"gu")}pre_tokenize_text(e,t){return e.trim().match(this.pattern)||[]}}class W extends q{constructor(e){super(),this.config=e,this.add_prefix_space=this.config.add_prefix_space,this.trim_offsets=this.config.trim_offsets,this.use_regex=this.config.use_regex??!0,this.pattern=/'s|'t|'re|'ve|'m|'ll|'d| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+/gu,this.byte_encoder=v,this.text_encoder=new TextEncoder}pre_tokenize_text(e,t){this.add_prefix_space&&!e.startsWith(" ")&&(e=" "+e);return(this.use_regex?e.match(this.pattern)||[]:[e]).map((e=>Array.from(this.text_encoder.encode(e),(e=>this.byte_encoder[e])).join("")))}}class U extends q{constructor(e){super(),this.config=e,this.pattern=_(this.config.pattern,this.config.invert)}pre_tokenize_text(e,t){return null===this.pattern?[]:this.config.invert?e.match(this.pattern)||[]:"removed"===this.config.behavior?.toLowerCase()?e.split(this.pattern).filter((e=>e)):function(e,t){const s=[];let r=0;for(const o of e.matchAll(t)){const t=o[0];r<o.index&&s.push(e.slice(r,o.index)),t.length>0&&s.push(t),r=o.index+t.length}return r<e.length&&s.push(e.slice(r)),s}(e,this.pattern)}}class Q extends q{constructor(e){super(),this.config=e,this.pattern=new RegExp(`[^${M}]+|[${M}]+`,"gu")}pre_tokenize_text(e,t){return e.match(this.pattern)||[]}}class X extends q{constructor(e){super(),this.config=e;const t="[^\\d]+|\\d"+(this.config.individual_digits?"":"+");this.pattern=new RegExp(t,"gu")}pre_tokenize_text(e,t){return e.match(this.pattern)||[]}}class H extends r.Callable{constructor(e){super(),this.config=e}static fromConfig(e){if(null===e)return null;switch(e.type){case"TemplateProcessing":return new K(e);case"ByteLevel":return new Z(e);case"RobertaProcessing":return new Y(e);case"BertProcessing":return new J(e);case"Sequence":return new ee(e);default:throw new Error(`Unknown PostProcessor type: ${e.type}`)}}post_process(e,...t){throw Error("post_process should be implemented in subclass.")}_call(e,...t){return this.post_process(e,...t)}}class J extends H{constructor(e){super(e),this.cls=e.cls[0],this.sep=e.sep[0]}post_process(e,t=null,{add_special_tokens:s=!0}={}){s&&(e=(0,o.mergeArrays)([this.cls],e,[this.sep]));let r=new Array(e.length).fill(0);if(null!==t){const n=s&&this instanceof Y?[this.sep]:[],a=s?[this.sep]:[];e=(0,o.mergeArrays)(e,n,t,a),r=(0,o.mergeArrays)(r,new Array(t.length+n.length+a.length).fill(1))}return{tokens:e,token_type_ids:r}}}class Y extends J{}class K extends H{constructor(e){super(e),this.single=e.single,this.pair=e.pair}post_process(e,t=null,{add_special_tokens:s=!0}={}){const r=null===t?this.single:this.pair;let n=[],a=[];for(const i of r)"SpecialToken"in i?s&&(n.push(i.SpecialToken.id),a.push(i.SpecialToken.type_id)):"Sequence"in i&&("A"===i.Sequence.id?(n=(0,o.mergeArrays)(n,e),a=(0,o.mergeArrays)(a,new Array(e.length).fill(i.Sequence.type_id))):"B"===i.Sequence.id&&(n=(0,o.mergeArrays)(n,t),a=(0,o.mergeArrays)(a,new Array(t.length).fill(i.Sequence.type_id))));return{tokens:n,token_type_ids:a}}}class Z extends H{post_process(e,t=null){return t&&(e=(0,o.mergeArrays)(e,t)),{tokens:e}}}class ee extends H{constructor(e){super(e),this.processors=e.processors.map((e=>H.fromConfig(e)))}post_process(e,t=null,s={}){let r;for(const o of this.processors)if(o instanceof Z){if(e=o.post_process(e).tokens,t){t=o.post_process(t).tokens}}else{const n=o.post_process(e,t,s);e=n.tokens,r=n.token_type_ids}return{tokens:e,token_type_ids:r}}}class te extends r.Callable{constructor(e){super(),this.config=e,this.added_tokens=[],this.end_of_word_suffix=null,this.trim_offsets=e.trim_offsets}static fromConfig(e){if(null===e)return null;switch(e.type){case"WordPiece":return new ae(e);case"Metaspace":return new me(e);case"ByteLevel":return new ie(e);case"Replace":return new se(e);case"ByteFallback":return new re(e);case"Fuse":return new oe(e);case"Strip":return new ne(e);case"Sequence":return new ce(e);case"CTC":return new le(e);case"BPEDecoder":return new de(e);default:throw new Error(`Unknown Decoder type: ${e.type}`)}}_call(e){return this.decode(e)}decode(e){return this.decode_chain(e).join("")}decode_chain(e){throw Error("`decode_chain` should be implemented in subclass.")}}class se extends te{decode_chain(e){const t=_(this.config.pattern);return null===t?e:e.map((e=>e.replaceAll(t,this.config.content)))}}class re extends te{constructor(e){super(e),this.text_decoder=new TextDecoder}decode_chain(e){const t=[];let s=[];for(const r of e){let e=null;if(6===r.length&&r.startsWith("<0x")&&r.endsWith(">")){const t=parseInt(r.slice(3,5),16);isNaN(t)||(e=t)}if(null!==e)s.push(e);else{if(s.length>0){const e=this.text_decoder.decode(Uint8Array.from(s));t.push(e),s=[]}t.push(r)}}if(s.length>0){const e=this.text_decoder.decode(Uint8Array.from(s));t.push(e),s=[]}return t}}class oe extends te{decode_chain(e){return[e.join("")]}}class ne extends te{constructor(e){super(e),this.content=this.config.content,this.start=this.config.start,this.stop=this.config.stop}decode_chain(e){return e.map((e=>{let t=0;for(let s=0;s<this.start&&e[s]===this.content;++s)t=s+1;let s=e.length;for(let t=0;t<this.stop;++t){const r=e.length-t-1;if(e[r]!==this.content)break;s=r}return e.slice(t,s)}))}}class ae extends te{constructor(e){super(e),this.cleanup=e.cleanup}decode_chain(e){return e.map(((e,t)=>(0!==t&&(e=e.startsWith(this.config.prefix)?e.replace(this.config.prefix,""):" "+e),this.cleanup&&(e=h(e)),e)))}}class ie extends te{constructor(e){super(e),this.byte_decoder=y,this.text_decoder=new TextDecoder("utf-8",{fatal:!1,ignoreBOM:!0}),this.end_of_word_suffix=null}convert_tokens_to_string(e){const t=e.join(""),s=new Uint8Array([...t].map((e=>this.byte_decoder[e])));return this.text_decoder.decode(s)}decode_chain(e){const t=[];let s=[];for(const r of e)void 0!==this.added_tokens.find((e=>e.content===r))?(s.length>0&&(t.push(this.convert_tokens_to_string(s)),s=[]),t.push(r)):s.push(r);return s.length>0&&t.push(this.convert_tokens_to_string(s)),t}}class le extends te{constructor(e){super(e),this.pad_token=this.config.pad_token,this.word_delimiter_token=this.config.word_delimiter_token,this.cleanup=this.config.cleanup}convert_tokens_to_string(e){if(0===e.length)return"";const t=[e[0]];for(let s=1;s<e.length;++s)e[s]!==t.at(-1)&&t.push(e[s]);let s=t.filter((e=>e!==this.pad_token)).join("");return this.cleanup&&(s=h(s).replaceAll(this.word_delimiter_token," ").trim()),s}decode_chain(e){return[this.convert_tokens_to_string(e)]}}class ce extends te{constructor(e){super(e),this.decoders=e.decoders.map((e=>te.fromConfig(e)))}decode_chain(e){return this.decoders.reduce(((e,t)=>t.decode_chain(e)),e)}}class de extends te{constructor(e){super(e),this.suffix=this.config.suffix}decode_chain(e){return e.map(((t,s)=>t.replaceAll(this.suffix,s===e.length-1?"":" ")))}}class ue extends te{decode_chain(e){let t="";for(let s=1;s<e.length;s+=2)t+=e[s];return[t]}}class _e extends q{constructor(e){super(),this.addPrefixSpace=e.add_prefix_space,this.replacement=e.replacement,this.strRep=e.str_rep||this.replacement,this.prepend_scheme=e.prepend_scheme??"always"}pre_tokenize_text(e,{section_index:t}={}){let s=e.replaceAll(" ",this.strRep);return this.addPrefixSpace&&!s.startsWith(this.replacement)&&("always"===this.prepend_scheme||"first"===this.prepend_scheme&&0===t)&&(s=this.strRep+s),[s]}}class me extends te{constructor(e){super(e),this.addPrefixSpace=e.add_prefix_space,this.replacement=e.replacement}decode_chain(e){const t=[];for(let s=0;s<e.length;++s){let r=e[s].replaceAll(this.replacement," ");this.addPrefixSpace&&0==s&&r.startsWith(" ")&&(r=r.substring(1)),t.push(r)}return t}}class pe extends A{constructor(e){super(e),this.charsmap=e.precompiled_charsmap}normalize(e){if((e=(e=e.replace(/[\u0001-\u0008\u000B\u000E-\u001F\u007F\u008F\u009F]/gm,"")).replace(/[\u0009\u000A\u000C\u000D\u00A0\u1680\u2000-\u200F\u2028\u2029\u202F\u205F\u2581\u3000\uFEFF\uFFFD]/gm," ")).includes("～")){const t=e.split("～");e=t.map((e=>e.normalize("NFKC"))).join("～")}else e=e.normalize("NFKC");return e}}class he extends q{constructor(e){super(),this.tokenizers=e.pretokenizers.map((e=>q.fromConfig(e)))}pre_tokenize_text(e,t){return this.tokenizers.reduce(((e,s)=>s.pre_tokenize(e,t)),[e])}}class ge extends q{constructor(e){super()}pre_tokenize_text(e,t){return e.match(/\w+|[^\w\s]+/g)||[]}}class fe extends q{constructor(e){super()}pre_tokenize_text(e,t){return function(e){return e.match(/\S+/g)||[]}(e)}}class Me extends q{constructor(e){super(),this.config=e,this.pattern=_(this.config.pattern),this.content=this.config.content}pre_tokenize_text(e,t){return null===this.pattern?[e]:[e.replaceAll(this.pattern,this.config.content)]}}const we=["bos_token","eos_token","unk_token","sep_token","pad_token","cls_token","mask_token"];function Te(e,t,s,r){for(const n of Object.keys(e)){const a=t-e[n].length,i=s(n),l=new Array(a).fill(i);e[n]="right"===r?(0,o.mergeArrays)(e[n],l):(0,o.mergeArrays)(l,e[n])}}function be(e,t){for(const s of Object.keys(e))e[s].length=t}class xe extends r.Callable{return_token_type_ids=!1;padding_side="right";constructor(e,t){super(),this.config=t,this.normalizer=A.fromConfig(e.normalizer),this.pre_tokenizer=q.fromConfig(e.pre_tokenizer),this.model=P.fromConfig(e.model,t),this.post_processor=H.fromConfig(e.post_processor),this.decoder=te.fromConfig(e.decoder),this.special_tokens=[],this.all_special_ids=[],this.added_tokens=[];for(const t of e.added_tokens){const e=new x(t);this.added_tokens.push(e),this.model.tokens_to_ids.set(e.content,e.id),this.model.vocab[e.id]=e.content,e.special&&(this.special_tokens.push(e.content),this.all_special_ids.push(e.id))}if(this.additional_special_tokens=t.additional_special_tokens??[],this.special_tokens.push(...this.additional_special_tokens),this.special_tokens=[...new Set(this.special_tokens)],this.decoder&&(this.decoder.added_tokens=this.added_tokens,this.decoder.end_of_word_suffix=this.model.end_of_word_suffix),this.added_tokens_splitter=new l.DictionarySplitter(this.added_tokens.map((e=>e.content))),this.added_tokens_map=new Map(this.added_tokens.map((e=>[e.content,e]))),this.mask_token=this.getToken("mask_token"),this.mask_token_id=this.model.tokens_to_ids.get(this.mask_token),this.pad_token=this.getToken("pad_token","eos_token"),this.pad_token_id=this.model.tokens_to_ids.get(this.pad_token),this.sep_token=this.getToken("sep_token"),this.sep_token_id=this.model.tokens_to_ids.get(this.sep_token),this.unk_token=this.getToken("unk_token"),this.unk_token_id=this.model.tokens_to_ids.get(this.unk_token),this.bos_token=this.getToken("bos_token"),this.bos_token_id=this.model.tokens_to_ids.get(this.bos_token),this.eos_token=this.getToken("eos_token"),this.eos_token_id=this.model.tokens_to_ids.get(this.eos_token),this.model_max_length=t.model_max_length,this.remove_space=t.remove_space,this.clean_up_tokenization_spaces=t.clean_up_tokenization_spaces??!0,this.do_lowercase_and_remove_accent=t.do_lowercase_and_remove_accent??!1,t.padding_side&&(this.padding_side=t.padding_side),this.add_bos_token=t.add_bos_token,this.add_eos_token=t.add_eos_token,this.legacy=!1,this.chat_template=t.chat_template??null,Array.isArray(this.chat_template)){const e=Object.create(null);for(const{name:t,template:s}of this.chat_template){if("string"!=typeof t||"string"!=typeof s)throw new Error('Chat template must be a list of objects with "name" and "template" properties');e[t]=s}this.chat_template=e}this._compiled_template_cache=new Map}getToken(...e){for(const t of e){const e=this.config[t];if(e){if("object"==typeof e){if("AddedToken"===e.__type)return e.content;throw Error(`Unknown token: ${e}`)}return e}}return null}static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:r=null,local_files_only:o=!1,revision:n="main",legacy:a=null}={}){return new this(...await u(e,{progress_callback:t,config:s,cache_dir:r,local_files_only:o,revision:n,legacy:a}))}_call(e,{text_pair:t=null,add_special_tokens:s=!0,padding:r=!1,truncation:o=null,max_length:n=null,return_tensor:l=!0,return_token_type_ids:c=null}={}){const d=Array.isArray(e);let u;if(d){if(0===e.length)throw Error("text array must be non-empty");if(null!==t){if(!Array.isArray(t))throw Error("text_pair must also be an array");if(e.length!==t.length)throw Error("text and text_pair must have the same length");u=e.map(((e,r)=>this._encode_plus(e,{text_pair:t[r],add_special_tokens:s,return_token_type_ids:c})))}else u=e.map((e=>this._encode_plus(e,{add_special_tokens:s,return_token_type_ids:c})))}else{if(null==e)throw Error("text may not be null or undefined");if(Array.isArray(t))throw Error("When specifying `text_pair`, since `text` is a string, `text_pair` must also be a string (i.e., not an array).");u=[this._encode_plus(e,{text_pair:t,add_special_tokens:s,return_token_type_ids:c})]}if(null===n?n=this.model_max_length:null===o&&(!0===r?(console.warn("`max_length` is ignored when `padding: true` and there is no truncation strategy. To pad to max length, use `padding: 'max_length'`."),n=this.model_max_length):!1===r&&(console.warn("Truncation was not explicitly activated but `max_length` is provided a specific value, please use `truncation: true` to explicitly truncate examples to max length."),o=!0)),!0===r&&(n=Math.min((0,a.max)(u.map((e=>e.input_ids.length)))[0],n??1/0)),n=Math.min(n,this.model_max_length??1/0),r||o)for(let e=0;e<u.length;++e)u[e].input_ids.length!==n&&(u[e].input_ids.length>n?o&&be(u[e],n):r&&Te(u[e],n,(e=>"input_ids"===e?this.pad_token_id:0),this.padding_side));const _={};if(l){if((!r||!o)&&u.some((e=>{for(const t of Object.keys(e))if(e[t].length!==u[0][t]?.length)return!0;return!1})))throw Error("Unable to create tensor, you should probably activate truncation and/or padding with 'padding=true' and 'truncation=true' to have batched tensors with the same length.");const e=[u.length,u[0].input_ids.length];for(const t of Object.keys(u[0]))_[t]=new i.Tensor("int64",BigInt64Array.from(u.flatMap((e=>e[t])).map(BigInt)),e)}else{for(const e of Object.keys(u[0]))_[e]=u.map((t=>t[e]));if(!d)for(const e of Object.keys(_))_[e]=_[e][0]}return _}_encode_text(e){if(null===e)return null;const t=this.added_tokens_splitter.split(e);for(let e=0;e<t.length;++e){const s=this.added_tokens_map.get(t[e]);s&&(s.lstrip&&e>0&&(t[e-1]=t[e-1].trimEnd()),s.rstrip&&e<t.length-1&&(t[e+1]=t[e+1].trimStart()))}const s=t.flatMap(((e,t)=>{if(0===e.length)return[];if(this.added_tokens_map.has(e))return[e];if(!0===this.remove_space&&(e=e.trim().split(/\s+/).join(" ")),this.do_lowercase_and_remove_accent&&(e=function(e){return g(e.toLowerCase())}(e)),null!==this.normalizer&&(e=this.normalizer(e)),0===e.length)return[];const s=null!==this.pre_tokenizer?this.pre_tokenizer(e,{section_index:t}):[e];return this.model(s)}));return s}_encode_plus(e,{text_pair:t=null,add_special_tokens:s=!0,return_token_type_ids:r=null}={}){const{tokens:o,token_type_ids:n}=this._tokenize_helper(e,{pair:t,add_special_tokens:s}),a=this.model.convert_tokens_to_ids(o),i={input_ids:a,attention_mask:new Array(a.length).fill(1)};return(r??this.return_token_type_ids)&&n&&(i.token_type_ids=n),i}_tokenize_helper(e,{pair:t=null,add_special_tokens:s=!1}={}){const r=this._encode_text(e),n=this._encode_text(t);return this.post_processor?this.post_processor(r,n,{add_special_tokens:s}):{tokens:(0,o.mergeArrays)(r??[],n??[])}}tokenize(e,{pair:t=null,add_special_tokens:s=!1}={}){return this._tokenize_helper(e,{pair:t,add_special_tokens:s}).tokens}encode(e,{text_pair:t=null,add_special_tokens:s=!0,return_token_type_ids:r=null}={}){return this._encode_plus(e,{text_pair:t,add_special_tokens:s,return_token_type_ids:r}).input_ids}batch_decode(e,t={}){return e instanceof i.Tensor&&(e=e.tolist()),e.map((e=>this.decode(e,t)))}decode(e,t={}){if(e instanceof i.Tensor&&(e=p(e)),!Array.isArray(e)||0===e.length||!(0,o.isIntegralNumber)(e[0]))throw Error("token_ids must be a non-empty array of integers.");return this.decode_single(e,t)}decode_single(e,{skip_special_tokens:t=!1,clean_up_tokenization_spaces:s=null}){let r=this.model.convert_ids_to_tokens(e);t&&(r=r.filter((e=>!this.special_tokens.includes(e))));let o=this.decoder?this.decoder(r):r.join(" ");return this.decoder&&this.decoder.end_of_word_suffix&&(o=o.replaceAll(this.decoder.end_of_word_suffix," "),t&&(o=o.trim())),(s??this.clean_up_tokenization_spaces)&&(o=h(o)),o}get_chat_template({chat_template:e=null,tools:t=null}={}){if(this.chat_template&&"object"==typeof this.chat_template){const s=this.chat_template;if(null!==e&&Object.hasOwn(s,e))e=s[e];else if(null===e)if(null!==t&&"tool_use"in s)e=s.tool_use;else{if(!("default"in s))throw Error(`This model has multiple chat templates with no default specified! Please either pass a chat template or the name of the template you wish to use to the 'chat_template' argument. Available template names are ${Object.keys(s).sort()}.`);e=s.default}}else if(null===e){if(!this.chat_template)throw Error("Cannot use apply_chat_template() because tokenizer.chat_template is not set and no template argument was passed! For information about writing templates and setting the tokenizer.chat_template attribute, please see the documentation at https://huggingface.co/docs/transformers/main/en/chat_templating");e=this.chat_template}return e}apply_chat_template(e,{tools:t=null,documents:s=null,chat_template:r=null,add_generation_prompt:o=!1,tokenize:n=!0,padding:a=!1,truncation:i=!1,max_length:l=null,return_tensor:d=!0,return_dict:u=!1,tokenizer_kwargs:_={},...m}={}){if("string"!=typeof(r=this.get_chat_template({chat_template:r,tools:t})))throw Error("chat_template must be a string, but got "+typeof r);let p=this._compiled_template_cache.get(r);void 0===p&&(p=new c.Template(r),this._compiled_template_cache.set(r,p));const h=Object.create(null);for(const e of we){const t=this.getToken(e);t&&(h[e]=t)}const g=p.render({messages:e,add_generation_prompt:o,tools:t,documents:s,...h,...m});if(n){const e=this._call(g,{add_special_tokens:!1,padding:a,truncation:i,max_length:l,return_tensor:d,..._});return u?e:e.input_ids}return g}}class Pe extends xe{return_token_type_ids=!0}class ke extends xe{return_token_type_ids=!0}class Fe extends xe{return_token_type_ids=!0}class ve extends xe{return_token_type_ids=!0}class ye extends xe{return_token_type_ids=!0}class Ce extends xe{return_token_type_ids=!0}class Se extends xe{return_token_type_ids=!0}class Ae extends xe{return_token_type_ids=!0}class Ee extends xe{return_token_type_ids=!0}class Le extends xe{}class Ie extends xe{}class ze extends xe{return_token_type_ids=!0;constructor(e,t){super(e,t),console.warn('WARNING: `XLMTokenizer` is not yet supported by Hugging Face\'s "fast" tokenizers library. Therefore, you may experience slightly inaccurate results.')}}class De extends xe{return_token_type_ids=!0}class je extends xe{}class Ve extends xe{}class Ne extends xe{}class Oe extends xe{constructor(e,t){super(e,t),this.languageRegex=/^[a-z]{2}_[A-Z]{2}$/,this.language_codes=this.special_tokens.filter((e=>this.languageRegex.test(e))),this.lang_to_token=e=>e}_build_translation_inputs(e,t,s){return et(this,e,t,s)}}class Be extends Oe{}class Ge extends xe{}class Re extends xe{}const qe="▁";class $e extends xe{padding_side="left";constructor(e,t){super(e,t),this.legacy=t.legacy??!0,this.legacy||(this.normalizer=null,this.pre_tokenizer=new _e({replacement:qe,add_prefix_space:!0,prepend_scheme:"first"}))}_encode_text(e){if(null===e)return null;if(this.legacy||0===e.length)return super._encode_text(e);let t=super._encode_text(qe+e.replaceAll(qe," "));return t.length>1&&t[0]===qe&&this.special_tokens.includes(t[1])&&(t=t.slice(1)),t}}class We extends xe{}class Ue extends xe{}class Qe extends xe{}class Xe extends xe{}class He extends xe{}class Je extends xe{}class Ye extends xe{}class Ke extends xe{}class Ze extends xe{}function et(e,t,s,r){if(!("language_codes"in e)||!Array.isArray(e.language_codes))throw new Error("Tokenizer must have `language_codes` attribute set and it should be an array of language ids.");if(!("languageRegex"in e&&e.languageRegex instanceof RegExp))throw new Error("Tokenizer must have `languageRegex` attribute set and it should be a regular expression.");if(!("lang_to_token"in e)||"function"!=typeof e.lang_to_token)throw new Error("Tokenizer must have `lang_to_token` attribute set and it should be a function.");const o=r.src_lang,n=r.tgt_lang;if(!e.language_codes.includes(n))throw new Error(`Target language code "${n}" is not valid. Must be one of: {${e.language_codes.join(", ")}}`);if(void 0!==o){if(!e.language_codes.includes(o))throw new Error(`Source language code "${o}" is not valid. Must be one of: {${e.language_codes.join(", ")}}`);for(const t of e.post_processor.config.single)if("SpecialToken"in t&&e.languageRegex.test(t.SpecialToken.id)){t.SpecialToken.id=e.lang_to_token(o);break}}return r.forced_bos_token_id=e.model.convert_tokens_to_ids([e.lang_to_token(n)])[0],e._call(t,s)}class tt extends xe{constructor(e,t){super(e,t),this.languageRegex=/^[a-z]{3}_[A-Z][a-z]{3}$/,this.language_codes=this.special_tokens.filter((e=>this.languageRegex.test(e))),this.lang_to_token=e=>e}_build_translation_inputs(e,t,s){return et(this,e,t,s)}}class st extends xe{constructor(e,t){super(e,t),this.languageRegex=/^__[a-z]{2,3}__$/,this.language_codes=this.special_tokens.filter((e=>this.languageRegex.test(e))).map((e=>e.slice(2,-2))),this.lang_to_token=e=>`__${e}__`}_build_translation_inputs(e,t,s){return et(this,e,t,s)}}class rt extends xe{get timestamp_begin(){return this.model.convert_tokens_to_ids(["<|notimestamps|>"])[0]+1}_decode_asr(e,{return_timestamps:t=!1,return_language:s=!1,time_precision:r=null,force_full_sequences:o=!0}={}){if(null===r)throw Error("Must specify time_precision");let n=null;const i="word"===t;function l(){return{language:n,timestamp:[null,null],text:""}}const c=[];let u=l(),_=0;const m=this.timestamp_begin,p=m+1500;let h=[],g=[],f=!1,M=null;const T=new Set(this.all_special_ids);for(const s of e){const e=s.tokens,o=i?s.token_timestamps:null;let b=null,x=m;if("stride"in s){const[t,o,n]=s.stride;if(_-=o,M=t-n,o&&(x=o/r+m),n)for(let t=e.length-1;t>=0;--t){const s=Number(e[t]);if(s>=m){if(null!==b&&(s-m)*r<M)break;b=s}}}let P=[],k=[];for(let s=0;s<e.length;++s){const M=Number(e[s]);if(T.has(M)){const e=this.decode([M]),s=d.WHISPER_LANGUAGE_MAPPING.get(e.slice(2,-2));if(void 0!==s){if(null!==n&&s!==n&&!t){h.push(P);const e=this.findLongestCommonSequence(h)[0],t=this.decode(e);u.text=t,c.push(u),h=[],P=[],u=l()}n=u.language=s}}else if(M>=m&&M<=p){const e=(M-m)*r+_,t=(0,a.round)(e,2);if(null!==b&&M>=b)f=!0;else if(f||h.length>0&&M<x)f=!1;else if(null===u.timestamp[0])u.timestamp[0]=t;else if(t===u.timestamp[0]);else{u.timestamp[1]=t,h.push(P),i&&g.push(k);const[e,s]=this.findLongestCommonSequence(h,g),r=this.decode(e);u.text=r,i&&(u.words=this.collateWordTimestamps(e,s,n)),c.push(u),h=[],P=[],g=[],k=[],u=l()}}else if(P.push(M),i){let e,t=(0,a.round)(o[s]+_,2);if(s+1<o.length){e=(0,a.round)(o[s+1]+_,2);const n=this.decode([M]);w.test(n)&&(e=(0,a.round)(Math.min(t+r,e),2))}else e=null;k.push([t,e])}}if("stride"in s){const[e,t,r]=s.stride;_+=e-r}P.length>0?(h.push(P),i&&g.push(k)):h.every((e=>0===e.length))&&(u=l(),h=[],P=[],g=[],k=[])}if(h.length>0){if(o&&t)throw new Error("Whisper did not predict an ending timestamp, which can happen if audio is cut off in the middle of a word. Also make sure WhisperTimeStampLogitsProcessor was used during generation.");const[e,s]=this.findLongestCommonSequence(h,g),r=this.decode(e);u.text=r,i&&(u.words=this.collateWordTimestamps(e,s,n)),c.push(u)}let b=Object.create(null);const x=c.map((e=>e.text)).join("");if(t||s){for(let e=0;e<c.length;++e){const r=c[e];t||delete r.timestamp,s||delete r.language}if(i){const e=[];for(const t of c)for(const s of t.words)e.push(s);b={chunks:e}}else b={chunks:c}}return[x,b]}findLongestCommonSequence(e,t=null){let s=e[0],r=s.length,o=[];const n=Array.isArray(t)&&t.length>0;let a=n?[]:null,i=n?t[0]:null;for(let l=1;l<e.length;++l){const c=e[l];let d=0,u=[r,r,0,0];const _=c.length;for(let e=1;e<r+_;++e){const o=Math.max(0,r-e),a=Math.min(r,r+_-e),m=s.slice(o,a),p=Math.max(0,e-r),h=Math.min(_,e),g=c.slice(p,h);if(m.length!==g.length)throw new Error("There is a bug within whisper `decode_asr` function, please report it. Dropping to prevent bad inference.");let f;f=n?m.filter(((e,s)=>e===g[s]&&i[o+s]<=t[l][p+s])).length:m.filter(((e,t)=>e===g[t])).length;const M=f/e+e/1e4;f>1&&M>d&&(d=M,u=[o,a,p,h])}const[m,p,h,g]=u,f=Math.floor((p+m)/2),M=Math.floor((g+h)/2);o.push(...s.slice(0,f)),s=c.slice(M),r=s.length,n&&(a.push(...i.slice(0,f)),i=t[l].slice(M))}return o.push(...s),n?(a.push(...i),[o,a]):[o,[]]}collateWordTimestamps(e,t,s){const[r,o,n]=this.combineTokensIntoWords(e,s),a=[];for(let e=0;e<r.length;++e){const s=n[e];a.push({text:r[e],timestamp:[t[s.at(0)][0],t[s.at(-1)][1]]})}return a}combineTokensIntoWords(e,t,s="\"'“¡¿([{-",r="\"'.。,，!！?？:：”)]}、"){let o,n,a;return["chinese","japanese","thai","lao","myanmar"].includes(t=t??"english")?[o,n,a]=this.splitTokensOnUnicode(e):[o,n,a]=this.splitTokensOnSpaces(e),this.mergePunctuations(o,n,a,s,r)}decode(e,t){let s;return t?.decode_with_timestamps?(e instanceof i.Tensor&&(e=p(e)),s=this.decodeWithTimestamps(e,t)):s=super.decode(e,t),s}decodeWithTimestamps(e,t){const s=t?.time_precision??.02,r=Array.from(this.all_special_ids).at(-1)+1;let o=[[]];for(let t of e)if(t=Number(t),t>=r){const e=((t-r)*s).toFixed(2);o.push(`<|${e}|>`),o.push([])}else o[o.length-1].push(t);return o=o.map((e=>"string"==typeof e?e:super.decode(e,t))),o.join("")}splitTokensOnUnicode(e){const t=this.decode(e,{decode_with_timestamps:!0}),s=[],r=[],o=[];let n=[],a=[],i=0;for(let l=0;l<e.length;++l){const c=e[l];n.push(c),a.push(l);const d=this.decode(n,{decode_with_timestamps:!0});d.includes("�")&&"�"!==t[i+d.indexOf("�")]||(s.push(d),r.push(n),o.push(a),n=[],a=[],i+=d.length)}return[s,r,o]}splitTokensOnSpaces(e){const[t,s,r]=this.splitTokensOnUnicode(e),o=[],n=[],a=[],i=new RegExp(`^[${M}]$`,"gu");for(let e=0;e<t.length;++e){const l=t[e],c=s[e],d=r[e],u=c[0]>=this.model.tokens_to_ids.get("<|endoftext|>"),_=l.startsWith(" "),m=l.trim(),p=i.test(m);if(u||_||p||0===o.length)o.push(l),n.push(c),a.push(d);else{const e=o.length-1;o[e]+=l,n[e].push(...c),a[e].push(...d)}}return[o,n,a]}mergePunctuations(e,t,s,r,n){const a=structuredClone(e),i=structuredClone(t),l=structuredClone(s);let c=a.length-2,d=a.length-1;for(;c>=0;)a[c].startsWith(" ")&&r.includes(a[c].trim())?(a[d]=a[c]+a[d],i[d]=(0,o.mergeArrays)(i[c],i[d]),l[d]=(0,o.mergeArrays)(l[c],l[d]),a[c]="",i[c]=[],l[c]=[]):d=c,--c;for(c=0,d=1;d<a.length;)!a[c].endsWith(" ")&&n.includes(a[d])?(a[c]+=a[d],i[c]=(0,o.mergeArrays)(i[c],i[d]),l[c]=(0,o.mergeArrays)(l[c],l[d]),a[d]="",i[d]=[],l[d]=[]):c=d,++d;return[a.filter((e=>e)),i.filter((e=>e.length>0)),l.filter((e=>e.length>0))]}}class ot extends xe{}class nt extends xe{}class at extends xe{}class it extends xe{constructor(e,t){super(e,t),this.languageRegex=/^(>>\w+<<)\s*/g,this.supported_language_codes=this.model.vocab.filter((e=>this.languageRegex.test(e))),console.warn('WARNING: `MarianTokenizer` is not yet supported by Hugging Face\'s "fast" tokenizers library. Therefore, you may experience slightly inaccurate results.')}_encode_text(e){if(null===e)return null;const[t,...s]=e.trim().split(this.languageRegex);if(0===s.length)return super._encode_text(t);if(2===s.length){const[e,t]=s;return this.supported_language_codes.includes(e)||console.warn(`Unsupported language code "${e}" detected, which may lead to unexpected behavior. Should be one of: ${JSON.stringify(this.supported_language_codes)}`),(0,o.mergeArrays)([e],super._encode_text(t))}}}class lt extends xe{}class ct extends xe{}class dt extends xe{}class ut extends xe{}class _t extends xe{}class mt extends xe{constructor(e,t){super(e,t),this.decoder=new ue({})}}class pt extends xe{}class ht extends xe{}class gt extends xe{}class ft{static TOKENIZER_CLASS_MAPPING={T5Tokenizer:je,DistilBertTokenizer:Le,CamembertTokenizer:Ie,DebertaTokenizer:ye,DebertaV2Tokenizer:Ce,BertTokenizer:Pe,HerbertTokenizer:Se,ConvBertTokenizer:Ae,RoFormerTokenizer:Ee,XLMTokenizer:ze,ElectraTokenizer:De,MobileBertTokenizer:Fe,SqueezeBertTokenizer:ve,AlbertTokenizer:ke,GPT2Tokenizer:Ve,BartTokenizer:Ne,MBartTokenizer:Oe,MBart50Tokenizer:Be,RobertaTokenizer:Ge,WhisperTokenizer:rt,CodeGenTokenizer:ot,CLIPTokenizer:nt,SiglipTokenizer:at,MarianTokenizer:it,BloomTokenizer:Re,NllbTokenizer:tt,M2M100Tokenizer:st,LlamaTokenizer:$e,CodeLlamaTokenizer:We,XLMRobertaTokenizer:Ue,MPNetTokenizer:Qe,FalconTokenizer:Xe,GPTNeoXTokenizer:He,EsmTokenizer:Je,Wav2Vec2CTCTokenizer:lt,BlenderbotTokenizer:ct,BlenderbotSmallTokenizer:dt,SpeechT5Tokenizer:ut,NougatTokenizer:_t,VitsTokenizer:mt,Qwen2Tokenizer:Ye,GemmaTokenizer:Ke,Grok1Tokenizer:Ze,CohereTokenizer:pt,MgpstrTokenizer:ht,Ernie4_5_Tokenizer:gt,PreTrainedTokenizer:xe};static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:r=null,local_files_only:o=!1,revision:n="main",legacy:a=null}={}){const[i,l]=await u(e,{progress_callback:t,config:s,cache_dir:r,local_files_only:o,revision:n,legacy:a}),c=l.tokenizer_class?.replace(/Fast$/,"")??"PreTrainedTokenizer";let d=this.TOKENIZER_CLASS_MAPPING[c];return d||(console.warn(`Unknown tokenizer class "${c}", attempting to construct from base class.`),d=xe),new d(i,l)}}},"./src/utils/audio.js":(e,t,s)=>{s.r(t),s.d(t,{RawAudio:()=>x,hamming:()=>_,hanning:()=>u,mel_filter_bank:()=>f,read_audio:()=>c,spectrogram:()=>w,window_function:()=>T});var r=s("./src/utils/hub.js"),o=s("./src/utils/maths.js"),n=s("./src/utils/core.js"),a=s("./src/env.js"),i=s("./src/utils/tensor.js"),l=s("?7992");async function c(e,t){if("undefined"==typeof AudioContext)throw Error("Unable to load audio from path/URL since `AudioContext` is not available in your environment. Instead, audio data should be passed directly to the pipeline/processor. For more information and some example code, see https://huggingface.co/docs/transformers.js/guides/node-audio-processing.");const s=await(await(0,r.getFile)(e)).arrayBuffer(),o=new AudioContext({sampleRate:t});void 0===t&&console.warn(`No sampling rate provided, using default of ${o.sampleRate}Hz.`);const n=await o.decodeAudioData(s);let a;if(2===n.numberOfChannels){const e=Math.sqrt(2),t=n.getChannelData(0),s=n.getChannelData(1);a=new Float32Array(t.length);for(let r=0;r<n.length;++r)a[r]=e*(t[r]+s[r])/2}else a=n.getChannelData(0);return a}function d(e,t){if(e<1)return new Float64Array;if(1===e)return new Float64Array([1]);const s=1-t,r=2*Math.PI/(e-1),o=new Float64Array(e);for(let n=0;n<e;++n)o[n]=t-s*Math.cos(n*r);return o}function u(e){return d(e,.5)}function _(e){return d(e,.54)}const m={htk:e=>2595*Math.log10(1+e/700),kaldi:e=>1127*Math.log(1+e/700),slaney:(e,t=1e3,s=15,r=27/Math.log(6.4))=>e>=t?s+Math.log(e/t)*r:3*e/200};function p(e,t="htk"){const s=m[t];if(!s)throw new Error('mel_scale should be one of "htk", "slaney" or "kaldi".');return"number"==typeof e?s(e):e.map((e=>s(e)))}const h={htk:e=>700*(10**(e/2595)-1),kaldi:e=>700*(Math.exp(e/1127)-1),slaney:(e,t=1e3,s=15,r=Math.log(6.4)/27)=>e>=s?t*Math.exp(r*(e-s)):200*e/3};function g(e,t,s){const r=(t-e)/(s-1);return Float64Array.from({length:s},((t,s)=>e+r*s))}function f(e,t,s,r,o,n=null,a="htk",i=!1){if(null!==n&&"slaney"!==n)throw new Error('norm must be one of null or "slaney"');if(e<2)throw new Error(`Require num_frequency_bins: ${e} >= 2`);if(s>r)throw new Error(`Require min_frequency: ${s} <= max_frequency: ${r}`);const l=g(p(s,a),p(r,a),t+2);let c,d=function(e,t="htk"){const s=h[t];if(!s)throw new Error('mel_scale should be one of "htk", "slaney" or "kaldi".');return"number"==typeof e?s(e):e.map((e=>s(e)))}(l,a);if(i){const t=o/(2*(e-1));c=p(Float64Array.from({length:e},((e,s)=>s*t)),a),d=l}else c=g(0,Math.floor(o/2),e);const u=function(e,t){const s=Float64Array.from({length:t.length-1},((e,s)=>t[s+1]-t[s])),r=Array.from({length:e.length},(()=>new Array(t.length)));for(let s=0;s<e.length;++s){const o=r[s];for(let r=0;r<t.length;++r)o[r]=t[r]-e[s]}const o=t.length-2,n=Array.from({length:o},(()=>new Array(e.length)));for(let t=0;t<e.length;++t){const e=r[t];for(let r=0;r<o;++r){const o=-e[r]/s[r],a=e[r+2]/s[r+1];n[r][t]=Math.max(0,Math.min(o,a))}}return n}(c,d);if(null!==n&&"slaney"===n)for(let s=0;s<t;++s){const t=u[s],r=2/(d[s+2]-d[s]);for(let s=0;s<e;++s)t[s]*=r}return u}function M(e,t,s,r,n){if(s<=0)throw new Error("reference must be greater than zero");if(r<=0)throw new Error("min_value must be greater than zero");s=Math.max(r,s);const a=Math.log10(s);for(let s=0;s<e.length;++s)e[s]=t*Math.log10(Math.max(r,e[s])-a);if(null!==n){if(n<=0)throw new Error("db_range must be greater than zero");const t=(0,o.max)(e)[0]-n;for(let s=0;s<e.length;++s)e[s]=Math.max(e[s],t)}return e}async function w(e,t,s,r,{fft_length:a=null,power:l=1,center:c=!0,pad_mode:d="reflect",onesided:u=!0,preemphasis:_=null,preemphasis_htk_flavor:m=!0,mel_filters:p=null,mel_floor:h=1e-10,log_mel:g=null,reference:f=1,min_value:w=1e-10,db_range:T=null,remove_dc_offset:b=null,min_num_frames:x=null,max_num_frames:P=null,do_pad:k=!0,transpose:F=!1}={}){const v=t.length;if(null===a&&(a=s),s>a)throw Error(`frame_length (${s}) may not be larger than fft_length (${a})`);if(v!==s)throw new Error(`Length of the window (${v}) must equal frame_length (${s})`);if(r<=0)throw new Error("hop_length must be greater than zero");if(null===l&&null!==p)throw new Error("You have provided `mel_filters` but `power` is `None`. Mel spectrogram computation is not yet supported for complex-valued spectrogram. Specify `power` to fix this issue.");if(!m)throw new Error("`preemphasis_htk_flavor=false` is not currently supported.");if(c){if("reflect"!==d)throw new Error(`pad_mode="${d}" not implemented yet.`);const t=Math.floor((a-1)/2)+1;e=function(e,t,s){const r=new e.constructor(e.length+t+s),o=e.length-1;for(let s=0;s<e.length;++s)r[t+s]=e[s];for(let s=1;s<=t;++s)r[t-s]=e[(0,n.calculateReflectOffset)(s,o)];for(let a=1;a<=s;++a)r[o+t+a]=e[(0,n.calculateReflectOffset)(o-a,o)];return r}(e,t,t)}let y=Math.floor(1+Math.floor((e.length-s)/r));null!==x&&y<x&&(y=x);const C=u?Math.floor(a/2)+1:a;let S=y,A=y;null!==P&&(P>y?k&&(A=P):A=S=P);const E=new o.FFT(a),L=new Float64Array(a),I=new Float64Array(E.outputBufferSize),z=new Float32Array(C*A);for(let o=0;o<S;++o){const n=o*r,a=Math.min(e.length-n,s);a!==s&&L.fill(0,0,s);for(let t=0;t<a;++t)L[t]=e[n+t];if(b){let e=0;for(let t=0;t<a;++t)e+=L[t];const t=e/a;for(let e=0;e<a;++e)L[e]-=t}if(null!==_){for(let e=a-1;e>=1;--e)L[e]-=_*L[e-1];L[0]*=1-_}for(let e=0;e<t.length;++e)L[e]*=t[e];E.realTransform(I,L);for(let e=0;e<C;++e){const t=e<<1;z[e*A+o]=I[t]**2+I[t+1]**2}}if(null!==l&&2!==l){const e=l/2;for(let t=0;t<z.length;++t)z[t]**=e}const D=p.length;let j=await(0,i.matmul)(new i.Tensor("float32",p.flat(),[D,C]),new i.Tensor("float32",z,[C,A]));F&&(j=j.transpose(1,0));const V=j.data;for(let e=0;e<V.length;++e)V[e]=Math.max(h,V[e]);if(null!==l&&null!==g){const e=Math.min(V.length,S*D);switch(g){case"log":for(let t=0;t<e;++t)V[t]=Math.log(V[t]);break;case"log10":for(let t=0;t<e;++t)V[t]=Math.log10(V[t]);break;case"dB":if(1===l)!function(e,t=1,s=1e-5,r=null){M(e,20,t,s,r)}(V,f,w,T);else{if(2!==l)throw new Error(`Cannot use log_mel option '${g}' with power ${l}`);!function(e,t=1,s=1e-10,r=null){M(e,10,t,s,r)}(V,f,w,T)}break;default:throw new Error(`log_mel must be one of null, 'log', 'log10' or 'dB'. Got '${g}'`)}}return j}function T(e,t,{periodic:s=!0,frame_length:r=null,center:o=!0}={}){const n=s?e+1:e;let a;switch(t){case"boxcar":a=new Float64Array(n).fill(1);break;case"hann":case"hann_window":a=u(n);break;case"hamming":a=_(n);break;case"povey":a=u(n).map((e=>Math.pow(e,.85)));break;default:throw new Error(`Unknown window type ${t}.`)}if(s&&(a=a.subarray(0,e)),null===r)return a;if(e>r)throw new Error(`Length of the window (${e}) may not be larger than frame_length (${r})`);return a}function b(e,t,s){for(let r=0;r<s.length;++r)e.setUint8(t+r,s.charCodeAt(r))}class x{constructor(e,t){this.audio=e,this.sampling_rate=t}toWav(){return function(e,t){let s=44;const r=new ArrayBuffer(s+4*e.length),o=new DataView(r);b(o,0,"RIFF"),o.setUint32(4,36+4*e.length,!0),b(o,8,"WAVE"),b(o,12,"fmt "),o.setUint32(16,16,!0),o.setUint16(20,3,!0),o.setUint16(22,1,!0),o.setUint32(24,t,!0),o.setUint32(28,4*t,!0),o.setUint16(32,4,!0),o.setUint16(34,32,!0),b(o,36,"data"),o.setUint32(40,4*e.length,!0);for(let t=0;t<e.length;++t,s+=4)o.setFloat32(s,e[t],!0);return r}(this.audio,this.sampling_rate)}toBlob(){const e=this.toWav();return new Blob([e],{type:"audio/wav"})}async save(e){let t;if(a.apis.IS_BROWSER_ENV){if(a.apis.IS_WEBWORKER_ENV)throw new Error("Unable to save a file from a Web Worker.");t=n.saveBlob}else{if(!a.apis.IS_FS_AVAILABLE)throw new Error("Unable to save because filesystem is disabled in this environment.");t=async(e,t)=>{let s=await t.arrayBuffer();l.writeFileSync(e,Buffer.from(s))}}await t(e,this.toBlob())}}},"./src/utils/constants.js":(e,t,s)=>{s.r(t),s.d(t,{CHAT_TEMPLATE_NAME:()=>l,CONFIG_NAME:()=>o,FEATURE_EXTRACTOR_NAME:()=>n,GENERATION_CONFIG_NAME:()=>c,GITHUB_ISSUE_URL:()=>r,IMAGE_PROCESSOR_NAME:()=>a,PROCESSOR_NAME:()=>i});const r="https://github.com/huggingface/transformers.js/issues/new/choose",o="config.json",n="preprocessor_config.json",a=n,i="processor_config.json",l="chat_template.jinja",c="generation_config.json"},"./src/utils/core.js":(e,t,s)=>{function r(e,t){e&&e(t)}function o(e){return Object.fromEntries(Object.entries(e).map((([e,t])=>[t,e])))}function n(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function a(e){return"TypedArray"===e?.prototype?.__proto__?.constructor?.name}function i(e){return Number.isInteger(e)||"bigint"==typeof e}function l(e){return null==e||-1===e}function c(e){const t=[];let s=e;for(;Array.isArray(s);)t.push(s.length),s=s[0];return t}function d(e,t,s=void 0){const r=e[t];if(void 0!==r)return delete e[t],r;if(void 0===s)throw Error(`Key ${t} does not exist in object.`);return s}function u(...e){return Array.prototype.concat.apply([],e)}function _(...e){return e.reduce(((e,t)=>e.flatMap((e=>t.map((t=>[e,t]))))))}function m(e,t){return Math.abs((e+t)%(2*t)-t)}function p(e,t){const s=URL.createObjectURL(t),r=document.createElement("a");r.href=s,r.download=e,r.click(),r.remove(),URL.revokeObjectURL(s)}function h(e,t){return Object.assign({},...t.map((t=>{if(void 0!==e[t])return{[t]:e[t]}})))}function g(e){let t=0;for(const s of e)++t;return t}function f(e,t){let s=0;for(const r of e)r===t&&++s;return s}s.r(t),s.d(t,{calculateDimensions:()=>c,calculateReflectOffset:()=>m,count:()=>f,dispatchCallback:()=>r,escapeRegExp:()=>n,isIntegralNumber:()=>i,isNullishDimension:()=>l,isTypedArray:()=>a,len:()=>g,mergeArrays:()=>u,pick:()=>h,pop:()=>d,product:()=>_,reverseDictionary:()=>o,saveBlob:()=>p})},"./src/utils/data-structures.js":(e,t,s)=>{s.r(t),s.d(t,{CharTrie:()=>o,DictionarySplitter:()=>l,LRUCache:()=>c,PriorityQueue:()=>r,TokenLattice:()=>a});class r{constructor(e=(e,t)=>e>t,t=1/0){this._heap=[],this._comparator=e,this._maxSize=t}get size(){return this._heap.length}isEmpty(){return 0===this.size}peek(){return this._heap[0]}push(...e){return this.extend(e)}extend(e){for(const t of e)if(this.size<this._maxSize)this._heap.push(t),this._siftUp();else{const e=this._smallest();this._comparator(t,this._heap[e])&&(this._heap[e]=t,this._siftUpFrom(e))}return this.size}pop(){const e=this.peek(),t=this.size-1;return t>0&&this._swap(0,t),this._heap.pop(),this._siftDown(),e}replace(e){const t=this.peek();return this._heap[0]=e,this._siftDown(),t}_parent(e){return(e+1>>>1)-1}_left(e){return 1+(e<<1)}_right(e){return e+1<<1}_greater(e,t){return this._comparator(this._heap[e],this._heap[t])}_swap(e,t){const s=this._heap[e];this._heap[e]=this._heap[t],this._heap[t]=s}_siftUp(){this._siftUpFrom(this.size-1)}_siftUpFrom(e){for(;e>0&&this._greater(e,this._parent(e));)this._swap(e,this._parent(e)),e=this._parent(e)}_siftDown(){let e=0;for(;this._left(e)<this.size&&this._greater(this._left(e),e)||this._right(e)<this.size&&this._greater(this._right(e),e);){const t=this._right(e)<this.size&&this._greater(this._right(e),this._left(e))?this._right(e):this._left(e);this._swap(e,t),e=t}}_smallest(){return 2**Math.floor(Math.log2(this.size))-1}}class o{constructor(){this.root=n.default()}extend(e){for(const t of e)this.push(t)}push(e){let t=this.root;for(const s of e){let e=t.children.get(s);void 0===e&&(e=n.default(),t.children.set(s,e)),t=e}t.isLeaf=!0}*commonPrefixSearch(e){let t=this.root;if(void 0===t)return;let s="";for(const r of e){if(s+=r,t=t.children.get(r),void 0===t)return;t.isLeaf&&(yield s)}}}class n{constructor(e,t){this.isLeaf=e,this.children=t}static default(){return new n(!1,new Map)}}class a{constructor(e,t,s){this.chars=Array.from(e),this.len=this.chars.length,this.bosTokenId=t,this.eosTokenId=s,this.nodes=[],this.beginNodes=Array.from({length:this.len+1},(()=>[])),this.endNodes=Array.from({length:this.len+1},(()=>[]));const r=new i(this.bosTokenId,0,0,0,0),o=new i(this.eosTokenId,1,this.len,0,0);this.nodes.push(r.clone()),this.nodes.push(o.clone()),this.beginNodes[this.len].push(o),this.endNodes[0].push(r)}insert(e,t,s,r){const o=this.nodes.length,n=new i(r,o,e,t,s);this.beginNodes[e].push(n),this.endNodes[e+t].push(n),this.nodes.push(n)}viterbi(){const e=this.len;let t=0;for(;t<=e;){if(0==this.beginNodes[t].length)return[];for(let e of this.beginNodes[t]){e.prev=null;let s=0,r=null;for(let o of this.endNodes[t]){const t=o.backtraceScore+e.score;(null===r||t>s)&&(r=o.clone(),s=t)}if(null===r)return[];e.prev=r,e.backtraceScore=s}++t}const s=[],r=this.beginNodes[e][0].prev;if(null===r)return[];let o=r.clone();for(;null!==o.prev;){s.push(o.clone());const e=o.clone();o=e.prev.clone()}return s.reverse(),s}piece(e){return this.chars.slice(e.pos,e.pos+e.length).join("")}tokens(){return this.viterbi().map((e=>this.piece(e)))}tokenIds(){return this.viterbi().map((e=>e.tokenId))}}class i{constructor(e,t,s,r,o){this.tokenId=e,this.nodeId=t,this.pos=s,this.length=r,this.score=o,this.prev=null,this.backtraceScore=0}clone(){const e=new i(this.tokenId,this.nodeId,this.pos,this.length,this.score);return e.prev=this.prev,e.backtraceScore=this.backtraceScore,e}}class l{constructor(e){this.trie=this._buildTrie(e)}_buildTrie(e){const t=Object.create(null);for(const s of e){let e=t;for(let t=0;t<s.length;++t)e=e[s[t]]??=Object.create(null);e.end=s}return t}split(e){const t=[],s=e.length;let r=0,o=0;for(;o<s;){let n=this.trie,a=null,i=o;for(;i<s&&(n=n[e[i]]);)n.end&&(a=n.end),++i;a?(o>r&&t.push(e.slice(r,o)),t.push(a),o+=a.length,r=o):++o}return r<s&&t.push(e.slice(r)),t}}class c{constructor(e){this.capacity=e,this.cache=new Map}get(e){if(!this.cache.has(e))return;const t=this.cache.get(e);return this.cache.delete(e),this.cache.set(e,t),t}put(e,t){this.cache.has(e)&&this.cache.delete(e),this.cache.set(e,t),this.cache.size>this.capacity&&this.cache.delete(this.cache.keys().next().value)}clear(){this.cache.clear()}}},"./src/utils/devices.js":(e,t,s)=>{s.r(t),s.d(t,{DEVICE_TYPES:()=>r});const r=Object.freeze({auto:"auto",gpu:"gpu",cpu:"cpu",wasm:"wasm",webgpu:"webgpu",cuda:"cuda",dml:"dml",webnn:"webnn","webnn-npu":"webnn-npu","webnn-gpu":"webnn-gpu","webnn-cpu":"webnn-cpu"})},"./src/utils/dtypes.js":(e,t,s)=>{s.r(t),s.d(t,{DATA_TYPES:()=>a,DEFAULT_DEVICE_DTYPE_MAPPING:()=>i,DEFAULT_DTYPE_SUFFIX_MAPPING:()=>l,isWebGpuFp16Supported:()=>n});var r=s("./src/env.js"),o=s("./src/utils/devices.js");const n=function(){let e;return async function(){if(void 0===e)if(r.apis.IS_WEBGPU_AVAILABLE)try{const t=await navigator.gpu.requestAdapter();e=t.features.has("shader-f16")}catch(t){e=!1}else e=!1;return e}}(),a=Object.freeze({auto:"auto",fp32:"fp32",fp16:"fp16",q8:"q8",int8:"int8",uint8:"uint8",q4:"q4",bnb4:"bnb4",q4f16:"q4f16"}),i=Object.freeze({[o.DEVICE_TYPES.wasm]:a.q8}),l=Object.freeze({[a.fp32]:"",[a.fp16]:"_fp16",[a.int8]:"_int8",[a.uint8]:"_uint8",[a.q8]:"_quantized",[a.q4]:"_q4",[a.q4f16]:"_q4f16",[a.bnb4]:"_bnb4"})},"./src/utils/generic.js":(e,t,s)=>{s.r(t),s.d(t,{Callable:()=>r});const r=class{constructor(){let e=function(...t){return e._call(...t)};return Object.setPrototypeOf(e,new.target.prototype)}_call(...e){throw Error("Must implement _call method in subclass")}}},"./src/utils/hub.js":(e,t,s)=>{s.r(t),s.d(t,{MAX_EXTERNAL_DATA_CHUNKS:()=>i,getFile:()=>_,getModelFile:()=>h,getModelJSON:()=>f,getModelText:()=>g});var r=s("?7992"),o=s("?5af5"),n=s("./src/env.js"),a=s("./src/utils/core.js");const i=100,l={txt:"text/plain",html:"text/html",css:"text/css",js:"text/javascript",json:"application/json",png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif"};class c{constructor(e){if(this.filePath=e,this.headers=new Headers,this.exists=r.existsSync(e),this.exists){this.status=200,this.statusText="OK";let t=r.statSync(e);this.headers.set("content-length",t.size.toString()),this.updateContentType();const s=r.createReadStream(e);this.body=new ReadableStream({start(e){s.on("data",(t=>e.enqueue(t))),s.on("end",(()=>e.close())),s.on("error",(t=>e.error(t)))},cancel(){s.destroy()}})}else this.status=404,this.statusText="Not Found",this.body=null}updateContentType(){const e=this.filePath.toString().split(".").pop().toLowerCase();this.headers.set("content-type",l[e]??"application/octet-stream")}clone(){let e=new c(this.filePath);return e.exists=this.exists,e.status=this.status,e.statusText=this.statusText,e.headers=new Headers(this.headers),e}async arrayBuffer(){return(await r.promises.readFile(this.filePath)).buffer}async blob(){const e=await r.promises.readFile(this.filePath);return new Blob([e],{type:this.headers.get("content-type")})}async text(){return await r.promises.readFile(this.filePath,"utf8")}async json(){return JSON.parse(await this.text())}}function d(e,t=null,s=null){let r;try{r=new URL(e)}catch(e){return!1}return!(t&&!t.includes(r.protocol))&&!(s&&!s.includes(r.hostname))}const u=/^(\b[\w\-.]+\b\/)?\b[\w\-.]{1,96}\b$/;async function _(e){if(n.env.useFS&&!d(e,["http:","https:","blob:"]))return new c(e instanceof URL?"file:"===e.protocol?e.pathname:e.toString():e);if("undefined"!=typeof process&&"node"===process?.release?.name){const t=!!process.env?.TESTING_REMOTELY,s=n.env.version,r=new Headers;r.set("User-Agent",`transformers.js/${s}; is_ci/${t};`);if(d(e,["http:","https:"],["huggingface.co","hf.co"])){const e=process.env?.HF_TOKEN??process.env?.HF_ACCESS_TOKEN;e&&r.set("Authorization",`Bearer ${e}`)}return fetch(e,{headers:r})}return fetch(e)}const m={400:"Bad request error occurred while trying to load file",401:"Unauthorized access to file",403:"Forbidden access to file",404:"Could not locate file",408:"Request timeout error occurred while trying to load file",500:"Internal server error error occurred while trying to load file",502:"Bad gateway error occurred while trying to load file",503:"Service unavailable error occurred while trying to load file",504:"Gateway timeout error occurred while trying to load file"};class p{constructor(e){this.path=e}async match(e){let t=o.join(this.path,e),s=new c(t);return s.exists?s:void 0}async put(e,t,s=void 0){let n=o.join(this.path,e);try{const e=t.headers.get("Content-Length"),a=parseInt(e??"0");let i=0;await r.promises.mkdir(o.dirname(n),{recursive:!0});const l=r.createWriteStream(n),c=t.body.getReader();for(;;){const{done:e,value:t}=await c.read();if(e)break;await new Promise(((e,s)=>{l.write(t,(t=>{t?s(t):e()}))})),i+=t.length;const r=a?i/a*100:0;s?.({progress:r,loaded:i,total:a})}l.close()}catch(e){try{await r.promises.unlink(n)}catch{}throw e}}}async function h(e,t,s=!0,r={},o=!1){if(!n.env.allowLocalModels){if(r.local_files_only)throw Error("Invalid configuration detected: local models are disabled (`env.allowLocalModels=false`) but you have requested to only use local models (`local_files_only=true`).");if(!n.env.allowRemoteModels)throw Error("Invalid configuration detected: both local and remote models are disabled. Fix by setting `env.allowLocalModels` or `env.allowRemoteModels` to `true`.")}let i;if((0,a.dispatchCallback)(r.progress_callback,{status:"initiate",name:e,file:t}),!i&&n.env.useCustomCache){if(!n.env.customCache)throw Error("`env.useCustomCache=true`, but `env.customCache` is not defined.");if(!n.env.customCache.match||!n.env.customCache.put)throw new Error("`env.customCache` must be an object which implements the `match` and `put` functions of the Web Cache API. For more information, see https://developer.mozilla.org/en-US/docs/Web/API/Cache");i=n.env.customCache}if(!i&&n.env.useBrowserCache){if("undefined"==typeof caches)throw Error("Browser cache is not available in this environment.");try{i=await caches.open("transformers-cache")}catch(e){console.warn("An error occurred while opening the browser cache:",e)}}if(!i&&n.env.useFSCache){if(!n.apis.IS_FS_AVAILABLE)throw Error("File System Cache is not available in this environment.");i=new p(r.cache_dir??n.env.cacheDir)}const l=r.revision??"main",h=M(e,t),g=(f=e,!(!u.test(f)||f.includes("..")||f.includes("--")||f.endsWith(".git")||f.endsWith(".ipynb")));var f;const w=g?M(n.env.localModelPath,h):h,T=M(n.env.remoteHost,n.env.remotePathTemplate.replaceAll("{model}",e).replaceAll("{revision}",encodeURIComponent(l)),t);let b;const x=i instanceof p?"main"===l?h:M(e,l,t):T;let P,k=!1;i&&(P=await async function(e,...t){for(let s of t)try{let t=await e.match(s);if(t)return t}catch(e){continue}}(i,w,x));const F=void 0!==P;if(void 0===P){if(n.env.allowLocalModels){if(d(h,["http:","https:"])){if(r.local_files_only)throw new Error(`\`local_files_only=true\`, but attempted to load a remote file from: ${h}.`);if(!n.env.allowRemoteModels)throw new Error(`\`env.allowRemoteModels=false\`, but attempted to load a remote file from: ${h}.`)}else try{P=await _(w),b=w}catch(e){console.warn(`Unable to load from local path "${w}": "${e}"`)}}if(void 0===P||404===P.status){if(r.local_files_only||!n.env.allowRemoteModels){if(s)throw Error(`\`local_files_only=true\` or \`env.allowRemoteModels=false\` and file was not found locally at "${w}".`);return null}if(!g)throw Error(`Local file missing at "${w}" and download aborted due to invalid model ID "${e}".`);if(P=await _(T),200!==P.status)return function(e,t,s){if(!s)return null;const r=m[e]??`Error (${e}) occurred while trying to load file`;throw Error(`${r}: "${t}".`)}(P.status,T,s);b=x}k=i&&"undefined"!=typeof Response&&P instanceof Response&&200===P.status}let v;if((0,a.dispatchCallback)(r.progress_callback,{status:"download",name:e,file:t}),!n.apis.IS_NODE_ENV||!o){let s;r.progress_callback?F&&"undefined"!=typeof navigator&&/firefox/i.test(navigator.userAgent)?(s=new Uint8Array(await P.arrayBuffer()),(0,a.dispatchCallback)(r.progress_callback,{status:"progress",name:e,file:t,progress:100,loaded:s.length,total:s.length})):s=await async function(e,t){const s=e.headers.get("Content-Length");null===s&&console.warn("Unable to determine content-length from response headers. Will expand buffer when needed.");let r=parseInt(s??"0"),o=new Uint8Array(r),n=0;const a=e.body.getReader();async function i(){const{done:e,value:s}=await a.read();if(e)return;const l=n+s.length;if(l>r){r=l;const e=new Uint8Array(r);e.set(o),o=e}o.set(s,n),n=l;return t({progress:n/r*100,loaded:n,total:r}),i()}return await i(),o}(P,(s=>{(0,a.dispatchCallback)(r.progress_callback,{status:"progress",name:e,file:t,...s})})):s=new Uint8Array(await P.arrayBuffer()),v=s}if(k&&b&&void 0===await i.match(b)&&(v?await i.put(b,new Response(v,{headers:P.headers})).catch((e=>{console.warn(`Unable to add response to browser cache: ${e}.`)})):await i.put(b,P,r.progress_callback)),(0,a.dispatchCallback)(r.progress_callback,{status:"done",name:e,file:t}),v){if(!n.apis.IS_NODE_ENV&&o)throw new Error("Cannot return path in a browser environment.");return v}if(P instanceof c)return P.filePath;const y=await(i?.match(b));if(y instanceof c)return y.filePath;if(y instanceof Response)return new Uint8Array(await y.arrayBuffer());if("string"==typeof y)return y;throw new Error("Unable to get model file path or buffer.")}async function g(e,t,s=!0,r={}){const o=await h(e,t,s,r,!1);if(null===o)return null;return new TextDecoder("utf-8").decode(o)}async function f(e,t,s=!0,r={}){const o=await g(e,t,s,r);return null===o?{}:JSON.parse(o)}function M(...e){return(e=e.map(((t,s)=>(s&&(t=t.replace(new RegExp("^/"),"")),s!==e.length-1&&(t=t.replace(new RegExp("/$"),"")),t)))).join("/")}},"./src/utils/image.js":(e,t,s)=>{s.r(t),s.d(t,{RawImage:()=>p,load_image:()=>h});var r=s("./src/utils/core.js"),o=s("./src/utils/hub.js"),n=s("./src/env.js"),a=s("./src/utils/tensor.js"),i=s("?2b25");let l,c,d;const u=n.apis.IS_BROWSER_ENV||n.apis.IS_WEBWORKER_ENV;if(u)l=(e,t)=>{if(!self.OffscreenCanvas)throw new Error("OffscreenCanvas not supported by this browser.");return new self.OffscreenCanvas(e,t)},d=self.createImageBitmap,c=self.ImageData;else{if(!i)throw new Error("Unable to load image processing library.");d=async e=>{const t=(await e.metadata()).channels,{data:s,info:r}=await e.rotate().raw().toBuffer({resolveWithObject:!0}),o=new p(new Uint8ClampedArray(s),r.width,r.height,r.channels);return void 0!==t&&t!==r.channels&&o.convert(t),o}}const _={0:"nearest",1:"lanczos",2:"bilinear",3:"bicubic",4:"box",5:"hamming"},m=new Map([["png","image/png"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["gif","image/gif"]]);class p{constructor(e,t,s,r){this.data=e,this.width=t,this.height=s,this.channels=r}get size(){return[this.width,this.height]}static async read(e){if(e instanceof p)return e;if("string"==typeof e||e instanceof URL)return await this.fromURL(e);if(e instanceof Blob)return await this.fromBlob(e);if("undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas)return this.fromCanvas(e);throw new Error("Unsupported input type: "+typeof e)}static fromCanvas(e){if(!u)throw new Error("fromCanvas() is only supported in browser environments.");const t=e.getContext("2d").getImageData(0,0,e.width,e.height).data;return new p(t,e.width,e.height,4)}static async fromURL(e){const t=await(0,o.getFile)(e);if(200!==t.status)throw new Error(`Unable to read image from "${e}" (${t.status} ${t.statusText})`);const s=await t.blob();return this.fromBlob(s)}static async fromBlob(e){if(u){const t=await d(e),s=l(t.width,t.height).getContext("2d");return s.drawImage(t,0,0),new this(s.getImageData(0,0,t.width,t.height).data,t.width,t.height,4)}{const t=i(await e.arrayBuffer());return await d(t)}}static fromTensor(e,t="CHW"){if(3!==e.dims.length)throw new Error(`Tensor should have 3 dimensions, but has ${e.dims.length} dimensions.`);if("CHW"===t)e=e.transpose(1,2,0);else if("HWC"!==t)throw new Error(`Unsupported channel format: ${t}`);if(!(e.data instanceof Uint8ClampedArray||e.data instanceof Uint8Array))throw new Error(`Unsupported tensor type: ${e.type}`);switch(e.dims[2]){case 1:case 2:case 3:case 4:return new p(e.data,e.dims[1],e.dims[0],e.dims[2]);default:throw new Error(`Unsupported number of channels: ${e.dims[2]}`)}}grayscale(){if(1===this.channels)return this;const e=new Uint8ClampedArray(this.width*this.height*1);switch(this.channels){case 3:case 4:for(let t=0,s=0;t<this.data.length;t+=this.channels){const r=this.data[t],o=this.data[t+1],n=this.data[t+2];e[s++]=Math.round(.2989*r+.587*o+.114*n)}break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this._update(e,this.width,this.height,1)}rgb(){if(3===this.channels)return this;const e=new Uint8ClampedArray(this.width*this.height*3);switch(this.channels){case 1:for(let t=0,s=0;t<this.data.length;++t)e[s++]=this.data[t],e[s++]=this.data[t],e[s++]=this.data[t];break;case 4:for(let t=0,s=0;t<this.data.length;t+=4)e[s++]=this.data[t],e[s++]=this.data[t+1],e[s++]=this.data[t+2];break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this._update(e,this.width,this.height,3)}rgba(){if(4===this.channels)return this;const e=new Uint8ClampedArray(this.width*this.height*4);switch(this.channels){case 1:for(let t=0,s=0;t<this.data.length;++t)e[s++]=this.data[t],e[s++]=this.data[t],e[s++]=this.data[t],e[s++]=255;break;case 3:for(let t=0,s=0;t<this.data.length;t+=3)e[s++]=this.data[t],e[s++]=this.data[t+1],e[s++]=this.data[t+2],e[s++]=255;break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this._update(e,this.width,this.height,4)}putAlpha(e){if(e.width!==this.width||e.height!==this.height)throw new Error(`Expected mask size to be ${this.width}x${this.height}, but got ${e.width}x${e.height}`);if(1!==e.channels)throw new Error(`Expected mask to have 1 channel, but got ${e.channels}`);const t=this.data,s=e.data,r=this.width*this.height;if(3===this.channels){const e=new Uint8ClampedArray(4*r);for(let o=0,n=0,a=0;o<r;++o)e[a++]=t[n++],e[a++]=t[n++],e[a++]=t[n++],e[a++]=s[o];return this._update(e,this.width,this.height,4)}if(4===this.channels){for(let e=0;e<r;++e)t[4*e+3]=s[e];return this}throw new Error(`Expected image to have 3 or 4 channels, but got ${this.channels}`)}async resize(e,t,{resample:s=2}={}){if(this.width===e&&this.height===t)return this;let o=_[s]??s;const n=(0,r.isNullishDimension)(e),a=(0,r.isNullishDimension)(t);if(n&&a)return this;if(n?e=t/this.height*this.width:a&&(t=e/this.width*this.height),u){const s=this.channels,r=this.toCanvas(),o=l(e,t).getContext("2d");o.drawImage(r,0,0,e,t);return new p(o.getImageData(0,0,e,t).data,e,t,4).convert(s)}{let s=this.toSharp();switch(o){case"box":case"hamming":"box"!==o&&"hamming"!==o||(console.warn(`Resampling method ${o} is not yet supported. Using bilinear instead.`),o="bilinear");case"nearest":case"bilinear":case"bicubic":s=s.affine([e/this.width,0,0,t/this.height],{interpolator:o});break;case"lanczos":s=s.resize({width:e,height:t,fit:"fill",kernel:"lanczos3"});break;default:throw new Error(`Resampling method ${o} is not supported.`)}return await d(s)}}async pad([e,t,s,r]){if(e=Math.max(e,0),t=Math.max(t,0),s=Math.max(s,0),r=Math.max(r,0),0===e&&0===t&&0===s&&0===r)return this;if(u){const o=this.channels,n=this.toCanvas(),a=this.width+e+t,i=this.height+s+r,c=l(a,i).getContext("2d");c.drawImage(n,0,0,this.width,this.height,e,s,this.width,this.height);return new p(c.getImageData(0,0,a,i).data,a,i,4).convert(o)}{const o=this.toSharp().extend({left:e,right:t,top:s,bottom:r});return await d(o)}}async crop([e,t,s,r]){if(e=Math.max(e,0),t=Math.max(t,0),s=Math.min(s,this.width-1),r=Math.min(r,this.height-1),0===e&&0===t&&s===this.width-1&&r===this.height-1)return this;const o=s-e+1,n=r-t+1;if(u){const s=this.channels,r=this.toCanvas(),a=l(o,n).getContext("2d");a.drawImage(r,e,t,o,n,0,0,o,n);return new p(a.getImageData(0,0,o,n).data,o,n,4).convert(s)}{const s=this.toSharp().extract({left:e,top:t,width:o,height:n});return await d(s)}}async center_crop(e,t){if(this.width===e&&this.height===t)return this;const s=(this.width-e)/2,r=(this.height-t)/2;if(u){const o=this.channels,n=this.toCanvas(),a=l(e,t).getContext("2d");let i=0,c=0,d=0,u=0;s>=0?i=s:d=-s,r>=0?c=r:u=-r,a.drawImage(n,i,c,e,t,d,u,e,t);return new p(a.getImageData(0,0,e,t).data,e,t,4).convert(o)}{let o=this.toSharp();if(s>=0&&r>=0)o=o.extract({left:Math.floor(s),top:Math.floor(r),width:e,height:t});else if(s<=0&&r<=0){const n=Math.floor(-r),a=Math.floor(-s);o=o.extend({top:n,left:a,right:e-this.width-a,bottom:t-this.height-n})}else{let n=[0,0],a=0;r<0?(n[0]=Math.floor(-r),n[1]=t-this.height-n[0]):a=Math.floor(r);let i=[0,0],l=0;s<0?(i[0]=Math.floor(-s),i[1]=e-this.width-i[0]):l=Math.floor(s),o=o.extend({top:n[0],bottom:n[1],left:i[0],right:i[1]}).extract({left:l,top:a,width:e,height:t})}return await d(o)}}async toBlob(e="image/png",t=1){if(!u)throw new Error("toBlob() is only supported in browser environments.");const s=this.toCanvas();return await s.convertToBlob({type:e,quality:t})}toTensor(e="CHW"){let t=new a.Tensor("uint8",new Uint8Array(this.data),[this.height,this.width,this.channels]);if("HWC"===e);else{if("CHW"!==e)throw new Error(`Unsupported channel format: ${e}`);t=t.permute(2,0,1)}return t}toCanvas(){if(!u)throw new Error("toCanvas() is only supported in browser environments.");const e=this.clone().rgba(),t=l(e.width,e.height),s=new c(e.data,e.width,e.height);return t.getContext("2d").putImageData(s,0,0),t}split(){const{data:e,width:t,height:s,channels:r}=this,o=e.constructor,n=e.length/r,a=Array.from({length:r},(()=>new o(n)));for(let t=0;t<n;++t){const s=r*t;for(let o=0;o<r;++o)a[o][t]=e[s+o]}return a.map((e=>new p(e,t,s,1)))}_update(e,t,s,r=null){return this.data=e,this.width=t,this.height=s,null!==r&&(this.channels=r),this}clone(){return new p(this.data.slice(),this.width,this.height,this.channels)}convert(e){if(this.channels===e)return this;switch(e){case 1:this.grayscale();break;case 3:this.rgb();break;case 4:this.rgba();break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this}async save(e){if(!u){if(n.apis.IS_FS_AVAILABLE){const t=this.toSharp();return await t.toFile(e)}throw new Error("Unable to save the image because filesystem is disabled in this environment.")}{if(n.apis.IS_WEBWORKER_ENV)throw new Error("Unable to save an image from a Web Worker.");const t=e.split(".").pop().toLowerCase(),s=m.get(t)??"image/png",o=await this.toBlob(s);(0,r.saveBlob)(e,o)}}toSharp(){if(u)throw new Error("toSharp() is only supported in server-side environments.");return i(this.data,{raw:{width:this.width,height:this.height,channels:this.channels}})}}const h=p.read.bind(p)},"./src/utils/maths.js":(e,t,s)=>{function r(e,[t,s,r],[o,n],a="bilinear",i=!1){const l=n/r,c=o/s,d=new e.constructor(o*n*t),u=s*r,_=o*n;for(let a=0;a<o;++a)for(let o=0;o<n;++o){const i=a*n+o,m=(o+.5)/l-.5,p=(a+.5)/c-.5;let h=Math.floor(m),g=Math.floor(p);const f=Math.min(h+1,r-1),M=Math.min(g+1,s-1);h=Math.max(h,0),g=Math.max(g,0);const w=m-h,T=p-g,b=(1-w)*(1-T),x=w*(1-T),P=(1-w)*T,k=w*T,F=g*r,v=M*r,y=F+h,C=F+f,S=v+h,A=v+f;for(let s=0;s<t;++s){const t=s*u;d[s*_+i]=b*e[t+y]+x*e[t+C]+P*e[t+S]+k*e[t+A]}}return d}function o(e,t,s){const r=new Array(s.length),o=new Array(s.length);for(let e=s.length-1,n=1;e>=0;--e)o[e]=n,r[e]=t[s[e]],n*=r[e];const n=s.map(((e,t)=>o[s.indexOf(t)])),a=new e.constructor(e.length);for(let s=0;s<e.length;++s){let r=0;for(let e=t.length-1,o=s;e>=0;--e)r+=o%t[e]*n[e],o=Math.floor(o/t[e]);a[r]=e[s]}return[a,r]}function n(e){const t=u(e)[0],s=e.map((e=>Math.exp(e-t))),r=s.reduce(((e,t)=>e+t),0);return s.map((e=>e/r))}function a(e){const t=u(e)[0];let s=0;for(let r=0;r<e.length;++r)s+=Math.exp(e[r]-t);const r=Math.log(s);return e.map((e=>e-t-r))}function i(e,t){let s=0;for(let r=0;r<e.length;++r)s+=e[r]*t[r];return s}function l(e,t){return i(e,t)/(c(e)*c(t))}function c(e){return Math.sqrt(e.reduce(((e,t)=>e+t*t),0))}function d(e){if(0===e.length)throw Error("Array must not be empty");let t=e[0],s=0;for(let r=1;r<e.length;++r)e[r]<t&&(t=e[r],s=r);return[t,s]}function u(e){if(0===e.length)throw Error("Array must not be empty");let t=e[0],s=0;for(let r=1;r<e.length;++r)e[r]>t&&(t=e[r],s=r);return[t,s]}function _(e){return e>0&&!(e&e-1)}s.r(t),s.d(t,{FFT:()=>h,bankers_round:()=>M,cos_sim:()=>l,dot:()=>i,dynamic_time_warping:()=>w,interpolate_data:()=>r,log_softmax:()=>a,magnitude:()=>c,max:()=>u,medianFilter:()=>g,min:()=>d,permute_data:()=>o,round:()=>f,softmax:()=>n});class m{constructor(e){if(this.size=0|e,this.size<=1||!_(this.size))throw new Error("FFT size must be a power of two larger than 1");this._csize=e<<1,this.table=new Float64Array(2*this.size);for(let e=0;e<this.table.length;e+=2){const t=Math.PI*e/this.size;this.table[e]=Math.cos(t),this.table[e+1]=-Math.sin(t)}let t=0;for(let e=1;this.size>e;e<<=1)++t;this._width=t%2==0?t-1:t,this._bitrev=new Int32Array(1<<this._width);for(let e=0;e<this._bitrev.length;++e){this._bitrev[e]=0;for(let t=0;t<this._width;t+=2){const s=this._width-t-2;this._bitrev[e]|=(e>>>t&3)<<s}}}createComplexArray(){return new Float64Array(this._csize)}fromComplexArray(e,t){const s=t||new Array(e.length>>>1);for(let t=0;t<e.length;t+=2)s[t>>>1]=e[t];return s}toComplexArray(e,t){const s=t||this.createComplexArray();for(let t=0;t<s.length;t+=2)s[t]=e[t>>>1],s[t+1]=0;return s}transform(e,t){if(e===t)throw new Error("Input and output buffers must be different");this._transform4(e,t,1)}realTransform(e,t){if(e===t)throw new Error("Input and output buffers must be different");this._realTransform4(e,t,1)}inverseTransform(e,t){if(e===t)throw new Error("Input and output buffers must be different");this._transform4(e,t,-1);for(let t=0;t<e.length;++t)e[t]/=this.size}_transform4(e,t,s){const r=this._csize;let o,n,a=1<<this._width,i=r/a<<1;const l=this._bitrev;if(4===i)for(o=0,n=0;o<r;o+=i,++n){const s=l[n];this._singleTransform2(t,e,o,s,a)}else for(o=0,n=0;o<r;o+=i,++n){const r=l[n];this._singleTransform4(t,e,o,r,a,s)}const c=this.table;for(a>>=2;a>=2;a>>=2){i=r/a<<1;const t=i>>>2;for(o=0;o<r;o+=i){const r=o+t-1;for(let n=o,i=0;n<r;n+=2,i+=a){const r=n,o=r+t,a=o+t,l=a+t,d=e[r],u=e[r+1],_=e[o],m=e[o+1],p=e[a],h=e[a+1],g=e[l],f=e[l+1],M=c[i],w=s*c[i+1],T=_*M-m*w,b=_*w+m*M,x=c[2*i],P=s*c[2*i+1],k=p*x-h*P,F=p*P+h*x,v=c[3*i],y=s*c[3*i+1],C=g*v-f*y,S=g*y+f*v,A=d+k,E=u+F,L=d-k,I=u-F,z=T+C,D=b+S,j=s*(T-C),V=s*(b-S);e[r]=A+z,e[r+1]=E+D,e[o]=L+V,e[o+1]=I-j,e[a]=A-z,e[a+1]=E-D,e[l]=L-V,e[l+1]=I+j}}}}_singleTransform2(e,t,s,r,o){const n=e[r],a=e[r+1],i=e[r+o],l=e[r+o+1];t[s]=n+i,t[s+1]=a+l,t[s+2]=n-i,t[s+3]=a-l}_singleTransform4(e,t,s,r,o,n){const a=2*o,i=3*o,l=e[r],c=e[r+1],d=e[r+o],u=e[r+o+1],_=e[r+a],m=e[r+a+1],p=e[r+i],h=e[r+i+1],g=l+_,f=c+m,M=l-_,w=c-m,T=d+p,b=u+h,x=n*(d-p),P=n*(u-h);t[s]=g+T,t[s+1]=f+b,t[s+2]=M+P,t[s+3]=w-x,t[s+4]=g-T,t[s+5]=f-b,t[s+6]=M-P,t[s+7]=w+x}_realTransform4(e,t,s){const r=this._csize;let o,n,a=1<<this._width,i=r/a<<1;const l=this._bitrev;if(4===i)for(o=0,n=0;o<r;o+=i,++n){const s=l[n];this._singleRealTransform2(t,e,o,s>>>1,a>>>1)}else for(o=0,n=0;o<r;o+=i,++n){const r=l[n];this._singleRealTransform4(t,e,o,r>>>1,a>>>1,s)}const c=this.table;for(a>>=2;a>=2;a>>=2){i=r/a<<1;const t=i>>>1,n=t>>>1,l=n>>>1;for(o=0;o<r;o+=i)for(let r=0,i=0;r<=l;r+=2,i+=a){const a=o+r,d=a+n,u=d+n,_=u+n,m=e[a],p=e[a+1],h=e[d],g=e[d+1],f=e[u],M=e[u+1],w=e[_],T=e[_+1],b=m,x=p,P=c[i],k=s*c[i+1],F=h*P-g*k,v=h*k+g*P,y=c[2*i],C=s*c[2*i+1],S=f*y-M*C,A=f*C+M*y,E=c[3*i],L=s*c[3*i+1],I=w*E-T*L,z=w*L+T*E,D=b+S,j=x+A,V=b-S,N=x-A,O=F+I,B=v+z,G=s*(F-I),R=s*(v-z);if(e[a]=D+O,e[a+1]=j+B,e[d]=V+R,e[d+1]=N-G,0===r){e[u]=D-O,e[u+1]=j-B;continue}if(r===l)continue;const q=o+n-r,$=o+t-r;e[q]=V-s*R,e[q+1]=-N-s*G,e[$]=D-s*O,e[$+1]=s*B-j}}const d=r>>>1;for(let t=2;t<d;t+=2)e[r-t]=e[t],e[r-t+1]=-e[t+1]}_singleRealTransform2(e,t,s,r,o){const n=e[r],a=e[r+o];t[s]=n+a,t[s+1]=0,t[s+2]=n-a,t[s+3]=0}_singleRealTransform4(e,t,s,r,o,n){const a=2*o,i=3*o,l=e[r],c=e[r+o],d=e[r+a],u=e[r+i],_=l+d,m=l-d,p=c+u,h=n*(c-u);t[s]=_+p,t[s+1]=0,t[s+2]=m,t[s+3]=-h,t[s+4]=_-p,t[s+5]=0,t[s+6]=m,t[s+7]=h}}class p{constructor(e){const t=2*(e-1),s=2*(2*e-1),r=2**Math.ceil(Math.log2(s));this.bufferSize=r,this._a=t;const o=new Float64Array(s),n=new Float64Array(r);this._chirpBuffer=new Float64Array(r),this._buffer1=new Float64Array(r),this._buffer2=new Float64Array(r),this._outBuffer1=new Float64Array(r),this._outBuffer2=new Float64Array(r);const a=-2*Math.PI/e,i=Math.cos(a),l=Math.sin(a);for(let t=0;t<s>>1;++t){const s=(t+1-e)**2/2,r=Math.sqrt(i**2+l**2)**s,a=s*Math.atan2(l,i),c=2*t;o[c]=r*Math.cos(a),o[c+1]=r*Math.sin(a),n[c]=o[c],n[c+1]=-o[c+1]}this._slicedChirpBuffer=o.subarray(t,s),this._f=new m(r>>1),this._f.transform(this._chirpBuffer,n)}_transform(e,t,s){const r=this._buffer1,o=this._buffer2,n=this._outBuffer1,a=this._outBuffer2,i=this._chirpBuffer,l=this._slicedChirpBuffer,c=this._a;if(s)for(let e=0;e<l.length;e+=2){const s=e+1,o=t[e>>1];r[e]=o*l[e],r[s]=o*l[s]}else for(let e=0;e<l.length;e+=2){const s=e+1;r[e]=t[e]*l[e]-t[s]*l[s],r[s]=t[e]*l[s]+t[s]*l[e]}this._f.transform(n,r);for(let e=0;e<i.length;e+=2){const t=e+1;o[e]=n[e]*i[e]-n[t]*i[t],o[t]=n[e]*i[t]+n[t]*i[e]}this._f.inverseTransform(a,o);for(let t=0;t<a.length;t+=2){const s=a[t+c],r=a[t+c+1],o=l[t],n=l[t+1];e[t]=s*o-r*n,e[t+1]=s*n+r*o}}transform(e,t){this._transform(e,t,!1)}realTransform(e,t){this._transform(e,t,!0)}}class h{constructor(e){this.fft_length=e,this.isPowerOfTwo=_(e),this.isPowerOfTwo?(this.fft=new m(e),this.outputBufferSize=2*e):(this.fft=new p(e),this.outputBufferSize=this.fft.bufferSize)}realTransform(e,t){this.fft.realTransform(e,t)}transform(e,t){this.fft.transform(e,t)}}function g(e,t){if(t%2==0||t<=0)throw new Error("Window size must be a positive odd number");const s=new e.constructor(e.length),r=new e.constructor(t),o=Math.floor(t/2);for(let t=0;t<e.length;++t){let n=0;for(let s=-o;s<=o;++s){let o=t+s;o<0?o=Math.abs(o):o>=e.length&&(o=2*(e.length-1)-o),r[n++]=e[o]}r.sort(),s[t]=r[o]}return s}function f(e,t){const s=Math.pow(10,t);return Math.round(e*s)/s}function M(e){const t=Math.round(e);return Math.abs(e)%1==.5?t%2==0?t:t-1:t}function w(e){const t=e.length,s=e[0].length,r=[t+1,s+1],o=Array.from({length:r[0]},(()=>Array(r[1]).fill(1/0)));o[0][0]=0;const n=Array.from({length:r[0]},(()=>Array(r[1]).fill(-1)));for(let t=1;t<r[1];++t)for(let s=1;s<r[0];++s){const r=o[s-1][t-1],a=o[s-1][t],i=o[s][t-1];let l,c;r<a&&r<i?(l=r,c=0):a<r&&a<i?(l=a,c=1):(l=i,c=2),o[s][t]=e[s-1][t-1]+l,n[s][t]=c}for(let e=0;e<r[1];++e)n[0][e]=2;for(let e=0;e<r[0];++e)n[e][0]=1;let a=t,i=s,l=[],c=[];for(;a>0||i>0;)switch(l.push(a-1),c.push(i-1),n[a][i]){case 0:--a,--i;break;case 1:--a;break;case 2:--i;break;default:throw new Error(`Internal error in dynamic time warping. Unexpected trace[${a}, ${i}]. Please file a bug report.`)}return l.reverse(),c.reverse(),[l,c]}},"./src/utils/tensor.js":(e,t,s)=>{s.r(t),s.d(t,{DataTypeMap:()=>a,Tensor:()=>i,cat:()=>b,full:()=>y,full_like:()=>C,interpolate:()=>c,interpolate_4d:()=>d,layer_norm:()=>f,matmul:()=>u,mean:()=>F,mean_pooling:()=>g,ones:()=>S,ones_like:()=>A,permute:()=>l,quantize_embeddings:()=>z,rand:()=>I,rfft:()=>_,slice:()=>h,stack:()=>x,std_mean:()=>k,topk:()=>m,zeros:()=>E,zeros_like:()=>L});var r=s("./src/utils/maths.js"),o=s("./src/backends/onnx.js"),n=s("./src/ops/registry.js");const a=Object.freeze({float32:Float32Array,float16:"undefined"!=typeof Float16Array?Float16Array:Uint16Array,float64:Float64Array,string:Array,int8:Int8Array,uint8:Uint8Array,int16:Int16Array,uint16:Uint16Array,int32:Int32Array,uint32:Uint32Array,int64:BigInt64Array,uint64:BigUint64Array,bool:Uint8Array,uint4:Uint8Array,int4:Int8Array});class i{get dims(){return this.ort_tensor.dims}set dims(e){this.ort_tensor.dims=e}get type(){return this.ort_tensor.type}get data(){return this.ort_tensor.data}get size(){return this.ort_tensor.size}get location(){return this.ort_tensor.location}ort_tensor;constructor(...e){return(0,o.isONNXTensor)(e[0])?this.ort_tensor=e[0]:this.ort_tensor=new o.Tensor(e[0],e[1],e[2]),new Proxy(this,{get:(e,t)=>{if("string"==typeof t){let s=Number(t);if(Number.isInteger(s))return e._getitem(s)}return e[t]},set:(e,t,s)=>e[t]=s})}dispose(){this.ort_tensor.dispose()}*[Symbol.iterator](){const[e,...t]=this.dims;if(t.length>0){const s=t.reduce(((e,t)=>e*t));for(let r=0;r<e;++r)yield this._subarray(r,s,t)}else yield*this.data}_getitem(e){const[t,...s]=this.dims;if(e=T(e,t),s.length>0){const t=s.reduce(((e,t)=>e*t));return this._subarray(e,t,s)}return new i(this.type,[this.data[e]],s)}indexOf(e){const t=this.data;for(let s=0;s<t.length;++s)if(t[s]==e)return s;return-1}_subarray(e,t,s){const r=e*t,o=(e+1)*t,n="subarray"in this.data?this.data.subarray(r,o):this.data.slice(r,o);return new i(this.type,n,s)}item(){const e=this.data;if(1!==e.length)throw new Error(`a Tensor with ${e.length} elements cannot be converted to Scalar`);return e[0]}tolist(){return function(e,t){const s=e.length,r=t.reduce(((e,t)=>e*t));if(s!==r)throw Error(`cannot reshape array of size ${s} into shape (${t})`);let o=e;for(let e=t.length-1;e>=0;e--)o=o.reduce(((s,r)=>{let o=s[s.length-1];return o.length<t[e]?o.push(r):s.push([r]),s}),[[]]);return o[0]}(this.data,this.dims)}sigmoid(){return this.clone().sigmoid_()}sigmoid_(){const e=this.data;for(let t=0;t<e.length;++t)e[t]=1/(1+Math.exp(-e[t]));return this}map(e){return this.clone().map_(e)}map_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]=e(t[s],s,t);return this}mul(e){return this.clone().mul_(e)}mul_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]*=e;return this}div(e){return this.clone().div_(e)}div_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]/=e;return this}add(e){return this.clone().add_(e)}add_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]+=e;return this}sub(e){return this.clone().sub_(e)}sub_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]-=e;return this}clone(){return new i(this.type,this.data.slice(),this.dims.slice())}slice(...e){const t=[],s=[];for(let r=0;r<this.dims.length;++r){let o=e[r];if(null==o)s.push([0,this.dims[r]]),t.push(this.dims[r]);else if("number"==typeof o)o=T(o,this.dims[r],r),s.push([o,o+1]);else{if(!Array.isArray(o)||2!==o.length)throw new Error(`Invalid slice: ${o}`);{let[e,n]=o;if(e=null===e?0:T(e,this.dims[r],r,!1),n=null===n?this.dims[r]:T(n,this.dims[r],r,!1),e>n)throw new Error(`Invalid slice: ${o}`);const a=[Math.max(e,0),Math.min(n,this.dims[r])];s.push(a),t.push(a[1]-a[0])}}}const r=s.map((([e,t])=>t-e)),o=r.reduce(((e,t)=>e*t)),n=this.data,a=new n.constructor(o),l=this.stride();let c=!0;for(let e=1;e<r.length;++e)if(0!==s[e][0]||s[e][1]!==this.dims[e]){c=!1;break}if(c){const e=s[0][0]*l[0],t=s[0][1]*l[0];if(ArrayBuffer.isView(n))a.set(n.subarray(e,t));else{if(!Array.isArray(n))throw new Error("Unsupported data type for slicing");{const s=n.slice(e,t);for(let e=0;e<s.length;++e)a[e]=s[e]}}}else for(let e=0;e<o;++e){let t=0;for(let o=r.length-1,n=e;o>=0;--o){const e=r[o];t+=(n%e+s[o][0])*l[o],n=Math.floor(n/e)}a[e]=n[t]}return new i(this.type,a,t)}permute(...e){return l(this,e)}transpose(...e){return this.permute(...e)}sum(e=null,t=!1){return this.norm(1,e,t)}norm(e="fro",t=null,s=!1){if("fro"===e)e=2;else if("string"==typeof e)throw Error(`Unsupported norm: ${e}`);const r=this.data,o=(t,s)=>t+s**e;if(null===t){const t=r.reduce(o,0)**(1/e);return new i(this.type,[t],[])}const[n,a,l]=P(o,this,t,s);if(1!==e)for(let t=0;t<a.length;++t)a[t]=a[t]**(1/e);return new i(n,a,l)}normalize_(e=2,t=1){t=T(t,this.dims.length);const s=this.norm(e,t,!0),r=this.data,o=s.data;for(let e=0;e<r.length;++e){let s=0;for(let r=this.dims.length-1,o=e,n=1;r>=0;--r){const e=this.dims[r];if(r!==t){s+=o%e*n,n*=this.dims[r]}o=Math.floor(o/e)}r[e]/=o[s]}return this}normalize(e=2,t=1){return this.clone().normalize_(e,t)}stride(){return function(e){const t=new Array(e.length);for(let s=e.length-1,r=1;s>=0;--s)t[s]=r,r*=e[s];return t}(this.dims)}squeeze(e=null){return new i(this.type,this.data,M(this.dims,e))}squeeze_(e=null){return this.dims=M(this.dims,e),this}unsqueeze(e=null){return new i(this.type,this.data,w(this.dims,e))}unsqueeze_(e=null){return this.dims=w(this.dims,e),this}flatten_(e=0,t=-1){t=(t+this.dims.length)%this.dims.length;let s=this.dims.slice(0,e),r=this.dims.slice(e,t+1),o=this.dims.slice(t+1);return this.dims=[...s,r.reduce(((e,t)=>e*t),1),...o],this}flatten(e=0,t=-1){return this.clone().flatten_(e,t)}view(...e){let t=-1;for(let s=0;s<e.length;++s)if(-1===e[s]){if(-1!==t)throw new Error("Only one dimension can be inferred");t=s}const s=this.data;if(-1!==t){const r=e.reduce(((e,s,r)=>r!==t?e*s:e),1);e[t]=s.length/r}return new i(this.type,s,e)}neg_(){const e=this.data;for(let t=0;t<e.length;++t)e[t]=-e[t];return this}neg(){return this.clone().neg_()}gt(e){const t=new Uint8Array(this.data.length),s=this.data;for(let r=0;r<s.length;++r)t[r]=s[r]>e?1:0;return new i("bool",t,this.dims)}lt(e){const t=new Uint8Array(this.data.length),s=this.data;for(let r=0;r<s.length;++r)t[r]=s[r]<e?1:0;return new i("bool",t,this.dims)}clamp_(e,t){const s=this.data;for(let r=0;r<s.length;++r)s[r]=Math.min(Math.max(s[r],e),t);return this}clamp(e,t){return this.clone().clamp_(e,t)}round_(){const e=this.data;for(let t=0;t<e.length;++t)e[t]=Math.round(e[t]);return this}round(){return this.clone().round_()}mean(e=null,t=!1){return F(this,e,t)}min(e=null,t=!1){if(null===e){const e=(0,r.min)(this.data)[0];return new i(this.type,[e],[])}const[s,o,n]=P(((e,t)=>Math.min(e,t)),this,e,t,1/0);return new i(s,o,n)}max(e=null,t=!1){if(null===e){const e=(0,r.max)(this.data)[0];return new i(this.type,[e],[])}const[s,o,n]=P(((e,t)=>Math.max(e,t)),this,e,t,-1/0);return new i(s,o,n)}argmin(e=null,t=!1){if(null!==e)throw new Error("`dim !== null` not yet implemented.");const s=(0,r.min)(this.data)[1];return new i("int64",[BigInt(s)],[])}argmax(e=null,t=!1){if(null!==e)throw new Error("`dim !== null` not yet implemented.");const s=(0,r.max)(this.data)[1];return new i("int64",[BigInt(s)],[])}to(e){if(this.type===e)return this;if(!a.hasOwnProperty(e))throw new Error(`Unsupported type: ${e}`);let t;const s=["int64","uint64"].includes(this.type),r=["int64","uint64"].includes(e);return s&&!r?t=Number:!s&&r&&(t=BigInt),new i(e,a[e].from(this.data,t),this.dims)}}function l(e,t){const[s,o]=(0,r.permute_data)(e.data,e.dims,t);return new i(e.type,s,o)}function c(e,[t,s],o="bilinear",n=!1){const a=e.dims.at(-3)??1,l=e.dims.at(-2),c=e.dims.at(-1);let d=(0,r.interpolate_data)(e.data,[a,l,c],[t,s],o,n);return new i(e.type,d,[a,t,s])}async function d(e,{size:t=null,mode:s="bilinear"}={}){if(4!==e.dims.length)throw new Error("`interpolate_4d` currently only supports 4D input.");if(!t)throw new Error("`interpolate_4d` requires a `size` argument.");let r,o;if(2===t.length)r=[...e.dims.slice(0,2),...t];else if(3===t.length)r=[e.dims[0],...t];else{if(4!==t.length)throw new Error("`size` must be of length 2, 3, or 4.");r=t}if("nearest"===s)o=await n.TensorOpRegistry.nearest_interpolate_4d;else if("bilinear"===s)o=await n.TensorOpRegistry.bilinear_interpolate_4d;else{if("bicubic"!==s)throw new Error(`Unsupported mode: ${s}`);o=await n.TensorOpRegistry.bicubic_interpolate_4d}const a=new i("int64",new BigInt64Array(r.map(BigInt)),[r.length]);return await o({x:e,s:a})}async function u(e,t){const s=await n.TensorOpRegistry.matmul;return await s({a:e,b:t})}async function _(e,t){const s=await n.TensorOpRegistry.rfft;return await s({x:e,a:t})}async function m(e,t){const s=await n.TensorOpRegistry.top_k;return t=null==t?e.dims.at(-1):Math.min(t,e.dims.at(-1)),await s({x:e,k:new i("int64",[BigInt(t)],[1])})}const p=e=>new i("int64",e,[e.length]);async function h(e,t,s,r,o){const a=await n.TensorOpRegistry.slice;return await a({x:e,s:p(t),e:p(s),a:p(r),t:p(o??new Array(r.length).fill(1))})}function g(e,t){const s=e.data,r=t.data,o=[e.dims[0],e.dims[2]],n=new s.constructor(o[0]*o[1]),[a,l,c]=e.dims;let d=0;for(let e=0;e<a;++e){const t=e*c*l;for(let o=0;o<c;++o){let a=0,i=0;const u=e*l,_=t+o;for(let e=0;e<l;++e){const t=Number(r[u+e]);i+=t,a+=s[_+e*c]*t}const m=a/i;n[d++]=m}}return new i(e.type,n,o)}function f(e,t,{eps:s=1e-5}={}){if(2!==e.dims.length)throw new Error("`layer_norm` currently only supports 2D input.");const[r,o]=e.dims;if(1!==t.length&&t[0]!==o)throw new Error("`normalized_shape` must be a 1D array with shape `[input.dims[1]]`.");const[n,a]=k(e,1,0,!0),l=n.data,c=a.data,d=e.data,u=new d.constructor(d.length);for(let e=0;e<r;++e){const t=e*o;for(let r=0;r<o;++r){const o=t+r;u[o]=(d[o]-c[e])/(l[e]+s)}}return new i(e.type,u,e.dims)}function M(e,t){return e=e.slice(),null===t?e=e.filter((e=>1!==e)):"number"==typeof t?1===e[t]&&e.splice(t,1):Array.isArray(t)&&(e=e.filter(((e,s)=>1!==e||!t.includes(s)))),e}function w(e,t){return t=T(t,e.length+1),(e=e.slice()).splice(t,0,1),e}function T(e,t,s=null,r=!0){if(e<-t||e>=t){if(r)throw new Error(`IndexError: index ${e} is out of bounds for dimension${null===s?"":" "+s} with size ${t}`);return e<-t?0:t}return e<0&&(e=(e%t+t)%t),e}function b(e,t=0){t=T(t,e[0].dims.length);const s=e[0].dims.slice();s[t]=e.reduce(((e,s)=>e+s.dims[t]),0);const r=s.reduce(((e,t)=>e*t),1),o=new e[0].data.constructor(r),n=e[0].type;if(0===t){let t=0;for(const s of e){const e=s.data;o.set(e,t),t+=e.length}}else{let r=0;for(let n=0;n<e.length;++n){const{data:a,dims:i}=e[n];for(let e=0;e<a.length;++e){let n=0;for(let o=i.length-1,a=e,l=1;o>=0;--o){const e=i[o];let c=a%e;o===t&&(c+=r),n+=c*l,l*=s[o],a=Math.floor(a/e)}o[n]=a[e]}r+=i[t]}}return new i(n,o,s)}function x(e,t=0){return b(e.map((e=>e.unsqueeze(t))),t)}function P(e,t,s=null,r=!1,o=null){const n=t.data,a=t.dims;s=T(s,a.length);const i=a.slice();i[s]=1;const l=new n.constructor(n.length/a[s]);null!==o&&l.fill(o);for(let t=0;t<n.length;++t){let r=0;for(let e=a.length-1,o=t,n=1;e>=0;--e){const t=a[e];if(e!==s){r+=o%t*n,n*=i[e]}o=Math.floor(o/t)}l[r]=e(l[r],n[t],t,r)}return r||i.splice(s,1),[t.type,l,i]}function k(e,t=null,s=1,r=!1){const o=e.data,n=e.dims;if(null===t){const t=o.reduce(((e,t)=>e+t),0)/o.length,r=Math.sqrt(o.reduce(((e,s)=>e+(s-t)**2),0)/(o.length-s)),n=new i(e.type,[t],[]);return[new i(e.type,[r],[]),n]}const a=F(e,t=T(t,n.length),r),l=a.data,[c,d,u]=P(((e,t,s,r)=>e+(t-l[r])**2),e,t,r);for(let e=0;e<d.length;++e)d[e]=Math.sqrt(d[e]/(n[t]-s));return[new i(c,d,u),a]}function F(e,t=null,s=!1){const r=e.dims,o=e.data;if(null===t){const t=o.reduce(((e,t)=>e+t),0);return new i(e.type,[t/o.length],[])}t=T(t,r.length);const[n,a,l]=P(((e,t)=>e+t),e,t,s);if(1!==r[t])for(let e=0;e<a.length;++e)a[e]/=r[t];return new i(n,a,l)}function v(e,t,s,r){const o=e.reduce(((e,t)=>e*t),1);return new i(s,new r(o).fill(t),e)}function y(e,t){let s,r;if("number"==typeof t)s="float32",r=Float32Array;else if("bigint"==typeof t)s="int64",r=BigInt64Array;else{if("boolean"!=typeof t)throw new Error("Unsupported data type: "+typeof t);s="bool",r=Uint8Array}return v(e,t,s,r)}function C(e,t){return y(e.dims,t)}function S(e){return v(e,1n,"int64",BigInt64Array)}function A(e){return S(e.dims)}function E(e){return v(e,0n,"int64",BigInt64Array)}function L(e){return E(e.dims)}function I(e){const t=e.reduce(((e,t)=>e*t),1);return new i("float32",Float32Array.from({length:t},(()=>Math.random())),e)}function z(e,t){if(2!==e.dims.length)throw new Error("The tensor must have 2 dimensions");if(e.dims.at(-1)%8!=0)throw new Error("The last dimension of the tensor must be a multiple of 8");if(!["binary","ubinary"].includes(t))throw new Error("The precision must be either 'binary' or 'ubinary'");const s="binary"===t,r=s?"int8":"uint8",o=s?Int8Array:Uint8Array,n=e.data,a=new o(n.length/8);for(let e=0;e<n.length;++e){const t=n[e]>0?1:0,r=Math.floor(e/8),o=e%8;a[r]|=t<<7-o,s&&0===o&&(a[r]-=128)}return new i(r,a,[e.dims[0],e.dims[1]/8])}},"./src/utils/video.js":(e,t,s)=>{s.r(t),s.d(t,{RawVideo:()=>a,RawVideoFrame:()=>n,load_video:()=>i});var r=s("./src/utils/image.js"),o=s("./src/env.js");class n{constructor(e,t){this.image=e,this.timestamp=t}}class a{constructor(e,t){e.length>0&&e[0]instanceof r.RawImage&&(e=e.map(((s,r)=>new n(s,(r+1)/(e.length+1)*t)))),this.frames=e,this.duration=t}get width(){return this.frames[0].image.width}get height(){return this.frames[0].image.height}get fps(){return this.frames.length/this.duration}}async function i(e,{num_frames:t=null,fps:s=null}={}){if(!o.apis.IS_BROWSER_ENV)throw new Error("`load_video` is currently only supported in browser environments.");if(null==t&&null==s)throw new Error("Either num_frames or fps must be provided.");const i=[],l=document.createElement("video");if(l.crossOrigin="anonymous",l.muted=!0,"string"==typeof e)l.src=e;else if(e instanceof Blob)l.src=URL.createObjectURL(e);else{if(!(e instanceof HTMLVideoElement))throw new Error("Invalid URL or video element provided.");l.src=e.src}if(await new Promise((e=>l.onloadedmetadata=e)),l.seekable.start(0)===l.seekable.end(0)){const e=await fetch(l.src),t=await e.blob();l.src=URL.createObjectURL(t),await new Promise((e=>l.onloadedmetadata=e))}const c=l.duration;let d,u;null!=t?(d=t,u=1===t?0:c/(t-1)):(u=1/s,d=Math.floor(c/u));let _=[];for(let e=0;e<d;++e)_.push(1===t?c/2:e*u);const m=document.createElement("canvas");m.width=l.videoWidth,m.height=l.videoHeight;const p=m.getContext("2d",{willReadFrequently:!0});for(const e of _){l.currentTime=e,await new Promise((e=>{l.onseeked=e})),p.drawImage(l,0,0,m.width,m.height);const t=p.getImageData(0,0,m.width,m.height),s=new r.RawImage(t.data,m.width,m.height,4),o=new n(s,e);i.push(o)}return l.remove(),new a(i,c)}}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}};return o[e](s,s.exports,a),s.exports}r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var o=Object.create(null);a.r(o);var n={};s=s||[null,r({}),r([]),r(r)];for(var i=2&t&&e;"object"==typeof i&&!~s.indexOf(i);i=r(i))Object.getOwnPropertyNames(i).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,a.d(o,n),o},a.d=(e,t)=>{for(var s in t)a.o(t,s)&&!a.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{a.r(i),a.d(i,{ASTFeatureExtractor:()=>m.ASTFeatureExtractor,ASTForAudioClassification:()=>s.ASTForAudioClassification,ASTModel:()=>s.ASTModel,ASTPreTrainedModel:()=>s.ASTPreTrainedModel,AlbertForMaskedLM:()=>s.AlbertForMaskedLM,AlbertForQuestionAnswering:()=>s.AlbertForQuestionAnswering,AlbertForSequenceClassification:()=>s.AlbertForSequenceClassification,AlbertModel:()=>s.AlbertModel,AlbertPreTrainedModel:()=>s.AlbertPreTrainedModel,AlbertTokenizer:()=>r.AlbertTokenizer,ArceeForCausalLM:()=>s.ArceeForCausalLM,ArceeModel:()=>s.ArceeModel,ArceePreTrainedModel:()=>s.ArceePreTrainedModel,AudioClassificationPipeline:()=>t.AudioClassificationPipeline,AutoConfig:()=>o.AutoConfig,AutoFeatureExtractor:()=>p.AutoFeatureExtractor,AutoImageProcessor:()=>f.AutoImageProcessor,AutoModel:()=>s.AutoModel,AutoModelForAudioClassification:()=>s.AutoModelForAudioClassification,AutoModelForAudioFrameClassification:()=>s.AutoModelForAudioFrameClassification,AutoModelForAudioTextToText:()=>s.AutoModelForAudioTextToText,AutoModelForCTC:()=>s.AutoModelForCTC,AutoModelForCausalLM:()=>s.AutoModelForCausalLM,AutoModelForDepthEstimation:()=>s.AutoModelForDepthEstimation,AutoModelForDocumentQuestionAnswering:()=>s.AutoModelForDocumentQuestionAnswering,AutoModelForImageClassification:()=>s.AutoModelForImageClassification,AutoModelForImageFeatureExtraction:()=>s.AutoModelForImageFeatureExtraction,AutoModelForImageMatting:()=>s.AutoModelForImageMatting,AutoModelForImageSegmentation:()=>s.AutoModelForImageSegmentation,AutoModelForImageTextToText:()=>s.AutoModelForImageTextToText,AutoModelForImageToImage:()=>s.AutoModelForImageToImage,AutoModelForMaskGeneration:()=>s.AutoModelForMaskGeneration,AutoModelForMaskedLM:()=>s.AutoModelForMaskedLM,AutoModelForNormalEstimation:()=>s.AutoModelForNormalEstimation,AutoModelForObjectDetection:()=>s.AutoModelForObjectDetection,AutoModelForPoseEstimation:()=>s.AutoModelForPoseEstimation,AutoModelForQuestionAnswering:()=>s.AutoModelForQuestionAnswering,AutoModelForSemanticSegmentation:()=>s.AutoModelForSemanticSegmentation,AutoModelForSeq2SeqLM:()=>s.AutoModelForSeq2SeqLM,AutoModelForSequenceClassification:()=>s.AutoModelForSequenceClassification,AutoModelForSpeechSeq2Seq:()=>s.AutoModelForSpeechSeq2Seq,AutoModelForTextToSpectrogram:()=>s.AutoModelForTextToSpectrogram,AutoModelForTextToWaveform:()=>s.AutoModelForTextToWaveform,AutoModelForTokenClassification:()=>s.AutoModelForTokenClassification,AutoModelForUniversalSegmentation:()=>s.AutoModelForUniversalSegmentation,AutoModelForVision2Seq:()=>s.AutoModelForVision2Seq,AutoModelForXVector:()=>s.AutoModelForXVector,AutoModelForZeroShotObjectDetection:()=>s.AutoModelForZeroShotObjectDetection,AutoProcessor:()=>T.AutoProcessor,AutoTokenizer:()=>r.AutoTokenizer,AutomaticSpeechRecognitionPipeline:()=>t.AutomaticSpeechRecognitionPipeline,BackgroundRemovalPipeline:()=>t.BackgroundRemovalPipeline,BartForConditionalGeneration:()=>s.BartForConditionalGeneration,BartForSequenceClassification:()=>s.BartForSequenceClassification,BartModel:()=>s.BartModel,BartPretrainedModel:()=>s.BartPretrainedModel,BartTokenizer:()=>r.BartTokenizer,BaseModelOutput:()=>s.BaseModelOutput,BaseStreamer:()=>b.BaseStreamer,BeitFeatureExtractor:()=>g.BeitFeatureExtractor,BeitForImageClassification:()=>s.BeitForImageClassification,BeitModel:()=>s.BeitModel,BeitPreTrainedModel:()=>s.BeitPreTrainedModel,BertForMaskedLM:()=>s.BertForMaskedLM,BertForQuestionAnswering:()=>s.BertForQuestionAnswering,BertForSequenceClassification:()=>s.BertForSequenceClassification,BertForTokenClassification:()=>s.BertForTokenClassification,BertModel:()=>s.BertModel,BertPreTrainedModel:()=>s.BertPreTrainedModel,BertTokenizer:()=>r.BertTokenizer,BitImageProcessor:()=>g.BitImageProcessor,BlenderbotForConditionalGeneration:()=>s.BlenderbotForConditionalGeneration,BlenderbotModel:()=>s.BlenderbotModel,BlenderbotPreTrainedModel:()=>s.BlenderbotPreTrainedModel,BlenderbotSmallForConditionalGeneration:()=>s.BlenderbotSmallForConditionalGeneration,BlenderbotSmallModel:()=>s.BlenderbotSmallModel,BlenderbotSmallPreTrainedModel:()=>s.BlenderbotSmallPreTrainedModel,BlenderbotSmallTokenizer:()=>r.BlenderbotSmallTokenizer,BlenderbotTokenizer:()=>r.BlenderbotTokenizer,BloomForCausalLM:()=>s.BloomForCausalLM,BloomModel:()=>s.BloomModel,BloomPreTrainedModel:()=>s.BloomPreTrainedModel,BloomTokenizer:()=>r.BloomTokenizer,CLIPFeatureExtractor:()=>g.CLIPFeatureExtractor,CLIPImageProcessor:()=>g.CLIPImageProcessor,CLIPModel:()=>s.CLIPModel,CLIPPreTrainedModel:()=>s.CLIPPreTrainedModel,CLIPSegForImageSegmentation:()=>s.CLIPSegForImageSegmentation,CLIPSegModel:()=>s.CLIPSegModel,CLIPSegPreTrainedModel:()=>s.CLIPSegPreTrainedModel,CLIPTextModel:()=>s.CLIPTextModel,CLIPTextModelWithProjection:()=>s.CLIPTextModelWithProjection,CLIPTokenizer:()=>r.CLIPTokenizer,CLIPVisionModel:()=>s.CLIPVisionModel,CLIPVisionModelWithProjection:()=>s.CLIPVisionModelWithProjection,CamembertForMaskedLM:()=>s.CamembertForMaskedLM,CamembertForQuestionAnswering:()=>s.CamembertForQuestionAnswering,CamembertForSequenceClassification:()=>s.CamembertForSequenceClassification,CamembertForTokenClassification:()=>s.CamembertForTokenClassification,CamembertModel:()=>s.CamembertModel,CamembertPreTrainedModel:()=>s.CamembertPreTrainedModel,CamembertTokenizer:()=>r.CamembertTokenizer,CausalLMOutput:()=>s.CausalLMOutput,CausalLMOutputWithPast:()=>s.CausalLMOutputWithPast,ChineseCLIPFeatureExtractor:()=>g.ChineseCLIPFeatureExtractor,ChineseCLIPModel:()=>s.ChineseCLIPModel,ChineseCLIPPreTrainedModel:()=>s.ChineseCLIPPreTrainedModel,ClapAudioModelWithProjection:()=>s.ClapAudioModelWithProjection,ClapFeatureExtractor:()=>m.ClapFeatureExtractor,ClapModel:()=>s.ClapModel,ClapPreTrainedModel:()=>s.ClapPreTrainedModel,ClapTextModelWithProjection:()=>s.ClapTextModelWithProjection,ClassifierFreeGuidanceLogitsProcessor:()=>P.ClassifierFreeGuidanceLogitsProcessor,CodeGenForCausalLM:()=>s.CodeGenForCausalLM,CodeGenModel:()=>s.CodeGenModel,CodeGenPreTrainedModel:()=>s.CodeGenPreTrainedModel,CodeGenTokenizer:()=>r.CodeGenTokenizer,CodeLlamaTokenizer:()=>r.CodeLlamaTokenizer,CohereForCausalLM:()=>s.CohereForCausalLM,CohereModel:()=>s.CohereModel,CoherePreTrainedModel:()=>s.CoherePreTrainedModel,CohereTokenizer:()=>r.CohereTokenizer,ConvBertForMaskedLM:()=>s.ConvBertForMaskedLM,ConvBertForQuestionAnswering:()=>s.ConvBertForQuestionAnswering,ConvBertForSequenceClassification:()=>s.ConvBertForSequenceClassification,ConvBertForTokenClassification:()=>s.ConvBertForTokenClassification,ConvBertModel:()=>s.ConvBertModel,ConvBertPreTrainedModel:()=>s.ConvBertPreTrainedModel,ConvBertTokenizer:()=>r.ConvBertTokenizer,ConvNextFeatureExtractor:()=>g.ConvNextFeatureExtractor,ConvNextForImageClassification:()=>s.ConvNextForImageClassification,ConvNextImageProcessor:()=>g.ConvNextImageProcessor,ConvNextModel:()=>s.ConvNextModel,ConvNextPreTrainedModel:()=>s.ConvNextPreTrainedModel,ConvNextV2ForImageClassification:()=>s.ConvNextV2ForImageClassification,ConvNextV2Model:()=>s.ConvNextV2Model,ConvNextV2PreTrainedModel:()=>s.ConvNextV2PreTrainedModel,DFineForObjectDetection:()=>s.DFineForObjectDetection,DFineModel:()=>s.DFineModel,DFinePreTrainedModel:()=>s.DFinePreTrainedModel,DINOv3ConvNextModel:()=>s.DINOv3ConvNextModel,DINOv3ConvNextPreTrainedModel:()=>s.DINOv3ConvNextPreTrainedModel,DINOv3ViTImageProcessor:()=>g.DINOv3ViTImageProcessor,DINOv3ViTModel:()=>s.DINOv3ViTModel,DINOv3ViTPreTrainedModel:()=>s.DINOv3ViTPreTrainedModel,DPTFeatureExtractor:()=>g.DPTFeatureExtractor,DPTForDepthEstimation:()=>s.DPTForDepthEstimation,DPTImageProcessor:()=>g.DPTImageProcessor,DPTModel:()=>s.DPTModel,DPTPreTrainedModel:()=>s.DPTPreTrainedModel,DacDecoderModel:()=>s.DacDecoderModel,DacDecoderOutput:()=>s.DacDecoderOutput,DacEncoderModel:()=>s.DacEncoderModel,DacEncoderOutput:()=>s.DacEncoderOutput,DacFeatureExtractor:()=>m.DacFeatureExtractor,DacModel:()=>s.DacModel,DacPreTrainedModel:()=>s.DacPreTrainedModel,DataTypeMap:()=>d.DataTypeMap,DebertaForMaskedLM:()=>s.DebertaForMaskedLM,DebertaForQuestionAnswering:()=>s.DebertaForQuestionAnswering,DebertaForSequenceClassification:()=>s.DebertaForSequenceClassification,DebertaForTokenClassification:()=>s.DebertaForTokenClassification,DebertaModel:()=>s.DebertaModel,DebertaPreTrainedModel:()=>s.DebertaPreTrainedModel,DebertaTokenizer:()=>r.DebertaTokenizer,DebertaV2ForMaskedLM:()=>s.DebertaV2ForMaskedLM,DebertaV2ForQuestionAnswering:()=>s.DebertaV2ForQuestionAnswering,DebertaV2ForSequenceClassification:()=>s.DebertaV2ForSequenceClassification,DebertaV2ForTokenClassification:()=>s.DebertaV2ForTokenClassification,DebertaV2Model:()=>s.DebertaV2Model,DebertaV2PreTrainedModel:()=>s.DebertaV2PreTrainedModel,DebertaV2Tokenizer:()=>r.DebertaV2Tokenizer,DecisionTransformerModel:()=>s.DecisionTransformerModel,DecisionTransformerPreTrainedModel:()=>s.DecisionTransformerPreTrainedModel,DeiTFeatureExtractor:()=>g.DeiTFeatureExtractor,DeiTForImageClassification:()=>s.DeiTForImageClassification,DeiTImageProcessor:()=>g.DeiTImageProcessor,DeiTModel:()=>s.DeiTModel,DeiTPreTrainedModel:()=>s.DeiTPreTrainedModel,DepthAnythingForDepthEstimation:()=>s.DepthAnythingForDepthEstimation,DepthAnythingPreTrainedModel:()=>s.DepthAnythingPreTrainedModel,DepthEstimationPipeline:()=>t.DepthEstimationPipeline,DepthProForDepthEstimation:()=>s.DepthProForDepthEstimation,DepthProPreTrainedModel:()=>s.DepthProPreTrainedModel,DetrFeatureExtractor:()=>g.DetrFeatureExtractor,DetrForObjectDetection:()=>s.DetrForObjectDetection,DetrForSegmentation:()=>s.DetrForSegmentation,DetrImageProcessor:()=>g.DetrImageProcessor,DetrModel:()=>s.DetrModel,DetrObjectDetectionOutput:()=>s.DetrObjectDetectionOutput,DetrPreTrainedModel:()=>s.DetrPreTrainedModel,DetrSegmentationOutput:()=>s.DetrSegmentationOutput,Dinov2ForImageClassification:()=>s.Dinov2ForImageClassification,Dinov2Model:()=>s.Dinov2Model,Dinov2PreTrainedModel:()=>s.Dinov2PreTrainedModel,Dinov2WithRegistersForImageClassification:()=>s.Dinov2WithRegistersForImageClassification,Dinov2WithRegistersModel:()=>s.Dinov2WithRegistersModel,Dinov2WithRegistersPreTrainedModel:()=>s.Dinov2WithRegistersPreTrainedModel,DistilBertForMaskedLM:()=>s.DistilBertForMaskedLM,DistilBertForQuestionAnswering:()=>s.DistilBertForQuestionAnswering,DistilBertForSequenceClassification:()=>s.DistilBertForSequenceClassification,DistilBertForTokenClassification:()=>s.DistilBertForTokenClassification,DistilBertModel:()=>s.DistilBertModel,DistilBertPreTrainedModel:()=>s.DistilBertPreTrainedModel,DistilBertTokenizer:()=>r.DistilBertTokenizer,DocumentQuestionAnsweringPipeline:()=>t.DocumentQuestionAnsweringPipeline,DonutFeatureExtractor:()=>g.DonutFeatureExtractor,DonutImageProcessor:()=>g.DonutImageProcessor,DonutSwinModel:()=>s.DonutSwinModel,DonutSwinPreTrainedModel:()=>s.DonutSwinPreTrainedModel,EfficientNetForImageClassification:()=>s.EfficientNetForImageClassification,EfficientNetImageProcessor:()=>g.EfficientNetImageProcessor,EfficientNetModel:()=>s.EfficientNetModel,EfficientNetPreTrainedModel:()=>s.EfficientNetPreTrainedModel,ElectraForMaskedLM:()=>s.ElectraForMaskedLM,ElectraForQuestionAnswering:()=>s.ElectraForQuestionAnswering,ElectraForSequenceClassification:()=>s.ElectraForSequenceClassification,ElectraForTokenClassification:()=>s.ElectraForTokenClassification,ElectraModel:()=>s.ElectraModel,ElectraPreTrainedModel:()=>s.ElectraPreTrainedModel,ElectraTokenizer:()=>r.ElectraTokenizer,EncodecFeatureExtractor:()=>m.EncodecFeatureExtractor,EosTokenCriteria:()=>x.EosTokenCriteria,Ernie4_5_ForCausalLM:()=>s.Ernie4_5_ForCausalLM,Ernie4_5_Model:()=>s.Ernie4_5_Model,Ernie4_5_PretrainedModel:()=>s.Ernie4_5_PretrainedModel,Ernie4_5_Tokenizer:()=>r.Ernie4_5_Tokenizer,EsmForMaskedLM:()=>s.EsmForMaskedLM,EsmForSequenceClassification:()=>s.EsmForSequenceClassification,EsmForTokenClassification:()=>s.EsmForTokenClassification,EsmModel:()=>s.EsmModel,EsmPreTrainedModel:()=>s.EsmPreTrainedModel,EsmTokenizer:()=>r.EsmTokenizer,ExaoneForCausalLM:()=>s.ExaoneForCausalLM,ExaoneModel:()=>s.ExaoneModel,ExaonePreTrainedModel:()=>s.ExaonePreTrainedModel,FFT:()=>u.FFT,FalconForCausalLM:()=>s.FalconForCausalLM,FalconModel:()=>s.FalconModel,FalconPreTrainedModel:()=>s.FalconPreTrainedModel,FalconTokenizer:()=>r.FalconTokenizer,FastViTForImageClassification:()=>s.FastViTForImageClassification,FastViTModel:()=>s.FastViTModel,FastViTPreTrainedModel:()=>s.FastViTPreTrainedModel,FeatureExtractionPipeline:()=>t.FeatureExtractionPipeline,FeatureExtractor:()=>_.FeatureExtractor,FillMaskPipeline:()=>t.FillMaskPipeline,Florence2ForConditionalGeneration:()=>s.Florence2ForConditionalGeneration,Florence2PreTrainedModel:()=>s.Florence2PreTrainedModel,Florence2Processor:()=>w.Florence2Processor,ForcedBOSTokenLogitsProcessor:()=>P.ForcedBOSTokenLogitsProcessor,ForcedEOSTokenLogitsProcessor:()=>P.ForcedEOSTokenLogitsProcessor,GLPNFeatureExtractor:()=>g.GLPNFeatureExtractor,GLPNForDepthEstimation:()=>s.GLPNForDepthEstimation,GLPNModel:()=>s.GLPNModel,GLPNPreTrainedModel:()=>s.GLPNPreTrainedModel,GPT2LMHeadModel:()=>s.GPT2LMHeadModel,GPT2Model:()=>s.GPT2Model,GPT2PreTrainedModel:()=>s.GPT2PreTrainedModel,GPT2Tokenizer:()=>r.GPT2Tokenizer,GPTBigCodeForCausalLM:()=>s.GPTBigCodeForCausalLM,GPTBigCodeModel:()=>s.GPTBigCodeModel,GPTBigCodePreTrainedModel:()=>s.GPTBigCodePreTrainedModel,GPTJForCausalLM:()=>s.GPTJForCausalLM,GPTJModel:()=>s.GPTJModel,GPTJPreTrainedModel:()=>s.GPTJPreTrainedModel,GPTNeoForCausalLM:()=>s.GPTNeoForCausalLM,GPTNeoModel:()=>s.GPTNeoModel,GPTNeoPreTrainedModel:()=>s.GPTNeoPreTrainedModel,GPTNeoXForCausalLM:()=>s.GPTNeoXForCausalLM,GPTNeoXModel:()=>s.GPTNeoXModel,GPTNeoXPreTrainedModel:()=>s.GPTNeoXPreTrainedModel,GPTNeoXTokenizer:()=>r.GPTNeoXTokenizer,Gemma2ForCausalLM:()=>s.Gemma2ForCausalLM,Gemma2Model:()=>s.Gemma2Model,Gemma2PreTrainedModel:()=>s.Gemma2PreTrainedModel,Gemma3ForCausalLM:()=>s.Gemma3ForCausalLM,Gemma3Model:()=>s.Gemma3Model,Gemma3PreTrainedModel:()=>s.Gemma3PreTrainedModel,Gemma3nAudioFeatureExtractor:()=>m.Gemma3nAudioFeatureExtractor,Gemma3nForConditionalGeneration:()=>s.Gemma3nForConditionalGeneration,Gemma3nPreTrainedModel:()=>s.Gemma3nPreTrainedModel,Gemma3nProcessor:()=>w.Gemma3nProcessor,GemmaForCausalLM:()=>s.GemmaForCausalLM,GemmaModel:()=>s.GemmaModel,GemmaPreTrainedModel:()=>s.GemmaPreTrainedModel,GemmaTokenizer:()=>r.GemmaTokenizer,GlmForCausalLM:()=>s.GlmForCausalLM,GlmModel:()=>s.GlmModel,GlmPreTrainedModel:()=>s.GlmPreTrainedModel,GraniteForCausalLM:()=>s.GraniteForCausalLM,GraniteModel:()=>s.GraniteModel,GranitePreTrainedModel:()=>s.GranitePreTrainedModel,Grok1Tokenizer:()=>r.Grok1Tokenizer,GroundingDinoForObjectDetection:()=>s.GroundingDinoForObjectDetection,GroundingDinoImageProcessor:()=>g.GroundingDinoImageProcessor,GroundingDinoPreTrainedModel:()=>s.GroundingDinoPreTrainedModel,GroundingDinoProcessor:()=>w.GroundingDinoProcessor,GroupViTModel:()=>s.GroupViTModel,GroupViTPreTrainedModel:()=>s.GroupViTPreTrainedModel,HeliumForCausalLM:()=>s.HeliumForCausalLM,HeliumModel:()=>s.HeliumModel,HeliumPreTrainedModel:()=>s.HeliumPreTrainedModel,HerbertTokenizer:()=>r.HerbertTokenizer,HieraForImageClassification:()=>s.HieraForImageClassification,HieraModel:()=>s.HieraModel,HieraPreTrainedModel:()=>s.HieraPreTrainedModel,HubertForCTC:()=>s.HubertForCTC,HubertForSequenceClassification:()=>s.HubertForSequenceClassification,HubertModel:()=>s.HubertModel,HubertPreTrainedModel:()=>s.HubertPreTrainedModel,IJepaForImageClassification:()=>s.IJepaForImageClassification,IJepaModel:()=>s.IJepaModel,IJepaPreTrainedModel:()=>s.IJepaPreTrainedModel,Idefics3ForConditionalGeneration:()=>s.Idefics3ForConditionalGeneration,Idefics3ImageProcessor:()=>g.Idefics3ImageProcessor,Idefics3PreTrainedModel:()=>s.Idefics3PreTrainedModel,Idefics3Processor:()=>w.Idefics3Processor,ImageClassificationPipeline:()=>t.ImageClassificationPipeline,ImageFeatureExtractionPipeline:()=>t.ImageFeatureExtractionPipeline,ImageFeatureExtractor:()=>m.ImageFeatureExtractor,ImageMattingOutput:()=>s.ImageMattingOutput,ImageProcessor:()=>h.ImageProcessor,ImageSegmentationPipeline:()=>t.ImageSegmentationPipeline,ImageToImagePipeline:()=>t.ImageToImagePipeline,ImageToTextPipeline:()=>t.ImageToTextPipeline,InterruptableStoppingCriteria:()=>x.InterruptableStoppingCriteria,JAISLMHeadModel:()=>s.JAISLMHeadModel,JAISModel:()=>s.JAISModel,JAISPreTrainedModel:()=>s.JAISPreTrainedModel,JinaCLIPImageProcessor:()=>g.JinaCLIPImageProcessor,JinaCLIPModel:()=>s.JinaCLIPModel,JinaCLIPPreTrainedModel:()=>s.JinaCLIPPreTrainedModel,JinaCLIPProcessor:()=>w.JinaCLIPProcessor,JinaCLIPTextModel:()=>s.JinaCLIPTextModel,JinaCLIPVisionModel:()=>s.JinaCLIPVisionModel,Lfm2ForCausalLM:()=>s.Lfm2ForCausalLM,Lfm2Model:()=>s.Lfm2Model,Lfm2PreTrainedModel:()=>s.Lfm2PreTrainedModel,LiteWhisperForConditionalGeneration:()=>s.LiteWhisperForConditionalGeneration,LlamaForCausalLM:()=>s.LlamaForCausalLM,LlamaModel:()=>s.LlamaModel,LlamaPreTrainedModel:()=>s.LlamaPreTrainedModel,LlamaTokenizer:()=>r.LlamaTokenizer,LlavaForConditionalGeneration:()=>s.LlavaForConditionalGeneration,LlavaOnevisionForConditionalGeneration:()=>s.LlavaOnevisionForConditionalGeneration,LlavaOnevisionImageProcessor:()=>g.LlavaOnevisionImageProcessor,LlavaPreTrainedModel:()=>s.LlavaPreTrainedModel,LlavaProcessor:()=>w.LlavaProcessor,LlavaQwen2ForCausalLM:()=>s.LlavaQwen2ForCausalLM,LogitsProcessor:()=>P.LogitsProcessor,LogitsProcessorList:()=>P.LogitsProcessorList,LogitsWarper:()=>P.LogitsWarper,LongT5ForConditionalGeneration:()=>s.LongT5ForConditionalGeneration,LongT5Model:()=>s.LongT5Model,LongT5PreTrainedModel:()=>s.LongT5PreTrainedModel,M2M100ForConditionalGeneration:()=>s.M2M100ForConditionalGeneration,M2M100Model:()=>s.M2M100Model,M2M100PreTrainedModel:()=>s.M2M100PreTrainedModel,M2M100Tokenizer:()=>r.M2M100Tokenizer,MBart50Tokenizer:()=>r.MBart50Tokenizer,MBartForCausalLM:()=>s.MBartForCausalLM,MBartForConditionalGeneration:()=>s.MBartForConditionalGeneration,MBartForSequenceClassification:()=>s.MBartForSequenceClassification,MBartModel:()=>s.MBartModel,MBartPreTrainedModel:()=>s.MBartPreTrainedModel,MBartTokenizer:()=>r.MBartTokenizer,MPNetForMaskedLM:()=>s.MPNetForMaskedLM,MPNetForQuestionAnswering:()=>s.MPNetForQuestionAnswering,MPNetForSequenceClassification:()=>s.MPNetForSequenceClassification,MPNetForTokenClassification:()=>s.MPNetForTokenClassification,MPNetModel:()=>s.MPNetModel,MPNetPreTrainedModel:()=>s.MPNetPreTrainedModel,MPNetTokenizer:()=>r.MPNetTokenizer,MT5ForConditionalGeneration:()=>s.MT5ForConditionalGeneration,MT5Model:()=>s.MT5Model,MT5PreTrainedModel:()=>s.MT5PreTrainedModel,MarianMTModel:()=>s.MarianMTModel,MarianModel:()=>s.MarianModel,MarianPreTrainedModel:()=>s.MarianPreTrainedModel,MarianTokenizer:()=>r.MarianTokenizer,Mask2FormerImageProcessor:()=>g.Mask2FormerImageProcessor,MaskFormerFeatureExtractor:()=>g.MaskFormerFeatureExtractor,MaskFormerForInstanceSegmentation:()=>s.MaskFormerForInstanceSegmentation,MaskFormerImageProcessor:()=>g.MaskFormerImageProcessor,MaskFormerModel:()=>s.MaskFormerModel,MaskFormerPreTrainedModel:()=>s.MaskFormerPreTrainedModel,MaskedLMOutput:()=>s.MaskedLMOutput,MaxLengthCriteria:()=>x.MaxLengthCriteria,Metric3DForDepthEstimation:()=>s.Metric3DForDepthEstimation,Metric3DPreTrainedModel:()=>s.Metric3DPreTrainedModel,Metric3Dv2ForDepthEstimation:()=>s.Metric3Dv2ForDepthEstimation,Metric3Dv2PreTrainedModel:()=>s.Metric3Dv2PreTrainedModel,MgpstrForSceneTextRecognition:()=>s.MgpstrForSceneTextRecognition,MgpstrModelOutput:()=>s.MgpstrModelOutput,MgpstrPreTrainedModel:()=>s.MgpstrPreTrainedModel,MgpstrProcessor:()=>w.MgpstrProcessor,MgpstrTokenizer:()=>r.MgpstrTokenizer,MimiDecoderModel:()=>s.MimiDecoderModel,MimiDecoderOutput:()=>s.MimiDecoderOutput,MimiEncoderModel:()=>s.MimiEncoderModel,MimiEncoderOutput:()=>s.MimiEncoderOutput,MimiModel:()=>s.MimiModel,MimiPreTrainedModel:()=>s.MimiPreTrainedModel,MinLengthLogitsProcessor:()=>P.MinLengthLogitsProcessor,MinNewTokensLengthLogitsProcessor:()=>P.MinNewTokensLengthLogitsProcessor,MistralForCausalLM:()=>s.MistralForCausalLM,MistralModel:()=>s.MistralModel,MistralPreTrainedModel:()=>s.MistralPreTrainedModel,MobileBertForMaskedLM:()=>s.MobileBertForMaskedLM,MobileBertForQuestionAnswering:()=>s.MobileBertForQuestionAnswering,MobileBertForSequenceClassification:()=>s.MobileBertForSequenceClassification,MobileBertModel:()=>s.MobileBertModel,MobileBertPreTrainedModel:()=>s.MobileBertPreTrainedModel,MobileBertTokenizer:()=>r.MobileBertTokenizer,MobileLLMForCausalLM:()=>s.MobileLLMForCausalLM,MobileLLMModel:()=>s.MobileLLMModel,MobileLLMPreTrainedModel:()=>s.MobileLLMPreTrainedModel,MobileNetV1FeatureExtractor:()=>g.MobileNetV1FeatureExtractor,MobileNetV1ForImageClassification:()=>s.MobileNetV1ForImageClassification,MobileNetV1ForSemanticSegmentation:()=>s.MobileNetV1ForSemanticSegmentation,MobileNetV1ImageProcessor:()=>g.MobileNetV1ImageProcessor,MobileNetV1Model:()=>s.MobileNetV1Model,MobileNetV1PreTrainedModel:()=>s.MobileNetV1PreTrainedModel,MobileNetV2FeatureExtractor:()=>g.MobileNetV2FeatureExtractor,MobileNetV2ForImageClassification:()=>s.MobileNetV2ForImageClassification,MobileNetV2ForSemanticSegmentation:()=>s.MobileNetV2ForSemanticSegmentation,MobileNetV2ImageProcessor:()=>g.MobileNetV2ImageProcessor,MobileNetV2Model:()=>s.MobileNetV2Model,MobileNetV2PreTrainedModel:()=>s.MobileNetV2PreTrainedModel,MobileNetV3FeatureExtractor:()=>g.MobileNetV3FeatureExtractor,MobileNetV3ForImageClassification:()=>s.MobileNetV3ForImageClassification,MobileNetV3ForSemanticSegmentation:()=>s.MobileNetV3ForSemanticSegmentation,MobileNetV3ImageProcessor:()=>g.MobileNetV3ImageProcessor,MobileNetV3Model:()=>s.MobileNetV3Model,MobileNetV3PreTrainedModel:()=>s.MobileNetV3PreTrainedModel,MobileNetV4FeatureExtractor:()=>g.MobileNetV4FeatureExtractor,MobileNetV4ForImageClassification:()=>s.MobileNetV4ForImageClassification,MobileNetV4ForSemanticSegmentation:()=>s.MobileNetV4ForSemanticSegmentation,MobileNetV4ImageProcessor:()=>g.MobileNetV4ImageProcessor,MobileNetV4Model:()=>s.MobileNetV4Model,MobileNetV4PreTrainedModel:()=>s.MobileNetV4PreTrainedModel,MobileViTFeatureExtractor:()=>g.MobileViTFeatureExtractor,MobileViTForImageClassification:()=>s.MobileViTForImageClassification,MobileViTImageProcessor:()=>g.MobileViTImageProcessor,MobileViTModel:()=>s.MobileViTModel,MobileViTPreTrainedModel:()=>s.MobileViTPreTrainedModel,MobileViTV2ForImageClassification:()=>s.MobileViTV2ForImageClassification,MobileViTV2Model:()=>s.MobileViTV2Model,MobileViTV2PreTrainedModel:()=>s.MobileViTV2PreTrainedModel,ModelOutput:()=>s.ModelOutput,ModernBertDecoderForCausalLM:()=>s.ModernBertDecoderForCausalLM,ModernBertDecoderModel:()=>s.ModernBertDecoderModel,ModernBertDecoderPreTrainedModel:()=>s.ModernBertDecoderPreTrainedModel,ModernBertForMaskedLM:()=>s.ModernBertForMaskedLM,ModernBertForSequenceClassification:()=>s.ModernBertForSequenceClassification,ModernBertForTokenClassification:()=>s.ModernBertForTokenClassification,ModernBertModel:()=>s.ModernBertModel,ModernBertPreTrainedModel:()=>s.ModernBertPreTrainedModel,Moondream1ForConditionalGeneration:()=>s.Moondream1ForConditionalGeneration,MoonshineFeatureExtractor:()=>m.MoonshineFeatureExtractor,MoonshineForConditionalGeneration:()=>s.MoonshineForConditionalGeneration,MoonshineModel:()=>s.MoonshineModel,MoonshinePreTrainedModel:()=>s.MoonshinePreTrainedModel,MoonshineProcessor:()=>w.MoonshineProcessor,MptForCausalLM:()=>s.MptForCausalLM,MptModel:()=>s.MptModel,MptPreTrainedModel:()=>s.MptPreTrainedModel,MultiModalityCausalLM:()=>s.MultiModalityCausalLM,MultiModalityPreTrainedModel:()=>s.MultiModalityPreTrainedModel,MusicgenForCausalLM:()=>s.MusicgenForCausalLM,MusicgenForConditionalGeneration:()=>s.MusicgenForConditionalGeneration,MusicgenModel:()=>s.MusicgenModel,MusicgenPreTrainedModel:()=>s.MusicgenPreTrainedModel,NeoBertForMaskedLM:()=>s.NeoBertForMaskedLM,NeoBertForQuestionAnswering:()=>s.NeoBertForQuestionAnswering,NeoBertForSequenceClassification:()=>s.NeoBertForSequenceClassification,NeoBertForTokenClassification:()=>s.NeoBertForTokenClassification,NeoBertModel:()=>s.NeoBertModel,NeoBertPreTrainedModel:()=>s.NeoBertPreTrainedModel,NllbTokenizer:()=>r.NllbTokenizer,NoBadWordsLogitsProcessor:()=>P.NoBadWordsLogitsProcessor,NoRepeatNGramLogitsProcessor:()=>P.NoRepeatNGramLogitsProcessor,NomicBertModel:()=>s.NomicBertModel,NomicBertPreTrainedModel:()=>s.NomicBertPreTrainedModel,NougatImageProcessor:()=>g.NougatImageProcessor,NougatTokenizer:()=>r.NougatTokenizer,OPTForCausalLM:()=>s.OPTForCausalLM,OPTModel:()=>s.OPTModel,OPTPreTrainedModel:()=>s.OPTPreTrainedModel,ObjectDetectionPipeline:()=>t.ObjectDetectionPipeline,Olmo2ForCausalLM:()=>s.Olmo2ForCausalLM,Olmo2Model:()=>s.Olmo2Model,Olmo2PreTrainedModel:()=>s.Olmo2PreTrainedModel,OlmoForCausalLM:()=>s.OlmoForCausalLM,OlmoModel:()=>s.OlmoModel,OlmoPreTrainedModel:()=>s.OlmoPreTrainedModel,OpenELMForCausalLM:()=>s.OpenELMForCausalLM,OpenELMModel:()=>s.OpenELMModel,OpenELMPreTrainedModel:()=>s.OpenELMPreTrainedModel,OwlViTFeatureExtractor:()=>g.OwlViTFeatureExtractor,OwlViTForObjectDetection:()=>s.OwlViTForObjectDetection,OwlViTImageProcessor:()=>g.OwlViTImageProcessor,OwlViTModel:()=>s.OwlViTModel,OwlViTPreTrainedModel:()=>s.OwlViTPreTrainedModel,OwlViTProcessor:()=>w.OwlViTProcessor,Owlv2ForObjectDetection:()=>s.Owlv2ForObjectDetection,Owlv2ImageProcessor:()=>g.Owlv2ImageProcessor,Owlv2Model:()=>s.Owlv2Model,Owlv2PreTrainedModel:()=>s.Owlv2PreTrainedModel,PaliGemmaForConditionalGeneration:()=>s.PaliGemmaForConditionalGeneration,PaliGemmaPreTrainedModel:()=>s.PaliGemmaPreTrainedModel,PaliGemmaProcessor:()=>w.PaliGemmaProcessor,PatchTSMixerForPrediction:()=>s.PatchTSMixerForPrediction,PatchTSMixerModel:()=>s.PatchTSMixerModel,PatchTSMixerPreTrainedModel:()=>s.PatchTSMixerPreTrainedModel,PatchTSTForPrediction:()=>s.PatchTSTForPrediction,PatchTSTModel:()=>s.PatchTSTModel,PatchTSTPreTrainedModel:()=>s.PatchTSTPreTrainedModel,Phi3ForCausalLM:()=>s.Phi3ForCausalLM,Phi3Model:()=>s.Phi3Model,Phi3PreTrainedModel:()=>s.Phi3PreTrainedModel,Phi3VForCausalLM:()=>s.Phi3VForCausalLM,Phi3VImageProcessor:()=>g.Phi3VImageProcessor,Phi3VPreTrainedModel:()=>s.Phi3VPreTrainedModel,Phi3VProcessor:()=>w.Phi3VProcessor,PhiForCausalLM:()=>s.PhiForCausalLM,PhiModel:()=>s.PhiModel,PhiPreTrainedModel:()=>s.PhiPreTrainedModel,Pipeline:()=>t.Pipeline,PreTrainedModel:()=>s.PreTrainedModel,PreTrainedTokenizer:()=>r.PreTrainedTokenizer,PretrainedConfig:()=>o.PretrainedConfig,PretrainedMixin:()=>s.PretrainedMixin,Processor:()=>M.Processor,PvtForImageClassification:()=>s.PvtForImageClassification,PvtImageProcessor:()=>g.PvtImageProcessor,PvtModel:()=>s.PvtModel,PvtPreTrainedModel:()=>s.PvtPreTrainedModel,PyAnnoteFeatureExtractor:()=>m.PyAnnoteFeatureExtractor,PyAnnoteForAudioFrameClassification:()=>s.PyAnnoteForAudioFrameClassification,PyAnnoteModel:()=>s.PyAnnoteModel,PyAnnotePreTrainedModel:()=>s.PyAnnotePreTrainedModel,PyAnnoteProcessor:()=>w.PyAnnoteProcessor,QuestionAnsweringModelOutput:()=>s.QuestionAnsweringModelOutput,QuestionAnsweringPipeline:()=>t.QuestionAnsweringPipeline,Qwen2ForCausalLM:()=>s.Qwen2ForCausalLM,Qwen2Model:()=>s.Qwen2Model,Qwen2PreTrainedModel:()=>s.Qwen2PreTrainedModel,Qwen2Tokenizer:()=>r.Qwen2Tokenizer,Qwen2VLForConditionalGeneration:()=>s.Qwen2VLForConditionalGeneration,Qwen2VLImageProcessor:()=>g.Qwen2VLImageProcessor,Qwen2VLPreTrainedModel:()=>s.Qwen2VLPreTrainedModel,Qwen2VLProcessor:()=>w.Qwen2VLProcessor,Qwen3ForCausalLM:()=>s.Qwen3ForCausalLM,Qwen3Model:()=>s.Qwen3Model,Qwen3PreTrainedModel:()=>s.Qwen3PreTrainedModel,RFDetrForObjectDetection:()=>s.RFDetrForObjectDetection,RFDetrModel:()=>s.RFDetrModel,RFDetrObjectDetectionOutput:()=>s.RFDetrObjectDetectionOutput,RFDetrPreTrainedModel:()=>s.RFDetrPreTrainedModel,RTDetrForObjectDetection:()=>s.RTDetrForObjectDetection,RTDetrImageProcessor:()=>g.RTDetrImageProcessor,RTDetrModel:()=>s.RTDetrModel,RTDetrObjectDetectionOutput:()=>s.RTDetrObjectDetectionOutput,RTDetrPreTrainedModel:()=>s.RTDetrPreTrainedModel,RTDetrV2ForObjectDetection:()=>s.RTDetrV2ForObjectDetection,RTDetrV2Model:()=>s.RTDetrV2Model,RTDetrV2ObjectDetectionOutput:()=>s.RTDetrV2ObjectDetectionOutput,RTDetrV2PreTrainedModel:()=>s.RTDetrV2PreTrainedModel,RawAudio:()=>n.RawAudio,RawImage:()=>l.RawImage,RawVideo:()=>c.RawVideo,RawVideoFrame:()=>c.RawVideoFrame,RepetitionPenaltyLogitsProcessor:()=>P.RepetitionPenaltyLogitsProcessor,ResNetForImageClassification:()=>s.ResNetForImageClassification,ResNetModel:()=>s.ResNetModel,ResNetPreTrainedModel:()=>s.ResNetPreTrainedModel,RoFormerForMaskedLM:()=>s.RoFormerForMaskedLM,RoFormerForQuestionAnswering:()=>s.RoFormerForQuestionAnswering,RoFormerForSequenceClassification:()=>s.RoFormerForSequenceClassification,RoFormerForTokenClassification:()=>s.RoFormerForTokenClassification,RoFormerModel:()=>s.RoFormerModel,RoFormerPreTrainedModel:()=>s.RoFormerPreTrainedModel,RoFormerTokenizer:()=>r.RoFormerTokenizer,RobertaForMaskedLM:()=>s.RobertaForMaskedLM,RobertaForQuestionAnswering:()=>s.RobertaForQuestionAnswering,RobertaForSequenceClassification:()=>s.RobertaForSequenceClassification,RobertaForTokenClassification:()=>s.RobertaForTokenClassification,RobertaModel:()=>s.RobertaModel,RobertaPreTrainedModel:()=>s.RobertaPreTrainedModel,RobertaTokenizer:()=>r.RobertaTokenizer,SamImageProcessor:()=>g.SamImageProcessor,SamImageSegmentationOutput:()=>s.SamImageSegmentationOutput,SamModel:()=>s.SamModel,SamPreTrainedModel:()=>s.SamPreTrainedModel,SamProcessor:()=>w.SamProcessor,SapiensForDepthEstimation:()=>s.SapiensForDepthEstimation,SapiensForNormalEstimation:()=>s.SapiensForNormalEstimation,SapiensForSemanticSegmentation:()=>s.SapiensForSemanticSegmentation,SapiensPreTrainedModel:()=>s.SapiensPreTrainedModel,SeamlessM4TFeatureExtractor:()=>m.SeamlessM4TFeatureExtractor,SegformerFeatureExtractor:()=>g.SegformerFeatureExtractor,SegformerForImageClassification:()=>s.SegformerForImageClassification,SegformerForSemanticSegmentation:()=>s.SegformerForSemanticSegmentation,SegformerImageProcessor:()=>g.SegformerImageProcessor,SegformerModel:()=>s.SegformerModel,SegformerPreTrainedModel:()=>s.SegformerPreTrainedModel,Seq2SeqLMOutput:()=>s.Seq2SeqLMOutput,SequenceClassifierOutput:()=>s.SequenceClassifierOutput,SiglipImageProcessor:()=>g.SiglipImageProcessor,SiglipModel:()=>s.SiglipModel,SiglipPreTrainedModel:()=>s.SiglipPreTrainedModel,SiglipTextModel:()=>s.SiglipTextModel,SiglipTokenizer:()=>r.SiglipTokenizer,SiglipVisionModel:()=>s.SiglipVisionModel,SmolLM3ForCausalLM:()=>s.SmolLM3ForCausalLM,SmolLM3Model:()=>s.SmolLM3Model,SmolLM3PreTrainedModel:()=>s.SmolLM3PreTrainedModel,SmolVLMForConditionalGeneration:()=>s.SmolVLMForConditionalGeneration,SmolVLMImageProcessor:()=>g.SmolVLMImageProcessor,SmolVLMProcessor:()=>w.SmolVLMProcessor,SnacDecoderModel:()=>s.SnacDecoderModel,SnacEncoderModel:()=>s.SnacEncoderModel,SnacFeatureExtractor:()=>m.SnacFeatureExtractor,SnacModel:()=>s.SnacModel,SnacPreTrainedModel:()=>s.SnacPreTrainedModel,SpeechT5FeatureExtractor:()=>m.SpeechT5FeatureExtractor,SpeechT5ForSpeechToText:()=>s.SpeechT5ForSpeechToText,SpeechT5ForTextToSpeech:()=>s.SpeechT5ForTextToSpeech,SpeechT5HifiGan:()=>s.SpeechT5HifiGan,SpeechT5Model:()=>s.SpeechT5Model,SpeechT5PreTrainedModel:()=>s.SpeechT5PreTrainedModel,SpeechT5Processor:()=>w.SpeechT5Processor,SpeechT5Tokenizer:()=>r.SpeechT5Tokenizer,SqueezeBertForMaskedLM:()=>s.SqueezeBertForMaskedLM,SqueezeBertForQuestionAnswering:()=>s.SqueezeBertForQuestionAnswering,SqueezeBertForSequenceClassification:()=>s.SqueezeBertForSequenceClassification,SqueezeBertModel:()=>s.SqueezeBertModel,SqueezeBertPreTrainedModel:()=>s.SqueezeBertPreTrainedModel,SqueezeBertTokenizer:()=>r.SqueezeBertTokenizer,StableLmForCausalLM:()=>s.StableLmForCausalLM,StableLmModel:()=>s.StableLmModel,StableLmPreTrainedModel:()=>s.StableLmPreTrainedModel,Starcoder2ForCausalLM:()=>s.Starcoder2ForCausalLM,Starcoder2Model:()=>s.Starcoder2Model,Starcoder2PreTrainedModel:()=>s.Starcoder2PreTrainedModel,StoppingCriteria:()=>x.StoppingCriteria,StoppingCriteriaList:()=>x.StoppingCriteriaList,StyleTextToSpeech2Model:()=>s.StyleTextToSpeech2Model,StyleTextToSpeech2PreTrainedModel:()=>s.StyleTextToSpeech2PreTrainedModel,SummarizationPipeline:()=>t.SummarizationPipeline,SuppressTokensAtBeginLogitsProcessor:()=>P.SuppressTokensAtBeginLogitsProcessor,Swin2SRForImageSuperResolution:()=>s.Swin2SRForImageSuperResolution,Swin2SRImageProcessor:()=>g.Swin2SRImageProcessor,Swin2SRModel:()=>s.Swin2SRModel,Swin2SRPreTrainedModel:()=>s.Swin2SRPreTrainedModel,SwinForImageClassification:()=>s.SwinForImageClassification,SwinForSemanticSegmentation:()=>s.SwinForSemanticSegmentation,SwinModel:()=>s.SwinModel,SwinPreTrainedModel:()=>s.SwinPreTrainedModel,T5ForConditionalGeneration:()=>s.T5ForConditionalGeneration,T5Model:()=>s.T5Model,T5PreTrainedModel:()=>s.T5PreTrainedModel,T5Tokenizer:()=>r.T5Tokenizer,TableTransformerForObjectDetection:()=>s.TableTransformerForObjectDetection,TableTransformerModel:()=>s.TableTransformerModel,TableTransformerObjectDetectionOutput:()=>s.TableTransformerObjectDetectionOutput,TableTransformerPreTrainedModel:()=>s.TableTransformerPreTrainedModel,TemperatureLogitsWarper:()=>P.TemperatureLogitsWarper,Tensor:()=>d.Tensor,Text2TextGenerationPipeline:()=>t.Text2TextGenerationPipeline,TextClassificationPipeline:()=>t.TextClassificationPipeline,TextGenerationPipeline:()=>t.TextGenerationPipeline,TextStreamer:()=>b.TextStreamer,TextToAudioPipeline:()=>t.TextToAudioPipeline,TokenClassificationPipeline:()=>t.TokenClassificationPipeline,TokenClassifierOutput:()=>s.TokenClassifierOutput,TokenizerModel:()=>r.TokenizerModel,TopKLogitsWarper:()=>P.TopKLogitsWarper,TopPLogitsWarper:()=>P.TopPLogitsWarper,TrOCRForCausalLM:()=>s.TrOCRForCausalLM,TrOCRPreTrainedModel:()=>s.TrOCRPreTrainedModel,TranslationPipeline:()=>t.TranslationPipeline,UltravoxModel:()=>s.UltravoxModel,UltravoxPreTrainedModel:()=>s.UltravoxPreTrainedModel,UltravoxProcessor:()=>w.UltravoxProcessor,UniSpeechForCTC:()=>s.UniSpeechForCTC,UniSpeechForSequenceClassification:()=>s.UniSpeechForSequenceClassification,UniSpeechModel:()=>s.UniSpeechModel,UniSpeechPreTrainedModel:()=>s.UniSpeechPreTrainedModel,UniSpeechSatForAudioFrameClassification:()=>s.UniSpeechSatForAudioFrameClassification,UniSpeechSatForCTC:()=>s.UniSpeechSatForCTC,UniSpeechSatForSequenceClassification:()=>s.UniSpeechSatForSequenceClassification,UniSpeechSatModel:()=>s.UniSpeechSatModel,UniSpeechSatPreTrainedModel:()=>s.UniSpeechSatPreTrainedModel,VLChatProcessor:()=>w.VLChatProcessor,VLMImageProcessor:()=>g.VLMImageProcessor,ViTFeatureExtractor:()=>g.ViTFeatureExtractor,ViTForImageClassification:()=>s.ViTForImageClassification,ViTImageProcessor:()=>g.ViTImageProcessor,ViTMAEModel:()=>s.ViTMAEModel,ViTMAEPreTrainedModel:()=>s.ViTMAEPreTrainedModel,ViTMSNForImageClassification:()=>s.ViTMSNForImageClassification,ViTMSNModel:()=>s.ViTMSNModel,ViTMSNPreTrainedModel:()=>s.ViTMSNPreTrainedModel,ViTModel:()=>s.ViTModel,ViTPreTrainedModel:()=>s.ViTPreTrainedModel,VisionEncoderDecoderModel:()=>s.VisionEncoderDecoderModel,VitMatteForImageMatting:()=>s.VitMatteForImageMatting,VitMatteImageProcessor:()=>g.VitMatteImageProcessor,VitMattePreTrainedModel:()=>s.VitMattePreTrainedModel,VitPoseForPoseEstimation:()=>s.VitPoseForPoseEstimation,VitPoseImageProcessor:()=>g.VitPoseImageProcessor,VitPosePreTrainedModel:()=>s.VitPosePreTrainedModel,VitsModel:()=>s.VitsModel,VitsModelOutput:()=>s.VitsModelOutput,VitsPreTrainedModel:()=>s.VitsPreTrainedModel,VitsTokenizer:()=>r.VitsTokenizer,VoxtralForConditionalGeneration:()=>s.VoxtralForConditionalGeneration,VoxtralProcessor:()=>w.VoxtralProcessor,Wav2Vec2BertForCTC:()=>s.Wav2Vec2BertForCTC,Wav2Vec2BertForSequenceClassification:()=>s.Wav2Vec2BertForSequenceClassification,Wav2Vec2BertModel:()=>s.Wav2Vec2BertModel,Wav2Vec2BertPreTrainedModel:()=>s.Wav2Vec2BertPreTrainedModel,Wav2Vec2CTCTokenizer:()=>r.Wav2Vec2CTCTokenizer,Wav2Vec2FeatureExtractor:()=>m.Wav2Vec2FeatureExtractor,Wav2Vec2ForAudioFrameClassification:()=>s.Wav2Vec2ForAudioFrameClassification,Wav2Vec2ForCTC:()=>s.Wav2Vec2ForCTC,Wav2Vec2ForSequenceClassification:()=>s.Wav2Vec2ForSequenceClassification,Wav2Vec2Model:()=>s.Wav2Vec2Model,Wav2Vec2PreTrainedModel:()=>s.Wav2Vec2PreTrainedModel,Wav2Vec2Processor:()=>w.Wav2Vec2Processor,Wav2Vec2ProcessorWithLM:()=>w.Wav2Vec2ProcessorWithLM,WavLMForAudioFrameClassification:()=>s.WavLMForAudioFrameClassification,WavLMForCTC:()=>s.WavLMForCTC,WavLMForSequenceClassification:()=>s.WavLMForSequenceClassification,WavLMForXVector:()=>s.WavLMForXVector,WavLMModel:()=>s.WavLMModel,WavLMPreTrainedModel:()=>s.WavLMPreTrainedModel,WeSpeakerFeatureExtractor:()=>m.WeSpeakerFeatureExtractor,WeSpeakerResNetModel:()=>s.WeSpeakerResNetModel,WeSpeakerResNetPreTrainedModel:()=>s.WeSpeakerResNetPreTrainedModel,WhisperFeatureExtractor:()=>m.WhisperFeatureExtractor,WhisperForConditionalGeneration:()=>s.WhisperForConditionalGeneration,WhisperModel:()=>s.WhisperModel,WhisperPreTrainedModel:()=>s.WhisperPreTrainedModel,WhisperProcessor:()=>w.WhisperProcessor,WhisperTextStreamer:()=>b.WhisperTextStreamer,WhisperTimeStampLogitsProcessor:()=>P.WhisperTimeStampLogitsProcessor,WhisperTokenizer:()=>r.WhisperTokenizer,XLMForQuestionAnswering:()=>s.XLMForQuestionAnswering,XLMForSequenceClassification:()=>s.XLMForSequenceClassification,XLMForTokenClassification:()=>s.XLMForTokenClassification,XLMModel:()=>s.XLMModel,XLMPreTrainedModel:()=>s.XLMPreTrainedModel,XLMRobertaForMaskedLM:()=>s.XLMRobertaForMaskedLM,XLMRobertaForQuestionAnswering:()=>s.XLMRobertaForQuestionAnswering,XLMRobertaForSequenceClassification:()=>s.XLMRobertaForSequenceClassification,XLMRobertaForTokenClassification:()=>s.XLMRobertaForTokenClassification,XLMRobertaModel:()=>s.XLMRobertaModel,XLMRobertaPreTrainedModel:()=>s.XLMRobertaPreTrainedModel,XLMRobertaTokenizer:()=>r.XLMRobertaTokenizer,XLMTokenizer:()=>r.XLMTokenizer,XLMWithLMHeadModel:()=>s.XLMWithLMHeadModel,XVectorOutput:()=>s.XVectorOutput,YolosFeatureExtractor:()=>g.YolosFeatureExtractor,YolosForObjectDetection:()=>s.YolosForObjectDetection,YolosImageProcessor:()=>g.YolosImageProcessor,YolosModel:()=>s.YolosModel,YolosObjectDetectionOutput:()=>s.YolosObjectDetectionOutput,YolosPreTrainedModel:()=>s.YolosPreTrainedModel,ZeroShotAudioClassificationPipeline:()=>t.ZeroShotAudioClassificationPipeline,ZeroShotClassificationPipeline:()=>t.ZeroShotClassificationPipeline,ZeroShotImageClassificationPipeline:()=>t.ZeroShotImageClassificationPipeline,ZeroShotObjectDetectionPipeline:()=>t.ZeroShotObjectDetectionPipeline,bankers_round:()=>u.bankers_round,cat:()=>d.cat,cos_sim:()=>u.cos_sim,dot:()=>u.dot,dynamic_time_warping:()=>u.dynamic_time_warping,env:()=>e.env,full:()=>d.full,full_like:()=>d.full_like,getCacheShapes:()=>o.getCacheShapes,hamming:()=>n.hamming,hanning:()=>n.hanning,interpolate:()=>d.interpolate,interpolate_4d:()=>d.interpolate_4d,interpolate_data:()=>u.interpolate_data,is_chinese_char:()=>r.is_chinese_char,layer_norm:()=>d.layer_norm,load_image:()=>l.load_image,load_video:()=>c.load_video,log_softmax:()=>u.log_softmax,magnitude:()=>u.magnitude,matmul:()=>d.matmul,max:()=>u.max,mean:()=>d.mean,mean_pooling:()=>d.mean_pooling,medianFilter:()=>u.medianFilter,mel_filter_bank:()=>n.mel_filter_bank,min:()=>u.min,ones:()=>d.ones,ones_like:()=>d.ones_like,permute:()=>d.permute,permute_data:()=>u.permute_data,pipeline:()=>t.pipeline,quantize_embeddings:()=>d.quantize_embeddings,rand:()=>d.rand,read_audio:()=>n.read_audio,rfft:()=>d.rfft,round:()=>u.round,slice:()=>d.slice,softmax:()=>u.softmax,spectrogram:()=>n.spectrogram,stack:()=>d.stack,std_mean:()=>d.std_mean,topk:()=>d.topk,window_function:()=>n.window_function,zeros:()=>d.zeros,zeros_like:()=>d.zeros_like});var e=a("./src/env.js"),t=a("./src/pipelines.js"),s=a("./src/models.js"),r=a("./src/tokenizers.js"),o=a("./src/configs.js"),n=a("./src/utils/audio.js"),l=a("./src/utils/image.js"),c=a("./src/utils/video.js"),d=a("./src/utils/tensor.js"),u=a("./src/utils/maths.js"),_=a("./src/base/feature_extraction_utils.js"),m=a("./src/models/feature_extractors.js"),p=a("./src/models/auto/feature_extraction_auto.js"),h=a("./src/base/image_processors_utils.js"),g=a("./src/models/image_processors.js"),f=a("./src/models/auto/image_processing_auto.js"),M=a("./src/base/processing_utils.js"),w=a("./src/models/processors.js"),T=a("./src/models/auto/processing_auto.js"),b=a("./src/generation/streamers.js"),x=a("./src/generation/stopping_criteria.js"),P=a("./src/generation/logits_process.js")})();var l=i.ASTFeatureExtractor,c=i.ASTForAudioClassification,d=i.ASTModel,u=i.ASTPreTrainedModel,_=i.AlbertForMaskedLM,m=i.AlbertForQuestionAnswering,p=i.AlbertForSequenceClassification,h=i.AlbertModel,g=i.AlbertPreTrainedModel,f=i.AlbertTokenizer,M=i.ArceeForCausalLM,w=i.ArceeModel,T=i.ArceePreTrainedModel,b=i.AudioClassificationPipeline,x=i.AutoConfig,P=i.AutoFeatureExtractor,k=i.AutoImageProcessor,F=i.AutoModel,v=i.AutoModelForAudioClassification,y=i.AutoModelForAudioFrameClassification,C=i.AutoModelForAudioTextToText,S=i.AutoModelForCTC,A=i.AutoModelForCausalLM,E=i.AutoModelForDepthEstimation,L=i.AutoModelForDocumentQuestionAnswering,I=i.AutoModelForImageClassification,z=i.AutoModelForImageFeatureExtraction,D=i.AutoModelForImageMatting,j=i.AutoModelForImageSegmentation,V=i.AutoModelForImageTextToText,N=i.AutoModelForImageToImage,O=i.AutoModelForMaskGeneration,B=i.AutoModelForMaskedLM,G=i.AutoModelForNormalEstimation,R=i.AutoModelForObjectDetection,q=i.AutoModelForPoseEstimation,$=i.AutoModelForQuestionAnswering,W=i.AutoModelForSemanticSegmentation,U=i.AutoModelForSeq2SeqLM,Q=i.AutoModelForSequenceClassification,X=i.AutoModelForSpeechSeq2Seq,H=i.AutoModelForTextToSpectrogram,J=i.AutoModelForTextToWaveform,Y=i.AutoModelForTokenClassification,K=i.AutoModelForUniversalSegmentation,Z=i.AutoModelForVision2Seq,ee=i.AutoModelForXVector,te=i.AutoModelForZeroShotObjectDetection,se=i.AutoProcessor,re=i.AutoTokenizer,oe=i.AutomaticSpeechRecognitionPipeline,ne=i.BackgroundRemovalPipeline,ae=i.BartForConditionalGeneration,ie=i.BartForSequenceClassification,le=i.BartModel,ce=i.BartPretrainedModel,de=i.BartTokenizer,ue=i.BaseModelOutput,_e=i.BaseStreamer,me=i.BeitFeatureExtractor,pe=i.BeitForImageClassification,he=i.BeitModel,ge=i.BeitPreTrainedModel,fe=i.BertForMaskedLM,Me=i.BertForQuestionAnswering,we=i.BertForSequenceClassification,Te=i.BertForTokenClassification,be=i.BertModel,xe=i.BertPreTrainedModel,Pe=i.BertTokenizer,ke=i.BitImageProcessor,Fe=i.BlenderbotForConditionalGeneration,ve=i.BlenderbotModel,ye=i.BlenderbotPreTrainedModel,Ce=i.BlenderbotSmallForConditionalGeneration,Se=i.BlenderbotSmallModel,Ae=i.BlenderbotSmallPreTrainedModel,Ee=i.BlenderbotSmallTokenizer,Le=i.BlenderbotTokenizer,Ie=i.BloomForCausalLM,ze=i.BloomModel,De=i.BloomPreTrainedModel,je=i.BloomTokenizer,Ve=i.CLIPFeatureExtractor,Ne=i.CLIPImageProcessor,Oe=i.CLIPModel,Be=i.CLIPPreTrainedModel,Ge=i.CLIPSegForImageSegmentation,Re=i.CLIPSegModel,qe=i.CLIPSegPreTrainedModel,$e=i.CLIPTextModel,We=i.CLIPTextModelWithProjection,Ue=i.CLIPTokenizer,Qe=i.CLIPVisionModel,Xe=i.CLIPVisionModelWithProjection,He=i.CamembertForMaskedLM,Je=i.CamembertForQuestionAnswering,Ye=i.CamembertForSequenceClassification,Ke=i.CamembertForTokenClassification,Ze=i.CamembertModel,et=i.CamembertPreTrainedModel,tt=i.CamembertTokenizer,st=i.CausalLMOutput,rt=i.CausalLMOutputWithPast,ot=i.ChineseCLIPFeatureExtractor,nt=i.ChineseCLIPModel,at=i.ChineseCLIPPreTrainedModel,it=i.ClapAudioModelWithProjection,lt=i.ClapFeatureExtractor,ct=i.ClapModel,dt=i.ClapPreTrainedModel,ut=i.ClapTextModelWithProjection,_t=i.ClassifierFreeGuidanceLogitsProcessor,mt=i.CodeGenForCausalLM,pt=i.CodeGenModel,ht=i.CodeGenPreTrainedModel,gt=i.CodeGenTokenizer,ft=i.CodeLlamaTokenizer,Mt=i.CohereForCausalLM,wt=i.CohereModel,Tt=i.CoherePreTrainedModel,bt=i.CohereTokenizer,xt=i.ConvBertForMaskedLM,Pt=i.ConvBertForQuestionAnswering,kt=i.ConvBertForSequenceClassification,Ft=i.ConvBertForTokenClassification,vt=i.ConvBertModel,yt=i.ConvBertPreTrainedModel,Ct=i.ConvBertTokenizer,St=i.ConvNextFeatureExtractor,At=i.ConvNextForImageClassification,Et=i.ConvNextImageProcessor,Lt=i.ConvNextModel,It=i.ConvNextPreTrainedModel,zt=i.ConvNextV2ForImageClassification,Dt=i.ConvNextV2Model,jt=i.ConvNextV2PreTrainedModel,Vt=i.DFineForObjectDetection,Nt=i.DFineModel,Ot=i.DFinePreTrainedModel,Bt=i.DINOv3ConvNextModel,Gt=i.DINOv3ConvNextPreTrainedModel,Rt=i.DINOv3ViTImageProcessor,qt=i.DINOv3ViTModel,$t=i.DINOv3ViTPreTrainedModel,Wt=i.DPTFeatureExtractor,Ut=i.DPTForDepthEstimation,Qt=i.DPTImageProcessor,Xt=i.DPTModel,Ht=i.DPTPreTrainedModel,Jt=i.DacDecoderModel,Yt=i.DacDecoderOutput,Kt=i.DacEncoderModel,Zt=i.DacEncoderOutput,es=i.DacFeatureExtractor,ts=i.DacModel,ss=i.DacPreTrainedModel,rs=i.DataTypeMap,os=i.DebertaForMaskedLM,ns=i.DebertaForQuestionAnswering,as=i.DebertaForSequenceClassification,is=i.DebertaForTokenClassification,ls=i.DebertaModel,cs=i.DebertaPreTrainedModel,ds=i.DebertaTokenizer,us=i.DebertaV2ForMaskedLM,_s=i.DebertaV2ForQuestionAnswering,ms=i.DebertaV2ForSequenceClassification,ps=i.DebertaV2ForTokenClassification,hs=i.DebertaV2Model,gs=i.DebertaV2PreTrainedModel,fs=i.DebertaV2Tokenizer,Ms=i.DecisionTransformerModel,ws=i.DecisionTransformerPreTrainedModel,Ts=i.DeiTFeatureExtractor,bs=i.DeiTForImageClassification,xs=i.DeiTImageProcessor,Ps=i.DeiTModel,ks=i.DeiTPreTrainedModel,Fs=i.DepthAnythingForDepthEstimation,vs=i.DepthAnythingPreTrainedModel,ys=i.DepthEstimationPipeline,Cs=i.DepthProForDepthEstimation,Ss=i.DepthProPreTrainedModel,As=i.DetrFeatureExtractor,Es=i.DetrForObjectDetection,Ls=i.DetrForSegmentation,Is=i.DetrImageProcessor,zs=i.DetrModel,Ds=i.DetrObjectDetectionOutput,js=i.DetrPreTrainedModel,Vs=i.DetrSegmentationOutput,Ns=i.Dinov2ForImageClassification,Os=i.Dinov2Model,Bs=i.Dinov2PreTrainedModel,Gs=i.Dinov2WithRegistersForImageClassification,Rs=i.Dinov2WithRegistersModel,qs=i.Dinov2WithRegistersPreTrainedModel,$s=i.DistilBertForMaskedLM,Ws=i.DistilBertForQuestionAnswering,Us=i.DistilBertForSequenceClassification,Qs=i.DistilBertForTokenClassification,Xs=i.DistilBertModel,Hs=i.DistilBertPreTrainedModel,Js=i.DistilBertTokenizer,Ys=i.DocumentQuestionAnsweringPipeline,Ks=i.DonutFeatureExtractor,Zs=i.DonutImageProcessor,er=i.DonutSwinModel,tr=i.DonutSwinPreTrainedModel,sr=i.EfficientNetForImageClassification,rr=i.EfficientNetImageProcessor,or=i.EfficientNetModel,nr=i.EfficientNetPreTrainedModel,ar=i.ElectraForMaskedLM,ir=i.ElectraForQuestionAnswering,lr=i.ElectraForSequenceClassification,cr=i.ElectraForTokenClassification,dr=i.ElectraModel,ur=i.ElectraPreTrainedModel,_r=i.ElectraTokenizer,mr=i.EncodecFeatureExtractor,pr=i.EosTokenCriteria,hr=i.Ernie4_5_ForCausalLM,gr=i.Ernie4_5_Model,fr=i.Ernie4_5_PretrainedModel,Mr=i.Ernie4_5_Tokenizer,wr=i.EsmForMaskedLM,Tr=i.EsmForSequenceClassification,br=i.EsmForTokenClassification,xr=i.EsmModel,Pr=i.EsmPreTrainedModel,kr=i.EsmTokenizer,Fr=i.ExaoneForCausalLM,vr=i.ExaoneModel,yr=i.ExaonePreTrainedModel,Cr=i.FFT,Sr=i.FalconForCausalLM,Ar=i.FalconModel,Er=i.FalconPreTrainedModel,Lr=i.FalconTokenizer,Ir=i.FastViTForImageClassification,zr=i.FastViTModel,Dr=i.FastViTPreTrainedModel,jr=i.FeatureExtractionPipeline,Vr=i.FeatureExtractor,Nr=i.FillMaskPipeline,Or=i.Florence2ForConditionalGeneration,Br=i.Florence2PreTrainedModel,Gr=i.Florence2Processor,Rr=i.ForcedBOSTokenLogitsProcessor,qr=i.ForcedEOSTokenLogitsProcessor,$r=i.GLPNFeatureExtractor,Wr=i.GLPNForDepthEstimation,Ur=i.GLPNModel,Qr=i.GLPNPreTrainedModel,Xr=i.GPT2LMHeadModel,Hr=i.GPT2Model,Jr=i.GPT2PreTrainedModel,Yr=i.GPT2Tokenizer,Kr=i.GPTBigCodeForCausalLM,Zr=i.GPTBigCodeModel,eo=i.GPTBigCodePreTrainedModel,to=i.GPTJForCausalLM,so=i.GPTJModel,ro=i.GPTJPreTrainedModel,oo=i.GPTNeoForCausalLM,no=i.GPTNeoModel,ao=i.GPTNeoPreTrainedModel,io=i.GPTNeoXForCausalLM,lo=i.GPTNeoXModel,co=i.GPTNeoXPreTrainedModel,uo=i.GPTNeoXTokenizer,_o=i.Gemma2ForCausalLM,mo=i.Gemma2Model,po=i.Gemma2PreTrainedModel,ho=i.Gemma3ForCausalLM,go=i.Gemma3Model,fo=i.Gemma3PreTrainedModel,Mo=i.Gemma3nAudioFeatureExtractor,wo=i.Gemma3nForConditionalGeneration,To=i.Gemma3nPreTrainedModel,bo=i.Gemma3nProcessor,xo=i.GemmaForCausalLM,Po=i.GemmaModel,ko=i.GemmaPreTrainedModel,Fo=i.GemmaTokenizer,vo=i.GlmForCausalLM,yo=i.GlmModel,Co=i.GlmPreTrainedModel,So=i.GraniteForCausalLM,Ao=i.GraniteModel,Eo=i.GranitePreTrainedModel,Lo=i.Grok1Tokenizer,Io=i.GroundingDinoForObjectDetection,zo=i.GroundingDinoImageProcessor,Do=i.GroundingDinoPreTrainedModel,jo=i.GroundingDinoProcessor,Vo=i.GroupViTModel,No=i.GroupViTPreTrainedModel,Oo=i.HeliumForCausalLM,Bo=i.HeliumModel,Go=i.HeliumPreTrainedModel,Ro=i.HerbertTokenizer,qo=i.HieraForImageClassification,$o=i.HieraModel,Wo=i.HieraPreTrainedModel,Uo=i.HubertForCTC,Qo=i.HubertForSequenceClassification,Xo=i.HubertModel,Ho=i.HubertPreTrainedModel,Jo=i.IJepaForImageClassification,Yo=i.IJepaModel,Ko=i.IJepaPreTrainedModel,Zo=i.Idefics3ForConditionalGeneration,en=i.Idefics3ImageProcessor,tn=i.Idefics3PreTrainedModel,sn=i.Idefics3Processor,rn=i.ImageClassificationPipeline,on=i.ImageFeatureExtractionPipeline,nn=i.ImageFeatureExtractor,an=i.ImageMattingOutput,ln=i.ImageProcessor,cn=i.ImageSegmentationPipeline,dn=i.ImageToImagePipeline,un=i.ImageToTextPipeline,_n=i.InterruptableStoppingCriteria,mn=i.JAISLMHeadModel,pn=i.JAISModel,hn=i.JAISPreTrainedModel,gn=i.JinaCLIPImageProcessor,fn=i.JinaCLIPModel,Mn=i.JinaCLIPPreTrainedModel,wn=i.JinaCLIPProcessor,Tn=i.JinaCLIPTextModel,bn=i.JinaCLIPVisionModel,xn=i.Lfm2ForCausalLM,Pn=i.Lfm2Model,kn=i.Lfm2PreTrainedModel,Fn=i.LiteWhisperForConditionalGeneration,vn=i.LlamaForCausalLM,yn=i.LlamaModel,Cn=i.LlamaPreTrainedModel,Sn=i.LlamaTokenizer,An=i.LlavaForConditionalGeneration,En=i.LlavaOnevisionForConditionalGeneration,Ln=i.LlavaOnevisionImageProcessor,In=i.LlavaPreTrainedModel,zn=i.LlavaProcessor,Dn=i.LlavaQwen2ForCausalLM,jn=i.LogitsProcessor,Vn=i.LogitsProcessorList,Nn=i.LogitsWarper,On=i.LongT5ForConditionalGeneration,Bn=i.LongT5Model,Gn=i.LongT5PreTrainedModel,Rn=i.M2M100ForConditionalGeneration,qn=i.M2M100Model,$n=i.M2M100PreTrainedModel,Wn=i.M2M100Tokenizer,Un=i.MBart50Tokenizer,Qn=i.MBartForCausalLM,Xn=i.MBartForConditionalGeneration,Hn=i.MBartForSequenceClassification,Jn=i.MBartModel,Yn=i.MBartPreTrainedModel,Kn=i.MBartTokenizer,Zn=i.MPNetForMaskedLM,ea=i.MPNetForQuestionAnswering,ta=i.MPNetForSequenceClassification,sa=i.MPNetForTokenClassification,ra=i.MPNetModel,oa=i.MPNetPreTrainedModel,na=i.MPNetTokenizer,aa=i.MT5ForConditionalGeneration,ia=i.MT5Model,la=i.MT5PreTrainedModel,ca=i.MarianMTModel,da=i.MarianModel,ua=i.MarianPreTrainedModel,_a=i.MarianTokenizer,ma=i.Mask2FormerImageProcessor,pa=i.MaskFormerFeatureExtractor,ha=i.MaskFormerForInstanceSegmentation,ga=i.MaskFormerImageProcessor,fa=i.MaskFormerModel,Ma=i.MaskFormerPreTrainedModel,wa=i.MaskedLMOutput,Ta=i.MaxLengthCriteria,ba=i.Metric3DForDepthEstimation,xa=i.Metric3DPreTrainedModel,Pa=i.Metric3Dv2ForDepthEstimation,ka=i.Metric3Dv2PreTrainedModel,Fa=i.MgpstrForSceneTextRecognition,va=i.MgpstrModelOutput,ya=i.MgpstrPreTrainedModel,Ca=i.MgpstrProcessor,Sa=i.MgpstrTokenizer,Aa=i.MimiDecoderModel,Ea=i.MimiDecoderOutput,La=i.MimiEncoderModel,Ia=i.MimiEncoderOutput,za=i.MimiModel,Da=i.MimiPreTrainedModel,ja=i.MinLengthLogitsProcessor,Va=i.MinNewTokensLengthLogitsProcessor,Na=i.MistralForCausalLM,Oa=i.MistralModel,Ba=i.MistralPreTrainedModel,Ga=i.MobileBertForMaskedLM,Ra=i.MobileBertForQuestionAnswering,qa=i.MobileBertForSequenceClassification,$a=i.MobileBertModel,Wa=i.MobileBertPreTrainedModel,Ua=i.MobileBertTokenizer,Qa=i.MobileLLMForCausalLM,Xa=i.MobileLLMModel,Ha=i.MobileLLMPreTrainedModel,Ja=i.MobileNetV1FeatureExtractor,Ya=i.MobileNetV1ForImageClassification,Ka=i.MobileNetV1ForSemanticSegmentation,Za=i.MobileNetV1ImageProcessor,ei=i.MobileNetV1Model,ti=i.MobileNetV1PreTrainedModel,si=i.MobileNetV2FeatureExtractor,ri=i.MobileNetV2ForImageClassification,oi=i.MobileNetV2ForSemanticSegmentation,ni=i.MobileNetV2ImageProcessor,ai=i.MobileNetV2Model,ii=i.MobileNetV2PreTrainedModel,li=i.MobileNetV3FeatureExtractor,ci=i.MobileNetV3ForImageClassification,di=i.MobileNetV3ForSemanticSegmentation,ui=i.MobileNetV3ImageProcessor,_i=i.MobileNetV3Model,mi=i.MobileNetV3PreTrainedModel,pi=i.MobileNetV4FeatureExtractor,hi=i.MobileNetV4ForImageClassification,gi=i.MobileNetV4ForSemanticSegmentation,fi=i.MobileNetV4ImageProcessor,Mi=i.MobileNetV4Model,wi=i.MobileNetV4PreTrainedModel,Ti=i.MobileViTFeatureExtractor,bi=i.MobileViTForImageClassification,xi=i.MobileViTImageProcessor,Pi=i.MobileViTModel,ki=i.MobileViTPreTrainedModel,Fi=i.MobileViTV2ForImageClassification,vi=i.MobileViTV2Model,yi=i.MobileViTV2PreTrainedModel,Ci=i.ModelOutput,Si=i.ModernBertDecoderForCausalLM,Ai=i.ModernBertDecoderModel,Ei=i.ModernBertDecoderPreTrainedModel,Li=i.ModernBertForMaskedLM,Ii=i.ModernBertForSequenceClassification,zi=i.ModernBertForTokenClassification,Di=i.ModernBertModel,ji=i.ModernBertPreTrainedModel,Vi=i.Moondream1ForConditionalGeneration,Ni=i.MoonshineFeatureExtractor,Oi=i.MoonshineForConditionalGeneration,Bi=i.MoonshineModel,Gi=i.MoonshinePreTrainedModel,Ri=i.MoonshineProcessor,qi=i.MptForCausalLM,$i=i.MptModel,Wi=i.MptPreTrainedModel,Ui=i.MultiModalityCausalLM,Qi=i.MultiModalityPreTrainedModel,Xi=i.MusicgenForCausalLM,Hi=i.MusicgenForConditionalGeneration,Ji=i.MusicgenModel,Yi=i.MusicgenPreTrainedModel,Ki=i.NeoBertForMaskedLM,Zi=i.NeoBertForQuestionAnswering,el=i.NeoBertForSequenceClassification,tl=i.NeoBertForTokenClassification,sl=i.NeoBertModel,rl=i.NeoBertPreTrainedModel,ol=i.NllbTokenizer,nl=i.NoBadWordsLogitsProcessor,al=i.NoRepeatNGramLogitsProcessor,il=i.NomicBertModel,ll=i.NomicBertPreTrainedModel,cl=i.NougatImageProcessor,dl=i.NougatTokenizer,ul=i.OPTForCausalLM,_l=i.OPTModel,ml=i.OPTPreTrainedModel,pl=i.ObjectDetectionPipeline,hl=i.Olmo2ForCausalLM,gl=i.Olmo2Model,fl=i.Olmo2PreTrainedModel,Ml=i.OlmoForCausalLM,wl=i.OlmoModel,Tl=i.OlmoPreTrainedModel,bl=i.OpenELMForCausalLM,xl=i.OpenELMModel,Pl=i.OpenELMPreTrainedModel,kl=i.OwlViTFeatureExtractor,Fl=i.OwlViTForObjectDetection,vl=i.OwlViTImageProcessor,yl=i.OwlViTModel,Cl=i.OwlViTPreTrainedModel,Sl=i.OwlViTProcessor,Al=i.Owlv2ForObjectDetection,El=i.Owlv2ImageProcessor,Ll=i.Owlv2Model,Il=i.Owlv2PreTrainedModel,zl=i.PaliGemmaForConditionalGeneration,Dl=i.PaliGemmaPreTrainedModel,jl=i.PaliGemmaProcessor,Vl=i.PatchTSMixerForPrediction,Nl=i.PatchTSMixerModel,Ol=i.PatchTSMixerPreTrainedModel,Bl=i.PatchTSTForPrediction,Gl=i.PatchTSTModel,Rl=i.PatchTSTPreTrainedModel,ql=i.Phi3ForCausalLM,$l=i.Phi3Model,Wl=i.Phi3PreTrainedModel,Ul=i.Phi3VForCausalLM,Ql=i.Phi3VImageProcessor,Xl=i.Phi3VPreTrainedModel,Hl=i.Phi3VProcessor,Jl=i.PhiForCausalLM,Yl=i.PhiModel,Kl=i.PhiPreTrainedModel,Zl=i.Pipeline,ec=i.PreTrainedModel,tc=i.PreTrainedTokenizer,sc=i.PretrainedConfig,rc=i.PretrainedMixin,oc=i.Processor,nc=i.PvtForImageClassification,ac=i.PvtImageProcessor,ic=i.PvtModel,lc=i.PvtPreTrainedModel,cc=i.PyAnnoteFeatureExtractor,dc=i.PyAnnoteForAudioFrameClassification,uc=i.PyAnnoteModel,_c=i.PyAnnotePreTrainedModel,mc=i.PyAnnoteProcessor,pc=i.QuestionAnsweringModelOutput,hc=i.QuestionAnsweringPipeline,gc=i.Qwen2ForCausalLM,fc=i.Qwen2Model,Mc=i.Qwen2PreTrainedModel,wc=i.Qwen2Tokenizer,Tc=i.Qwen2VLForConditionalGeneration,bc=i.Qwen2VLImageProcessor,xc=i.Qwen2VLPreTrainedModel,Pc=i.Qwen2VLProcessor,kc=i.Qwen3ForCausalLM,Fc=i.Qwen3Model,vc=i.Qwen3PreTrainedModel,yc=i.RFDetrForObjectDetection,Cc=i.RFDetrModel,Sc=i.RFDetrObjectDetectionOutput,Ac=i.RFDetrPreTrainedModel,Ec=i.RTDetrForObjectDetection,Lc=i.RTDetrImageProcessor,Ic=i.RTDetrModel,zc=i.RTDetrObjectDetectionOutput,Dc=i.RTDetrPreTrainedModel,jc=i.RTDetrV2ForObjectDetection,Vc=i.RTDetrV2Model,Nc=i.RTDetrV2ObjectDetectionOutput,Oc=i.RTDetrV2PreTrainedModel,Bc=i.RawAudio,Gc=i.RawImage,Rc=i.RawVideo,qc=i.RawVideoFrame,$c=i.RepetitionPenaltyLogitsProcessor,Wc=i.ResNetForImageClassification,Uc=i.ResNetModel,Qc=i.ResNetPreTrainedModel,Xc=i.RoFormerForMaskedLM,Hc=i.RoFormerForQuestionAnswering,Jc=i.RoFormerForSequenceClassification,Yc=i.RoFormerForTokenClassification,Kc=i.RoFormerModel,Zc=i.RoFormerPreTrainedModel,ed=i.RoFormerTokenizer,td=i.RobertaForMaskedLM,sd=i.RobertaForQuestionAnswering,rd=i.RobertaForSequenceClassification,od=i.RobertaForTokenClassification,nd=i.RobertaModel,ad=i.RobertaPreTrainedModel,id=i.RobertaTokenizer,ld=i.SamImageProcessor,cd=i.SamImageSegmentationOutput,dd=i.SamModel,ud=i.SamPreTrainedModel,_d=i.SamProcessor,md=i.SapiensForDepthEstimation,pd=i.SapiensForNormalEstimation,hd=i.SapiensForSemanticSegmentation,gd=i.SapiensPreTrainedModel,fd=i.SeamlessM4TFeatureExtractor,Md=i.SegformerFeatureExtractor,wd=i.SegformerForImageClassification,Td=i.SegformerForSemanticSegmentation,bd=i.SegformerImageProcessor,xd=i.SegformerModel,Pd=i.SegformerPreTrainedModel,kd=i.Seq2SeqLMOutput,Fd=i.SequenceClassifierOutput,vd=i.SiglipImageProcessor,yd=i.SiglipModel,Cd=i.SiglipPreTrainedModel,Sd=i.SiglipTextModel,Ad=i.SiglipTokenizer,Ed=i.SiglipVisionModel,Ld=i.SmolLM3ForCausalLM,Id=i.SmolLM3Model,zd=i.SmolLM3PreTrainedModel,Dd=i.SmolVLMForConditionalGeneration,jd=i.SmolVLMImageProcessor,Vd=i.SmolVLMProcessor,Nd=i.SnacDecoderModel,Od=i.SnacEncoderModel,Bd=i.SnacFeatureExtractor,Gd=i.SnacModel,Rd=i.SnacPreTrainedModel,qd=i.SpeechT5FeatureExtractor,$d=i.SpeechT5ForSpeechToText,Wd=i.SpeechT5ForTextToSpeech,Ud=i.SpeechT5HifiGan,Qd=i.SpeechT5Model,Xd=i.SpeechT5PreTrainedModel,Hd=i.SpeechT5Processor,Jd=i.SpeechT5Tokenizer,Yd=i.SqueezeBertForMaskedLM,Kd=i.SqueezeBertForQuestionAnswering,Zd=i.SqueezeBertForSequenceClassification,eu=i.SqueezeBertModel,tu=i.SqueezeBertPreTrainedModel,su=i.SqueezeBertTokenizer,ru=i.StableLmForCausalLM,ou=i.StableLmModel,nu=i.StableLmPreTrainedModel,au=i.Starcoder2ForCausalLM,iu=i.Starcoder2Model,lu=i.Starcoder2PreTrainedModel,cu=i.StoppingCriteria,du=i.StoppingCriteriaList,uu=i.StyleTextToSpeech2Model,_u=i.StyleTextToSpeech2PreTrainedModel,mu=i.SummarizationPipeline,pu=i.SuppressTokensAtBeginLogitsProcessor,hu=i.Swin2SRForImageSuperResolution,gu=i.Swin2SRImageProcessor,fu=i.Swin2SRModel,Mu=i.Swin2SRPreTrainedModel,wu=i.SwinForImageClassification,Tu=i.SwinForSemanticSegmentation,bu=i.SwinModel,xu=i.SwinPreTrainedModel,Pu=i.T5ForConditionalGeneration,ku=i.T5Model,Fu=i.T5PreTrainedModel,vu=i.T5Tokenizer,yu=i.TableTransformerForObjectDetection,Cu=i.TableTransformerModel,Su=i.TableTransformerObjectDetectionOutput,Au=i.TableTransformerPreTrainedModel,Eu=i.TemperatureLogitsWarper,Lu=i.Tensor,Iu=i.Text2TextGenerationPipeline,zu=i.TextClassificationPipeline,Du=i.TextGenerationPipeline,ju=i.TextStreamer,Vu=i.TextToAudioPipeline,Nu=i.TokenClassificationPipeline,Ou=i.TokenClassifierOutput,Bu=i.TokenizerModel,Gu=i.TopKLogitsWarper,Ru=i.TopPLogitsWarper,qu=i.TrOCRForCausalLM,$u=i.TrOCRPreTrainedModel,Wu=i.TranslationPipeline,Uu=i.UltravoxModel,Qu=i.UltravoxPreTrainedModel,Xu=i.UltravoxProcessor,Hu=i.UniSpeechForCTC,Ju=i.UniSpeechForSequenceClassification,Yu=i.UniSpeechModel,Ku=i.UniSpeechPreTrainedModel,Zu=i.UniSpeechSatForAudioFrameClassification,e_=i.UniSpeechSatForCTC,t_=i.UniSpeechSatForSequenceClassification,s_=i.UniSpeechSatModel,r_=i.UniSpeechSatPreTrainedModel,o_=i.VLChatProcessor,n_=i.VLMImageProcessor,a_=i.ViTFeatureExtractor,i_=i.ViTForImageClassification,l_=i.ViTImageProcessor,c_=i.ViTMAEModel,d_=i.ViTMAEPreTrainedModel,u_=i.ViTMSNForImageClassification,__=i.ViTMSNModel,m_=i.ViTMSNPreTrainedModel,p_=i.ViTModel,h_=i.ViTPreTrainedModel,g_=i.VisionEncoderDecoderModel,f_=i.VitMatteForImageMatting,M_=i.VitMatteImageProcessor,w_=i.VitMattePreTrainedModel,T_=i.VitPoseForPoseEstimation,b_=i.VitPoseImageProcessor,x_=i.VitPosePreTrainedModel,P_=i.VitsModel,k_=i.VitsModelOutput,F_=i.VitsPreTrainedModel,v_=i.VitsTokenizer,y_=i.VoxtralForConditionalGeneration,C_=i.VoxtralProcessor,S_=i.Wav2Vec2BertForCTC,A_=i.Wav2Vec2BertForSequenceClassification,E_=i.Wav2Vec2BertModel,L_=i.Wav2Vec2BertPreTrainedModel,I_=i.Wav2Vec2CTCTokenizer,z_=i.Wav2Vec2FeatureExtractor,D_=i.Wav2Vec2ForAudioFrameClassification,j_=i.Wav2Vec2ForCTC,V_=i.Wav2Vec2ForSequenceClassification,N_=i.Wav2Vec2Model,O_=i.Wav2Vec2PreTrainedModel,B_=i.Wav2Vec2Processor,G_=i.Wav2Vec2ProcessorWithLM,R_=i.WavLMForAudioFrameClassification,q_=i.WavLMForCTC,$_=i.WavLMForSequenceClassification,W_=i.WavLMForXVector,U_=i.WavLMModel,Q_=i.WavLMPreTrainedModel,X_=i.WeSpeakerFeatureExtractor,H_=i.WeSpeakerResNetModel,J_=i.WeSpeakerResNetPreTrainedModel,Y_=i.WhisperFeatureExtractor,K_=i.WhisperForConditionalGeneration,Z_=i.WhisperModel,em=i.WhisperPreTrainedModel,tm=i.WhisperProcessor,sm=i.WhisperTextStreamer,rm=i.WhisperTimeStampLogitsProcessor,om=i.WhisperTokenizer,nm=i.XLMForQuestionAnswering,am=i.XLMForSequenceClassification,im=i.XLMForTokenClassification,lm=i.XLMModel,cm=i.XLMPreTrainedModel,dm=i.XLMRobertaForMaskedLM,um=i.XLMRobertaForQuestionAnswering,_m=i.XLMRobertaForSequenceClassification,mm=i.XLMRobertaForTokenClassification,pm=i.XLMRobertaModel,hm=i.XLMRobertaPreTrainedModel,gm=i.XLMRobertaTokenizer,fm=i.XLMTokenizer,Mm=i.XLMWithLMHeadModel,wm=i.XVectorOutput,Tm=i.YolosFeatureExtractor,bm=i.YolosForObjectDetection,xm=i.YolosImageProcessor,Pm=i.YolosModel,km=i.YolosObjectDetectionOutput,Fm=i.YolosPreTrainedModel,vm=i.ZeroShotAudioClassificationPipeline,ym=i.ZeroShotClassificationPipeline,Cm=i.ZeroShotImageClassificationPipeline,Sm=i.ZeroShotObjectDetectionPipeline,Am=i.bankers_round,Em=i.cat,Lm=i.cos_sim,Im=i.dot,zm=i.dynamic_time_warping,Dm=i.env,jm=i.full,Vm=i.full_like,Nm=i.getCacheShapes,Om=i.hamming,Bm=i.hanning,Gm=i.interpolate,Rm=i.interpolate_4d,qm=i.interpolate_data,$m=i.is_chinese_char,Wm=i.layer_norm,Um=i.load_image,Qm=i.load_video,Xm=i.log_softmax,Hm=i.magnitude,Jm=i.matmul,Ym=i.max,Km=i.mean,Zm=i.mean_pooling,ep=i.medianFilter,tp=i.mel_filter_bank,sp=i.min,rp=i.ones,op=i.ones_like,np=i.permute,ap=i.permute_data,ip=i.pipeline,lp=i.quantize_embeddings,cp=i.rand,dp=i.read_audio,up=i.rfft,_p=i.round,mp=i.slice,pp=i.softmax,hp=i.spectrogram,gp=i.stack,fp=i.std_mean,Mp=i.topk,wp=i.window_function,Tp=i.zeros,bp=i.zeros_like;export{l as ASTFeatureExtractor,c as ASTForAudioClassification,d as ASTModel,u as ASTPreTrainedModel,_ as AlbertForMaskedLM,m as AlbertForQuestionAnswering,p as AlbertForSequenceClassification,h as AlbertModel,g as AlbertPreTrainedModel,f as AlbertTokenizer,M as ArceeForCausalLM,w as ArceeModel,T as ArceePreTrainedModel,b as AudioClassificationPipeline,x as AutoConfig,P as AutoFeatureExtractor,k as AutoImageProcessor,F as AutoModel,v as AutoModelForAudioClassification,y as AutoModelForAudioFrameClassification,C as AutoModelForAudioTextToText,S as AutoModelForCTC,A as AutoModelForCausalLM,E as AutoModelForDepthEstimation,L as AutoModelForDocumentQuestionAnswering,I as AutoModelForImageClassification,z as AutoModelForImageFeatureExtraction,D as AutoModelForImageMatting,j as AutoModelForImageSegmentation,V as AutoModelForImageTextToText,N as AutoModelForImageToImage,O as AutoModelForMaskGeneration,B as AutoModelForMaskedLM,G as AutoModelForNormalEstimation,R as AutoModelForObjectDetection,q as AutoModelForPoseEstimation,$ as AutoModelForQuestionAnswering,W as AutoModelForSemanticSegmentation,U as AutoModelForSeq2SeqLM,Q as AutoModelForSequenceClassification,X as AutoModelForSpeechSeq2Seq,H as AutoModelForTextToSpectrogram,J as AutoModelForTextToWaveform,Y as AutoModelForTokenClassification,K as AutoModelForUniversalSegmentation,Z as AutoModelForVision2Seq,ee as AutoModelForXVector,te as AutoModelForZeroShotObjectDetection,se as AutoProcessor,re as AutoTokenizer,oe as AutomaticSpeechRecognitionPipeline,ne as BackgroundRemovalPipeline,ae as BartForConditionalGeneration,ie as BartForSequenceClassification,le as BartModel,ce as BartPretrainedModel,de as BartTokenizer,ue as BaseModelOutput,_e as BaseStreamer,me as BeitFeatureExtractor,pe as BeitForImageClassification,he as BeitModel,ge as BeitPreTrainedModel,fe as BertForMaskedLM,Me as BertForQuestionAnswering,we as BertForSequenceClassification,Te as BertForTokenClassification,be as BertModel,xe as BertPreTrainedModel,Pe as BertTokenizer,ke as BitImageProcessor,Fe as BlenderbotForConditionalGeneration,ve as BlenderbotModel,ye as BlenderbotPreTrainedModel,Ce as BlenderbotSmallForConditionalGeneration,Se as BlenderbotSmallModel,Ae as BlenderbotSmallPreTrainedModel,Ee as BlenderbotSmallTokenizer,Le as BlenderbotTokenizer,Ie as BloomForCausalLM,ze as BloomModel,De as BloomPreTrainedModel,je as BloomTokenizer,Ve as CLIPFeatureExtractor,Ne as CLIPImageProcessor,Oe as CLIPModel,Be as CLIPPreTrainedModel,Ge as CLIPSegForImageSegmentation,Re as CLIPSegModel,qe as CLIPSegPreTrainedModel,$e as CLIPTextModel,We as CLIPTextModelWithProjection,Ue as CLIPTokenizer,Qe as CLIPVisionModel,Xe as CLIPVisionModelWithProjection,He as CamembertForMaskedLM,Je as CamembertForQuestionAnswering,Ye as CamembertForSequenceClassification,Ke as CamembertForTokenClassification,Ze as CamembertModel,et as CamembertPreTrainedModel,tt as CamembertTokenizer,st as CausalLMOutput,rt as CausalLMOutputWithPast,ot as ChineseCLIPFeatureExtractor,nt as ChineseCLIPModel,at as ChineseCLIPPreTrainedModel,it as ClapAudioModelWithProjection,lt as ClapFeatureExtractor,ct as ClapModel,dt as ClapPreTrainedModel,ut as ClapTextModelWithProjection,_t as ClassifierFreeGuidanceLogitsProcessor,mt as CodeGenForCausalLM,pt as CodeGenModel,ht as CodeGenPreTrainedModel,gt as CodeGenTokenizer,ft as CodeLlamaTokenizer,Mt as CohereForCausalLM,wt as CohereModel,Tt as CoherePreTrainedModel,bt as CohereTokenizer,xt as ConvBertForMaskedLM,Pt as ConvBertForQuestionAnswering,kt as ConvBertForSequenceClassification,Ft as ConvBertForTokenClassification,vt as ConvBertModel,yt as ConvBertPreTrainedModel,Ct as ConvBertTokenizer,St as ConvNextFeatureExtractor,At as ConvNextForImageClassification,Et as ConvNextImageProcessor,Lt as ConvNextModel,It as ConvNextPreTrainedModel,zt as ConvNextV2ForImageClassification,Dt as ConvNextV2Model,jt as ConvNextV2PreTrainedModel,Vt as DFineForObjectDetection,Nt as DFineModel,Ot as DFinePreTrainedModel,Bt as DINOv3ConvNextModel,Gt as DINOv3ConvNextPreTrainedModel,Rt as DINOv3ViTImageProcessor,qt as DINOv3ViTModel,$t as DINOv3ViTPreTrainedModel,Wt as DPTFeatureExtractor,Ut as DPTForDepthEstimation,Qt as DPTImageProcessor,Xt as DPTModel,Ht as DPTPreTrainedModel,Jt as DacDecoderModel,Yt as DacDecoderOutput,Kt as DacEncoderModel,Zt as DacEncoderOutput,es as DacFeatureExtractor,ts as DacModel,ss as DacPreTrainedModel,rs as DataTypeMap,os as DebertaForMaskedLM,ns as DebertaForQuestionAnswering,as as DebertaForSequenceClassification,is as DebertaForTokenClassification,ls as DebertaModel,cs as DebertaPreTrainedModel,ds as DebertaTokenizer,us as DebertaV2ForMaskedLM,_s as DebertaV2ForQuestionAnswering,ms as DebertaV2ForSequenceClassification,ps as DebertaV2ForTokenClassification,hs as DebertaV2Model,gs as DebertaV2PreTrainedModel,fs as DebertaV2Tokenizer,Ms as DecisionTransformerModel,ws as DecisionTransformerPreTrainedModel,Ts as DeiTFeatureExtractor,bs as DeiTForImageClassification,xs as DeiTImageProcessor,Ps as DeiTModel,ks as DeiTPreTrainedModel,Fs as DepthAnythingForDepthEstimation,vs as DepthAnythingPreTrainedModel,ys as DepthEstimationPipeline,Cs as DepthProForDepthEstimation,Ss as DepthProPreTrainedModel,As as DetrFeatureExtractor,Es as DetrForObjectDetection,Ls as DetrForSegmentation,Is as DetrImageProcessor,zs as DetrModel,Ds as DetrObjectDetectionOutput,js as DetrPreTrainedModel,Vs as DetrSegmentationOutput,Ns as Dinov2ForImageClassification,Os as Dinov2Model,Bs as Dinov2PreTrainedModel,Gs as Dinov2WithRegistersForImageClassification,Rs as Dinov2WithRegistersModel,qs as Dinov2WithRegistersPreTrainedModel,$s as DistilBertForMaskedLM,Ws as DistilBertForQuestionAnswering,Us as DistilBertForSequenceClassification,Qs as DistilBertForTokenClassification,Xs as DistilBertModel,Hs as DistilBertPreTrainedModel,Js as DistilBertTokenizer,Ys as DocumentQuestionAnsweringPipeline,Ks as DonutFeatureExtractor,Zs as DonutImageProcessor,er as DonutSwinModel,tr as DonutSwinPreTrainedModel,sr as EfficientNetForImageClassification,rr as EfficientNetImageProcessor,or as EfficientNetModel,nr as EfficientNetPreTrainedModel,ar as ElectraForMaskedLM,ir as ElectraForQuestionAnswering,lr as ElectraForSequenceClassification,cr as ElectraForTokenClassification,dr as ElectraModel,ur as ElectraPreTrainedModel,_r as ElectraTokenizer,mr as EncodecFeatureExtractor,pr as EosTokenCriteria,hr as Ernie4_5_ForCausalLM,gr as Ernie4_5_Model,fr as Ernie4_5_PretrainedModel,Mr as Ernie4_5_Tokenizer,wr as EsmForMaskedLM,Tr as EsmForSequenceClassification,br as EsmForTokenClassification,xr as EsmModel,Pr as EsmPreTrainedModel,kr as EsmTokenizer,Fr as ExaoneForCausalLM,vr as ExaoneModel,yr as ExaonePreTrainedModel,Cr as FFT,Sr as FalconForCausalLM,Ar as FalconModel,Er as FalconPreTrainedModel,Lr as FalconTokenizer,Ir as FastViTForImageClassification,zr as FastViTModel,Dr as FastViTPreTrainedModel,jr as FeatureExtractionPipeline,Vr as FeatureExtractor,Nr as FillMaskPipeline,Or as Florence2ForConditionalGeneration,Br as Florence2PreTrainedModel,Gr as Florence2Processor,Rr as ForcedBOSTokenLogitsProcessor,qr as ForcedEOSTokenLogitsProcessor,$r as GLPNFeatureExtractor,Wr as GLPNForDepthEstimation,Ur as GLPNModel,Qr as GLPNPreTrainedModel,Xr as GPT2LMHeadModel,Hr as GPT2Model,Jr as GPT2PreTrainedModel,Yr as GPT2Tokenizer,Kr as GPTBigCodeForCausalLM,Zr as GPTBigCodeModel,eo as GPTBigCodePreTrainedModel,to as GPTJForCausalLM,so as GPTJModel,ro as GPTJPreTrainedModel,oo as GPTNeoForCausalLM,no as GPTNeoModel,ao as GPTNeoPreTrainedModel,io as GPTNeoXForCausalLM,lo as GPTNeoXModel,co as GPTNeoXPreTrainedModel,uo as GPTNeoXTokenizer,_o as Gemma2ForCausalLM,mo as Gemma2Model,po as Gemma2PreTrainedModel,ho as Gemma3ForCausalLM,go as Gemma3Model,fo as Gemma3PreTrainedModel,Mo as Gemma3nAudioFeatureExtractor,wo as Gemma3nForConditionalGeneration,To as Gemma3nPreTrainedModel,bo as Gemma3nProcessor,xo as GemmaForCausalLM,Po as GemmaModel,ko as GemmaPreTrainedModel,Fo as GemmaTokenizer,vo as GlmForCausalLM,yo as GlmModel,Co as GlmPreTrainedModel,So as GraniteForCausalLM,Ao as GraniteModel,Eo as GranitePreTrainedModel,Lo as Grok1Tokenizer,Io as GroundingDinoForObjectDetection,zo as GroundingDinoImageProcessor,Do as GroundingDinoPreTrainedModel,jo as GroundingDinoProcessor,Vo as GroupViTModel,No as GroupViTPreTrainedModel,Oo as HeliumForCausalLM,Bo as HeliumModel,Go as HeliumPreTrainedModel,Ro as HerbertTokenizer,qo as HieraForImageClassification,$o as HieraModel,Wo as HieraPreTrainedModel,Uo as HubertForCTC,Qo as HubertForSequenceClassification,Xo as HubertModel,Ho as HubertPreTrainedModel,Jo as IJepaForImageClassification,Yo as IJepaModel,Ko as IJepaPreTrainedModel,Zo as Idefics3ForConditionalGeneration,en as Idefics3ImageProcessor,tn as Idefics3PreTrainedModel,sn as Idefics3Processor,rn as ImageClassificationPipeline,on as ImageFeatureExtractionPipeline,nn as ImageFeatureExtractor,an as ImageMattingOutput,ln as ImageProcessor,cn as ImageSegmentationPipeline,dn as ImageToImagePipeline,un as ImageToTextPipeline,_n as InterruptableStoppingCriteria,mn as JAISLMHeadModel,pn as JAISModel,hn as JAISPreTrainedModel,gn as JinaCLIPImageProcessor,fn as JinaCLIPModel,Mn as JinaCLIPPreTrainedModel,wn as JinaCLIPProcessor,Tn as JinaCLIPTextModel,bn as JinaCLIPVisionModel,xn as Lfm2ForCausalLM,Pn as Lfm2Model,kn as Lfm2PreTrainedModel,Fn as LiteWhisperForConditionalGeneration,vn as LlamaForCausalLM,yn as LlamaModel,Cn as LlamaPreTrainedModel,Sn as LlamaTokenizer,An as LlavaForConditionalGeneration,En as LlavaOnevisionForConditionalGeneration,Ln as LlavaOnevisionImageProcessor,In as LlavaPreTrainedModel,zn as LlavaProcessor,Dn as LlavaQwen2ForCausalLM,jn as LogitsProcessor,Vn as LogitsProcessorList,Nn as LogitsWarper,On as LongT5ForConditionalGeneration,Bn as LongT5Model,Gn as LongT5PreTrainedModel,Rn as M2M100ForConditionalGeneration,qn as M2M100Model,$n as M2M100PreTrainedModel,Wn as M2M100Tokenizer,Un as MBart50Tokenizer,Qn as MBartForCausalLM,Xn as MBartForConditionalGeneration,Hn as MBartForSequenceClassification,Jn as MBartModel,Yn as MBartPreTrainedModel,Kn as MBartTokenizer,Zn as MPNetForMaskedLM,ea as MPNetForQuestionAnswering,ta as MPNetForSequenceClassification,sa as MPNetForTokenClassification,ra as MPNetModel,oa as MPNetPreTrainedModel,na as MPNetTokenizer,aa as MT5ForConditionalGeneration,ia as MT5Model,la as MT5PreTrainedModel,ca as MarianMTModel,da as MarianModel,ua as MarianPreTrainedModel,_a as MarianTokenizer,ma as Mask2FormerImageProcessor,pa as MaskFormerFeatureExtractor,ha as MaskFormerForInstanceSegmentation,ga as MaskFormerImageProcessor,fa as MaskFormerModel,Ma as MaskFormerPreTrainedModel,wa as MaskedLMOutput,Ta as MaxLengthCriteria,ba as Metric3DForDepthEstimation,xa as Metric3DPreTrainedModel,Pa as Metric3Dv2ForDepthEstimation,ka as Metric3Dv2PreTrainedModel,Fa as MgpstrForSceneTextRecognition,va as MgpstrModelOutput,ya as MgpstrPreTrainedModel,Ca as MgpstrProcessor,Sa as MgpstrTokenizer,Aa as MimiDecoderModel,Ea as MimiDecoderOutput,La as MimiEncoderModel,Ia as MimiEncoderOutput,za as MimiModel,Da as MimiPreTrainedModel,ja as MinLengthLogitsProcessor,Va as MinNewTokensLengthLogitsProcessor,Na as MistralForCausalLM,Oa as MistralModel,Ba as MistralPreTrainedModel,Ga as MobileBertForMaskedLM,Ra as MobileBertForQuestionAnswering,qa as MobileBertForSequenceClassification,$a as MobileBertModel,Wa as MobileBertPreTrainedModel,Ua as MobileBertTokenizer,Qa as MobileLLMForCausalLM,Xa as MobileLLMModel,Ha as MobileLLMPreTrainedModel,Ja as MobileNetV1FeatureExtractor,Ya as MobileNetV1ForImageClassification,Ka as MobileNetV1ForSemanticSegmentation,Za as MobileNetV1ImageProcessor,ei as MobileNetV1Model,ti as MobileNetV1PreTrainedModel,si as MobileNetV2FeatureExtractor,ri as MobileNetV2ForImageClassification,oi as MobileNetV2ForSemanticSegmentation,ni as MobileNetV2ImageProcessor,ai as MobileNetV2Model,ii as MobileNetV2PreTrainedModel,li as MobileNetV3FeatureExtractor,ci as MobileNetV3ForImageClassification,di as MobileNetV3ForSemanticSegmentation,ui as MobileNetV3ImageProcessor,_i as MobileNetV3Model,mi as MobileNetV3PreTrainedModel,pi as MobileNetV4FeatureExtractor,hi as MobileNetV4ForImageClassification,gi as MobileNetV4ForSemanticSegmentation,fi as MobileNetV4ImageProcessor,Mi as MobileNetV4Model,wi as MobileNetV4PreTrainedModel,Ti as MobileViTFeatureExtractor,bi as MobileViTForImageClassification,xi as MobileViTImageProcessor,Pi as MobileViTModel,ki as MobileViTPreTrainedModel,Fi as MobileViTV2ForImageClassification,vi as MobileViTV2Model,yi as MobileViTV2PreTrainedModel,Ci as ModelOutput,Si as ModernBertDecoderForCausalLM,Ai as ModernBertDecoderModel,Ei as ModernBertDecoderPreTrainedModel,Li as ModernBertForMaskedLM,Ii as ModernBertForSequenceClassification,zi as ModernBertForTokenClassification,Di as ModernBertModel,ji as ModernBertPreTrainedModel,Vi as Moondream1ForConditionalGeneration,Ni as MoonshineFeatureExtractor,Oi as MoonshineForConditionalGeneration,Bi as MoonshineModel,Gi as MoonshinePreTrainedModel,Ri as MoonshineProcessor,qi as MptForCausalLM,$i as MptModel,Wi as MptPreTrainedModel,Ui as MultiModalityCausalLM,Qi as MultiModalityPreTrainedModel,Xi as MusicgenForCausalLM,Hi as MusicgenForConditionalGeneration,Ji as MusicgenModel,Yi as MusicgenPreTrainedModel,Ki as NeoBertForMaskedLM,Zi as NeoBertForQuestionAnswering,el as NeoBertForSequenceClassification,tl as NeoBertForTokenClassification,sl as NeoBertModel,rl as NeoBertPreTrainedModel,ol as NllbTokenizer,nl as NoBadWordsLogitsProcessor,al as NoRepeatNGramLogitsProcessor,il as NomicBertModel,ll as NomicBertPreTrainedModel,cl as NougatImageProcessor,dl as NougatTokenizer,ul as OPTForCausalLM,_l as OPTModel,ml as OPTPreTrainedModel,pl as ObjectDetectionPipeline,hl as Olmo2ForCausalLM,gl as Olmo2Model,fl as Olmo2PreTrainedModel,Ml as OlmoForCausalLM,wl as OlmoModel,Tl as OlmoPreTrainedModel,bl as OpenELMForCausalLM,xl as OpenELMModel,Pl as OpenELMPreTrainedModel,kl as OwlViTFeatureExtractor,Fl as OwlViTForObjectDetection,vl as OwlViTImageProcessor,yl as OwlViTModel,Cl as OwlViTPreTrainedModel,Sl as OwlViTProcessor,Al as Owlv2ForObjectDetection,El as Owlv2ImageProcessor,Ll as Owlv2Model,Il as Owlv2PreTrainedModel,zl as PaliGemmaForConditionalGeneration,Dl as PaliGemmaPreTrainedModel,jl as PaliGemmaProcessor,Vl as PatchTSMixerForPrediction,Nl as PatchTSMixerModel,Ol as PatchTSMixerPreTrainedModel,Bl as PatchTSTForPrediction,Gl as PatchTSTModel,Rl as PatchTSTPreTrainedModel,ql as Phi3ForCausalLM,$l as Phi3Model,Wl as Phi3PreTrainedModel,Ul as Phi3VForCausalLM,Ql as Phi3VImageProcessor,Xl as Phi3VPreTrainedModel,Hl as Phi3VProcessor,Jl as PhiForCausalLM,Yl as PhiModel,Kl as PhiPreTrainedModel,Zl as Pipeline,ec as PreTrainedModel,tc as PreTrainedTokenizer,sc as PretrainedConfig,rc as PretrainedMixin,oc as Processor,nc as PvtForImageClassification,ac as PvtImageProcessor,ic as PvtModel,lc as PvtPreTrainedModel,cc as PyAnnoteFeatureExtractor,dc as PyAnnoteForAudioFrameClassification,uc as PyAnnoteModel,_c as PyAnnotePreTrainedModel,mc as PyAnnoteProcessor,pc as QuestionAnsweringModelOutput,hc as QuestionAnsweringPipeline,gc as Qwen2ForCausalLM,fc as Qwen2Model,Mc as Qwen2PreTrainedModel,wc as Qwen2Tokenizer,Tc as Qwen2VLForConditionalGeneration,bc as Qwen2VLImageProcessor,xc as Qwen2VLPreTrainedModel,Pc as Qwen2VLProcessor,kc as Qwen3ForCausalLM,Fc as Qwen3Model,vc as Qwen3PreTrainedModel,yc as RFDetrForObjectDetection,Cc as RFDetrModel,Sc as RFDetrObjectDetectionOutput,Ac as RFDetrPreTrainedModel,Ec as RTDetrForObjectDetection,Lc as RTDetrImageProcessor,Ic as RTDetrModel,zc as RTDetrObjectDetectionOutput,Dc as RTDetrPreTrainedModel,jc as RTDetrV2ForObjectDetection,Vc as RTDetrV2Model,Nc as RTDetrV2ObjectDetectionOutput,Oc as RTDetrV2PreTrainedModel,Bc as RawAudio,Gc as RawImage,Rc as RawVideo,qc as RawVideoFrame,$c as RepetitionPenaltyLogitsProcessor,Wc as ResNetForImageClassification,Uc as ResNetModel,Qc as ResNetPreTrainedModel,Xc as RoFormerForMaskedLM,Hc as RoFormerForQuestionAnswering,Jc as RoFormerForSequenceClassification,Yc as RoFormerForTokenClassification,Kc as RoFormerModel,Zc as RoFormerPreTrainedModel,ed as RoFormerTokenizer,td as RobertaForMaskedLM,sd as RobertaForQuestionAnswering,rd as RobertaForSequenceClassification,od as RobertaForTokenClassification,nd as RobertaModel,ad as RobertaPreTrainedModel,id as RobertaTokenizer,ld as SamImageProcessor,cd as SamImageSegmentationOutput,dd as SamModel,ud as SamPreTrainedModel,_d as SamProcessor,md as SapiensForDepthEstimation,pd as SapiensForNormalEstimation,hd as SapiensForSemanticSegmentation,gd as SapiensPreTrainedModel,fd as SeamlessM4TFeatureExtractor,Md as SegformerFeatureExtractor,wd as SegformerForImageClassification,Td as SegformerForSemanticSegmentation,bd as SegformerImageProcessor,xd as SegformerModel,Pd as SegformerPreTrainedModel,kd as Seq2SeqLMOutput,Fd as SequenceClassifierOutput,vd as SiglipImageProcessor,yd as SiglipModel,Cd as SiglipPreTrainedModel,Sd as SiglipTextModel,Ad as SiglipTokenizer,Ed as SiglipVisionModel,Ld as SmolLM3ForCausalLM,Id as SmolLM3Model,zd as SmolLM3PreTrainedModel,Dd as SmolVLMForConditionalGeneration,jd as SmolVLMImageProcessor,Vd as SmolVLMProcessor,Nd as SnacDecoderModel,Od as SnacEncoderModel,Bd as SnacFeatureExtractor,Gd as SnacModel,Rd as SnacPreTrainedModel,qd as SpeechT5FeatureExtractor,$d as SpeechT5ForSpeechToText,Wd as SpeechT5ForTextToSpeech,Ud as SpeechT5HifiGan,Qd as SpeechT5Model,Xd as SpeechT5PreTrainedModel,Hd as SpeechT5Processor,Jd as SpeechT5Tokenizer,Yd as SqueezeBertForMaskedLM,Kd as SqueezeBertForQuestionAnswering,Zd as SqueezeBertForSequenceClassification,eu as SqueezeBertModel,tu as SqueezeBertPreTrainedModel,su as SqueezeBertTokenizer,ru as StableLmForCausalLM,ou as StableLmModel,nu as StableLmPreTrainedModel,au as Starcoder2ForCausalLM,iu as Starcoder2Model,lu as Starcoder2PreTrainedModel,cu as StoppingCriteria,du as StoppingCriteriaList,uu as StyleTextToSpeech2Model,_u as StyleTextToSpeech2PreTrainedModel,mu as SummarizationPipeline,pu as SuppressTokensAtBeginLogitsProcessor,hu as Swin2SRForImageSuperResolution,gu as Swin2SRImageProcessor,fu as Swin2SRModel,Mu as Swin2SRPreTrainedModel,wu as SwinForImageClassification,Tu as SwinForSemanticSegmentation,bu as SwinModel,xu as SwinPreTrainedModel,Pu as T5ForConditionalGeneration,ku as T5Model,Fu as T5PreTrainedModel,vu as T5Tokenizer,yu as TableTransformerForObjectDetection,Cu as TableTransformerModel,Su as TableTransformerObjectDetectionOutput,Au as TableTransformerPreTrainedModel,Eu as TemperatureLogitsWarper,Lu as Tensor,Iu as Text2TextGenerationPipeline,zu as TextClassificationPipeline,Du as TextGenerationPipeline,ju as TextStreamer,Vu as TextToAudioPipeline,Nu as TokenClassificationPipeline,Ou as TokenClassifierOutput,Bu as TokenizerModel,Gu as TopKLogitsWarper,Ru as TopPLogitsWarper,qu as TrOCRForCausalLM,$u as TrOCRPreTrainedModel,Wu as TranslationPipeline,Uu as UltravoxModel,Qu as UltravoxPreTrainedModel,Xu as UltravoxProcessor,Hu as UniSpeechForCTC,Ju as UniSpeechForSequenceClassification,Yu as UniSpeechModel,Ku as UniSpeechPreTrainedModel,Zu as UniSpeechSatForAudioFrameClassification,e_ as UniSpeechSatForCTC,t_ as UniSpeechSatForSequenceClassification,s_ as UniSpeechSatModel,r_ as UniSpeechSatPreTrainedModel,o_ as VLChatProcessor,n_ as VLMImageProcessor,a_ as ViTFeatureExtractor,i_ as ViTForImageClassification,l_ as ViTImageProcessor,c_ as ViTMAEModel,d_ as ViTMAEPreTrainedModel,u_ as ViTMSNForImageClassification,__ as ViTMSNModel,m_ as ViTMSNPreTrainedModel,p_ as ViTModel,h_ as ViTPreTrainedModel,g_ as VisionEncoderDecoderModel,f_ as VitMatteForImageMatting,M_ as VitMatteImageProcessor,w_ as VitMattePreTrainedModel,T_ as VitPoseForPoseEstimation,b_ as VitPoseImageProcessor,x_ as VitPosePreTrainedModel,P_ as VitsModel,k_ as VitsModelOutput,F_ as VitsPreTrainedModel,v_ as VitsTokenizer,y_ as VoxtralForConditionalGeneration,C_ as VoxtralProcessor,S_ as Wav2Vec2BertForCTC,A_ as Wav2Vec2BertForSequenceClassification,E_ as Wav2Vec2BertModel,L_ as Wav2Vec2BertPreTrainedModel,I_ as Wav2Vec2CTCTokenizer,z_ as Wav2Vec2FeatureExtractor,D_ as Wav2Vec2ForAudioFrameClassification,j_ as Wav2Vec2ForCTC,V_ as Wav2Vec2ForSequenceClassification,N_ as Wav2Vec2Model,O_ as Wav2Vec2PreTrainedModel,B_ as Wav2Vec2Processor,G_ as Wav2Vec2ProcessorWithLM,R_ as WavLMForAudioFrameClassification,q_ as WavLMForCTC,$_ as WavLMForSequenceClassification,W_ as WavLMForXVector,U_ as WavLMModel,Q_ as WavLMPreTrainedModel,X_ as WeSpeakerFeatureExtractor,H_ as WeSpeakerResNetModel,J_ as WeSpeakerResNetPreTrainedModel,Y_ as WhisperFeatureExtractor,K_ as WhisperForConditionalGeneration,Z_ as WhisperModel,em as WhisperPreTrainedModel,tm as WhisperProcessor,sm as WhisperTextStreamer,rm as WhisperTimeStampLogitsProcessor,om as WhisperTokenizer,nm as XLMForQuestionAnswering,am as XLMForSequenceClassification,im as XLMForTokenClassification,lm as XLMModel,cm as XLMPreTrainedModel,dm as XLMRobertaForMaskedLM,um as XLMRobertaForQuestionAnswering,_m as XLMRobertaForSequenceClassification,mm as XLMRobertaForTokenClassification,pm as XLMRobertaModel,hm as XLMRobertaPreTrainedModel,gm as XLMRobertaTokenizer,fm as XLMTokenizer,Mm as XLMWithLMHeadModel,wm as XVectorOutput,Tm as YolosFeatureExtractor,bm as YolosForObjectDetection,xm as YolosImageProcessor,Pm as YolosModel,km as YolosObjectDetectionOutput,Fm as YolosPreTrainedModel,vm as ZeroShotAudioClassificationPipeline,ym as ZeroShotClassificationPipeline,Cm as ZeroShotImageClassificationPipeline,Sm as ZeroShotObjectDetectionPipeline,Am as bankers_round,Em as cat,Lm as cos_sim,Im as dot,zm as dynamic_time_warping,Dm as env,jm as full,Vm as full_like,Nm as getCacheShapes,Om as hamming,Bm as hanning,Gm as interpolate,Rm as interpolate_4d,qm as interpolate_data,$m as is_chinese_char,Wm as layer_norm,Um as load_image,Qm as load_video,Xm as log_softmax,Hm as magnitude,Jm as matmul,Ym as max,Km as mean,Zm as mean_pooling,ep as medianFilter,tp as mel_filter_bank,sp as min,rp as ones,op as ones_like,np as permute,ap as permute_data,ip as pipeline,lp as quantize_embeddings,cp as rand,dp as read_audio,up as rfft,_p as round,mp as slice,pp as softmax,hp as spectrogram,gp as stack,fp as std_mean,Mp as topk,wp as window_function,Tp as zeros,bp as zeros_like};
//# sourceMappingURL=transformers.web.min.js.map