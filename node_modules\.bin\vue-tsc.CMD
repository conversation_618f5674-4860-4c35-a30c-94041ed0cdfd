@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vue-tsc@2.2.12_typescript@5.9.2\node_modules\vue-tsc\bin\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vue-tsc@2.2.12_typescript@5.9.2\node_modules\vue-tsc\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vue-tsc@2.2.12_typescript@5.9.2\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vue-tsc@2.2.12_typescript@5.9.2\node_modules\vue-tsc\bin\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vue-tsc@2.2.12_typescript@5.9.2\node_modules\vue-tsc\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\vue-tsc@2.2.12_typescript@5.9.2\node_modules;D:\Project\Other_Project\ClaudeCode_Project\Test_3\smolvlm-realtime-webgpu\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vue-tsc\bin\vue-tsc.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vue-tsc\bin\vue-tsc.js" %*
)
