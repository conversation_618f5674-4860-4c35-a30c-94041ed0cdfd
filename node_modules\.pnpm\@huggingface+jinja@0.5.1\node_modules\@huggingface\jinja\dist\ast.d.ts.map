{"version": 3, "file": "ast.d.ts", "sourceRoot": "", "sources": ["../src/ast.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAErC;;GAEG;AACH,qBAAa,SAAS;IACrB,IAAI,SAAe;CACnB;AAED;;GAEG;AACH,qBAAa,OAAQ,SAAQ,SAAS;IAGlB,IAAI,EAAE,SAAS,EAAE;IAF3B,IAAI,SAAa;gBAEP,IAAI,EAAE,SAAS,EAAE;CAGpC;AAED,qBAAa,EAAG,SAAQ,SAAS;IAIxB,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,SAAS,EAAE;IACjB,SAAS,EAAE,SAAS,EAAE;IALrB,IAAI,SAAQ;gBAGb,IAAI,EAAE,UAAU,EAChB,IAAI,EAAE,SAAS,EAAE,EACjB,SAAS,EAAE,SAAS,EAAE;CAI9B;AAED;;;GAGG;AACH,qBAAa,GAAI,SAAQ,SAAS;IAIzB,OAAO,EAAE,UAAU,GAAG,YAAY;IAClC,QAAQ,EAAE,UAAU;IACpB,IAAI,EAAE,SAAS,EAAE;IACjB,YAAY,EAAE,SAAS,EAAE;IANxB,IAAI,SAAS;gBAGd,OAAO,EAAE,UAAU,GAAG,YAAY,EAClC,QAAQ,EAAE,UAAU,EACpB,IAAI,EAAE,SAAS,EAAE,EACjB,YAAY,EAAE,SAAS,EAAE;CAIjC;AAED,qBAAa,KAAM,SAAQ,SAAS;IAC1B,IAAI,SAAW;CACxB;AACD,qBAAa,QAAS,SAAQ,SAAS;IAC7B,IAAI,SAAc;CAC3B;AAED,qBAAa,YAAa,SAAQ,SAAS;IAGlC,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,UAAU,GAAG,IAAI;IACxB,IAAI,EAAE,SAAS,EAAE;IAJhB,IAAI,SAAS;gBAEd,QAAQ,EAAE,UAAU,EACpB,KAAK,EAAE,UAAU,GAAG,IAAI,EACxB,IAAI,EAAE,SAAS,EAAE;CAIzB;AAED,qBAAa,KAAM,SAAQ,SAAS;IAI3B,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,UAAU,EAAE;IAClB,IAAI,EAAE,SAAS,EAAE;IALhB,IAAI,SAAW;gBAGhB,IAAI,EAAE,UAAU,EAChB,IAAI,EAAE,UAAU,EAAE,EAClB,IAAI,EAAE,SAAS,EAAE;CAIzB;AAED,qBAAa,OAAQ,SAAQ,SAAS;IAElB,KAAK,EAAE,MAAM;IADvB,IAAI,SAAa;gBACP,KAAK,EAAE,MAAM;CAGhC;AAED;;GAEG;AACH,qBAAa,UAAW,SAAQ,SAAS;IAC/B,IAAI,SAAgB;CAC7B;AAED,qBAAa,gBAAiB,SAAQ,UAAU;IAIvC,MAAM,EAAE,UAAU;IAClB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,OAAO;IALhB,IAAI,SAAsB;gBAG3B,MAAM,EAAE,UAAU,EAClB,QAAQ,EAAE,UAAU,EACpB,QAAQ,EAAE,OAAO;CAIzB;AAED,qBAAa,cAAe,SAAQ,UAAU;IAIrC,MAAM,EAAE,UAAU;IAClB,IAAI,EAAE,UAAU,EAAE;IAJjB,IAAI,SAAoB;gBAGzB,MAAM,EAAE,UAAU,EAClB,IAAI,EAAE,UAAU,EAAE;CAI1B;AAED;;GAEG;AACH,qBAAa,UAAW,SAAQ,UAAU;IAMtB,KAAK,EAAE,MAAM;IALvB,IAAI,SAAgB;IAE7B;;OAEG;gBACgB,KAAK,EAAE,MAAM;CAGhC;AAED;;;GAGG;AACH,uBAAe,OAAO,CAAC,CAAC,CAAE,SAAQ,UAAU;IAGxB,KAAK,EAAE,CAAC;IAFlB,IAAI,SAAa;gBAEP,KAAK,EAAE,CAAC;CAG3B;AAED,qBAAa,cAAe,SAAQ,OAAO,CAAC,MAAM,CAAC;IACzC,IAAI,SAAoB;CACjC;AAED,qBAAa,YAAa,SAAQ,OAAO,CAAC,MAAM,CAAC;IACvC,IAAI,SAAkB;CAC/B;AAED;;GAEG;AACH,qBAAa,aAAc,SAAQ,OAAO,CAAC,MAAM,CAAC;IACxC,IAAI,SAAmB;CAChC;AAED;;GAEG;AACH,qBAAa,YAAa,SAAQ,OAAO,CAAC,UAAU,EAAE,CAAC;IAC7C,IAAI,SAAkB;CAC/B;AAED;;GAEG;AACH,qBAAa,YAAa,SAAQ,OAAO,CAAC,UAAU,EAAE,CAAC;IAC7C,IAAI,SAAkB;CAC/B;AAED;;GAEG;AACH,qBAAa,aAAc,SAAQ,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC7D,IAAI,SAAmB;CAChC;AAED;;;;GAIG;AACH,qBAAa,gBAAiB,SAAQ,UAAU;IAIvC,QAAQ,EAAE,KAAK;IACf,IAAI,EAAE,UAAU;IAChB,KAAK,EAAE,UAAU;IALhB,IAAI,SAAsB;gBAG3B,QAAQ,EAAE,KAAK,EACf,IAAI,EAAE,UAAU,EAChB,KAAK,EAAE,UAAU;CAIzB;AAED;;;GAGG;AACH,qBAAa,gBAAiB,SAAQ,UAAU;IAIvC,OAAO,EAAE,UAAU;IACnB,MAAM,EAAE,UAAU,GAAG,cAAc;IAJlC,IAAI,SAAsB;gBAG3B,OAAO,EAAE,UAAU,EACnB,MAAM,EAAE,UAAU,GAAG,cAAc;CAI3C;AAED,qBAAa,eAAgB,SAAQ,SAAS;IAIrC,MAAM,EAAE,UAAU,GAAG,cAAc;IACnC,IAAI,EAAE,SAAS,EAAE;IAJhB,IAAI,SAAqB;gBAG1B,MAAM,EAAE,UAAU,GAAG,cAAc,EACnC,IAAI,EAAE,SAAS,EAAE;CAIzB;AAED;;;;;GAKG;AACH,qBAAa,gBAAiB,SAAQ,UAAU;IAIvC,GAAG,EAAE,UAAU;IACf,IAAI,EAAE,UAAU;IAJf,IAAI,SAAsB;gBAG3B,GAAG,EAAE,UAAU,EACf,IAAI,EAAE,UAAU;CAIxB;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,UAAU;IAIrC,OAAO,EAAE,UAAU;IACnB,MAAM,EAAE,OAAO;IACf,IAAI,EAAE,UAAU;IALf,IAAI,SAAoB;gBAGzB,OAAO,EAAE,UAAU,EACnB,MAAM,EAAE,OAAO,EACf,IAAI,EAAE,UAAU;CAIxB;AAED;;GAEG;AACH,qBAAa,eAAgB,SAAQ,UAAU;IAItC,QAAQ,EAAE,KAAK;IACf,QAAQ,EAAE,UAAU;IAJnB,IAAI,SAAqB;gBAG1B,QAAQ,EAAE,KAAK,EACf,QAAQ,EAAE,UAAU;CAI5B;AAED,qBAAa,eAAgB,SAAQ,UAAU;IAItC,KAAK,EAAE,UAAU,GAAG,SAAS;IAC7B,IAAI,EAAE,UAAU,GAAG,SAAS;IAC5B,IAAI,EAAE,UAAU,GAAG,SAAS;IAL3B,IAAI,SAAqB;gBAG1B,KAAK,GAAE,UAAU,GAAG,SAAqB,EACzC,IAAI,GAAE,UAAU,GAAG,SAAqB,EACxC,IAAI,GAAE,UAAU,GAAG,SAAqB;CAIhD;AAED,qBAAa,yBAA0B,SAAQ,UAAU;IAIhD,GAAG,EAAE,UAAU;IACf,KAAK,EAAE,UAAU;IAJhB,IAAI,SAA+B;gBAGpC,GAAG,EAAE,UAAU,EACf,KAAK,EAAE,UAAU;CAIzB;AAED,qBAAa,gBAAiB,SAAQ,UAAU;IAG5B,QAAQ,EAAE,UAAU;IAF9B,IAAI,SAAsB;gBAEhB,QAAQ,EAAE,UAAU;CAGvC;AAED,qBAAa,aAAc,SAAQ,SAAS;IAInC,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,UAAU,EAAE,GAAG,IAAI;IAC/B,IAAI,EAAE,SAAS,EAAE;IALhB,IAAI,SAAmB;gBAGxB,IAAI,EAAE,cAAc,EACpB,UAAU,EAAE,UAAU,EAAE,GAAG,IAAI,EAC/B,IAAI,EAAE,SAAS,EAAE;CAIzB;AAED,qBAAa,OAAQ,SAAQ,UAAU;IAG9B,SAAS,EAAE,UAAU;IACrB,QAAQ,EAAE,UAAU;IACpB,SAAS,EAAE,UAAU;IAJpB,IAAI,SAAa;gBAElB,SAAS,EAAE,UAAU,EACrB,QAAQ,EAAE,UAAU,EACpB,SAAS,EAAE,UAAU;CAI7B"}