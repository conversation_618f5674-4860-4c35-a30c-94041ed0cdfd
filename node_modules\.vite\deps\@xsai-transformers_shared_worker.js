import "./chunk-V6TY7KAL.js";

// node_modules/@xsai-transformers/shared/dist/worker/index.js
var createTransformersWorker = (createOptions) => {
  let worker;
  let isReady = false;
  let isLoading = false;
  const load = async (payload, options) => {
    if (!payload)
      throw new Error("Payload is required");
    return new Promise((resolve, reject) => {
      if (!createOptions.workerURL)
        throw new Error("Worker URL is required");
      if (isReady) {
        resolve();
        return;
      }
      try {
        let onProgress;
        if ((options == null ? void 0 : options.onProgress) != null) {
          onProgress = options == null ? void 0 : options.onProgress;
          options == null ? true : delete options.onProgress;
        }
        if (!isLoading && !isReady && !worker) {
          let workerURLString;
          if (createOptions.workerURL instanceof URL) {
            const workerURL = new URL(createOptions.workerURL);
            workerURLString = workerURL.searchParams.get("worker-url");
          } else {
            workerURLString = createOptions.workerURL;
          }
          if (!workerURLString)
            throw new Error("Worker URL is required");
          if (!worker)
            worker = new Worker(workerURLString, { type: "module" });
          if (!worker)
            throw new Error("Worker not initialized");
          worker.postMessage(payload);
          worker.addEventListener("message", (event) => {
            switch (event.data.type) {
              case "error":
                isLoading = false;
                reject(event.data.data.error);
                break;
              case "progress":
                if (onProgress != null && typeof onProgress === "function") {
                  onProgress(event.data.data.progress);
                }
                break;
            }
          });
        }
        worker.addEventListener("message", (event) => {
          if (event.data.type !== "status" || event.data.data.status !== "ready")
            return;
          isReady = true;
          isLoading = false;
          resolve();
        });
      } catch (err) {
        isLoading = false;
        reject(err);
      }
    });
  };
  const ensureLoadBeforeProcess = async (options) => {
    var _a, _b;
    if (options != null && !(options == null ? void 0 : options.loadOptions))
      await load((_a = options == null ? void 0 : options.loadOptions) == null ? void 0 : _a.payload, (_b = options == null ? void 0 : options.loadOptions) == null ? void 0 : _b.options);
  };
  const process = (payload, onResultType, options) => {
    return new Promise((resolve, reject) => {
      ensureLoadBeforeProcess(options).then(() => {
        if (!worker || !isReady) {
          reject(new Error("Model not loaded"));
          return;
        }
        worker.addEventListener("error", (event) => {
          reject(event);
        });
        let errored = false;
        let resultDone = false;
        worker.addEventListener("message", (event) => {
          var _a;
          switch (event.data.type) {
            case "error":
              errored = true;
              reject((_a = event.data.data) == null ? void 0 : _a.error);
              break;
            case onResultType:
              resultDone = true;
              resolve(event.data.data);
              break;
          }
        });
        if (!errored && !resultDone) {
          worker == null ? void 0 : worker.postMessage(payload);
        }
      });
    });
  };
  return {
    dispose: () => {
      if (!worker)
        return;
      worker.terminate();
      isReady = false;
      isLoading = false;
      worker = void 0;
    },
    load,
    process
  };
};
export {
  createTransformersWorker
};
//# sourceMappingURL=@xsai-transformers_shared_worker.js.map
