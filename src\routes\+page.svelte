<script>
	import { onMount } from 'svelte';
	import {
		AutoProcessor,
		AutoModelForVision2Seq,
		RawImage
	} from '@huggingface/transformers';

	// 响应式变量
	let videoElement;
	let canvasElement;
	let instructionText = "What do you see?";
	let responseText = "Camera access granted. Ready to start.";
	let intervalValue = "500";
	let isProcessing = false;
	let stream = null;
	let showLoading = true;

	// AI模型相关
	let processor = null;
	let model = null;
	let modelLoaded = false;
	let processingCount = 0;

	// 下载进度相关
	let downloadProgress = 0;
	let downloadStatus = '';
	let isDownloading = false;
	let downloadError = null;

	// 间隔选项
	const intervalOptions = [
		{ value: "100", label: "100ms" },
		{ value: "250", label: "250ms" },
		{ value: "500", label: "500ms" },
		{ value: "1000", label: "1s" },
		{ value: "2000", label: "2s" }
	];

	onMount(async () => {
		// 检查WebGPU支持
		if (!navigator.gpu) {
			responseText = "❌ 此浏览器不支持 WebGPU。\n\n请使用启用了 WebGPU 的 Chrome/Edge 浏览器：\n1. 打开 chrome://flags/\n2. 启用 'Unsafe WebGPU'\n3. 重启浏览器";
			showLoading = false;
			return;
		}

		responseText = "✅ 检测到 WebGPU 支持。正在初始化 AI 模型...";

		try {
			// 初始化AI模型
			await initModel();
			// 初始化摄像头
			await initCamera();
		} catch (error) {
			console.error('Initialization error:', error);
			responseText = `Initialization failed: ${error.message}`;
		}

		showLoading = false;
	});

	// 清理函数
	function cleanup() {
		if (stream) {
			stream.getTracks().forEach((track) => track.stop());
		}
		isProcessing = false;
	}

	// 重试模型加载
	async function retryModelLoad() {
		showLoading = true;
		downloadError = null;
		downloadProgress = 0;
		isDownloading = false;

		try {
			await initModel();
			await initCamera();
		} catch (error) {
			console.error('Retry failed:', error);
		}

		showLoading = false;
	}

	// 页面卸载时清理资源
	if (typeof window !== 'undefined') {
		window.addEventListener('beforeunload', cleanup);
	}

	// 进度回调函数
	function onProgress(progress) {
		if (progress.status === 'downloading') {
			isDownloading = true;
			downloadProgress = progress.progress || 0;
			downloadStatus = `正在下载 ${progress.file || '模型文件'}...`;
			responseText = `${downloadStatus}\n${Math.round(downloadProgress)}% 已完成`;
		} else if (progress.status === 'loading') {
			downloadStatus = `正在加载 ${progress.file || '模型'}...`;
			responseText = downloadStatus;
		} else if (progress.status === 'ready') {
			isDownloading = false;
			downloadProgress = 100;
			downloadStatus = '模型已就绪！';
		}
	}

	async function initModel() {
		const modelId = "HuggingFaceTB/SmolVLM-500M-Instruct";

		try {
			downloadError = null;
			isDownloading = true;
			downloadProgress = 0;

			responseText = "正在初始化处理器...";
			processor = await AutoProcessor.from_pretrained(modelId, {
				progress_callback: onProgress
			});

			responseText = "处理器已加载。正在加载模型...";
			downloadProgress = 0; // 重置进度，开始模型下载

			model = await AutoModelForVision2Seq.from_pretrained(modelId, {
				dtype: {
					embed_tokens: "fp16",
					vision_encoder: "q4",
					decoder_model_merged: "q4",
				},
				device: "webgpu",
				progress_callback: onProgress
			});

			isDownloading = false;
			modelLoaded = true;
			downloadProgress = 100;
			responseText = "✅ 模型加载成功！正在初始化摄像头...";

		} catch (error) {
			isDownloading = false;
			downloadError = error.message;
			responseText = `❌ 模型加载失败：${error.message}`;
			throw error;
		}
	}

	async function initCamera() {
		try {
			stream = await navigator.mediaDevices.getUserMedia({
				video: true,
				audio: false
			});
			if (videoElement) {
				videoElement.srcObject = stream;
			}
			responseText = "摄像头访问已授权。准备开始。";
		} catch (err) {
			console.error("Error accessing camera:", err);
			responseText = `摄像头访问错误：${err.name} - ${err.message}。请确保已授权摄像头权限，并且在 HTTPS 或 localhost 环境下使用。`;
		}
	}

	function captureImage() {
		if (!stream || !videoElement || !videoElement.videoWidth) {
			console.warn("视频流未准备好进行捕获。");
			return null;
		}

		if (!canvasElement) {
			console.warn("Canvas 元素不可用。");
			return null;
		}

		canvasElement.width = videoElement.videoWidth;
		canvasElement.height = videoElement.videoHeight;
		const context = canvasElement.getContext("2d", { willReadFrequently: true });
		context.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);
		const frame = context.getImageData(0, 0, canvasElement.width, canvasElement.height);
		return new RawImage(frame.data, frame.width, frame.height, 4);
	}

	async function runLocalVisionInference(imgElement, instruction) {
		if (!processor || !model || !modelLoaded) {
			throw new Error("模型尚未加载");
		}

		const messages = [
			{
				role: "user",
				content: [{ type: "image" }, { type: "text", text: instruction }],
			},
		];

		const text = processor.apply_chat_template(messages, {
			add_generation_prompt: true,
		});

		const inputs = await processor(text, [imgElement], {
			do_image_splitting: false,
		});

		const generatedIds = await model.generate({
			...inputs,
			max_new_tokens: 100,
		});

		const output = processor.batch_decode(
			generatedIds.slice(null, [inputs.input_ids.dims.at(-1), null]),
			{ skip_special_tokens: true }
		);

		return output[0].trim();
	}

	async function sendData() {
		if (!isProcessing) return;

		const instruction = instructionText;
		const rawImg = captureImage();

		if (!rawImg) {
			responseText = "捕获失败 - 视频未准备好";
			return;
		}

		try {
			processingCount++;
			responseText = `正在处理... (${processingCount})`;

			const startTime = Date.now();
			const reply = await runLocalVisionInference(rawImg, instruction);
			const endTime = Date.now();

			responseText = `${reply}\n\n[处理耗时 ${endTime - startTime}毫秒 | 处理次数: ${processingCount}]`;
		} catch (e) {
			console.error('AI inference error:', e);
			responseText = `错误：${e.message}\n\n提示：请确保使用支持 WebGPU 的浏览器（Chrome/Edge）并有足够的 GPU 内存。`;
		}
	}

	function sleep(ms) {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	async function processingLoop() {
		const intervalMs = parseInt(intervalValue, 10);
		while (isProcessing) {
			await sendData();
			if (!isProcessing) break;
			await sleep(intervalMs);
		}
	}

	function handleStartStop() {
		if (isProcessing) {
			// 停止处理
			isProcessing = false;
			responseText = "处理已停止。";
		} else {
			// 开始处理
			if (!stream) {
				responseText = "摄像头不可用。无法开始。";
				return;
			}

			if (!modelLoaded) {
				responseText = "模型尚未加载完成。请稍候。";
				return;
			}

			isProcessing = true;
			responseText = "开始处理...";
			processingLoop();
		}
	}
</script>

<svelte:head>
	<title>Camera Interaction App</title>
</svelte:head>

<h1 class="text-3xl font-bold text-center text-blue-600 mb-6">Camera Interaction App</h1>

<video
	id="videoFeed"
	bind:this={videoElement}
	autoplay
	playsinline
	class="w-[480px] h-[360px] border-2 border-gray-800 bg-black rounded-lg"
></video>
<canvas id="canvas" bind:this={canvasElement} class="hidden"></canvas>

<div class="flex flex-col gap-4 bg-white p-6 rounded-lg shadow-md w-full max-w-2xl">
	<div>
		<label for="instructionText" class="block text-sm font-medium text-gray-700 mb-2">Instruction:</label>
		<textarea
			id="instructionText"
			bind:value={instructionText}
			disabled={isProcessing}
			name="Instruction"
			class="w-full h-20 p-3 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
		></textarea>
	</div>
	<div>
		<label for="responseText" class="block text-sm font-medium text-gray-700 mb-2">Response:</label>
		<textarea
			id="responseText"
			bind:value={responseText}
			readonly
			name="Response"
			placeholder="Server response will appear here..."
			class="w-full h-20 p-3 border border-gray-300 rounded-md text-sm bg-gray-50"
		></textarea>
	</div>
</div>

<div class="flex gap-4 items-center bg-white p-4 rounded-lg shadow-md">
	<label for="intervalSelect" class="text-sm font-medium text-gray-700">Interval between 2 requests:</label>
	<select
		id="intervalSelect"
		bind:value={intervalValue}
		disabled={isProcessing}
		name="Interval between 2 requests"
		class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
	>
		{#each intervalOptions as option}
			<option value={option.value}>{option.label}</option>
		{/each}
	</select>
	<button
		id="startButton"
		on:click={handleStartStop}
		class="px-6 py-2 text-white font-medium rounded-md transition-colors duration-200 {isProcessing ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'}"
	>
		{isProcessing ? 'Stop' : 'Start'}
	</button>
</div>

<style lang="postcss">
	/* 使用Tailwind CSS的全局样式 */
	@reference "tailwindcss";
	:global(body) {
		@apply font-sans flex flex-col items-center gap-5 p-5 bg-gray-100 m-0;
	}
</style>
