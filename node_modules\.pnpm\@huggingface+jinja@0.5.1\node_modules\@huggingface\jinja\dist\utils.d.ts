/**
 * Function that mimics Python's range() function.
 * @param start The start value of the range.
 * @param stop The stop value of the range. If not provided, start will be 0 and stop will be the provided start value.
 * @param step The step value of the range. Defaults to 1.
 * @returns The range of numbers.
 */
export declare function range(start: number, stop?: number, step?: number): number[];
/**
 * Function that mimics Python's array slicing.
 * @param array The array to slice.
 * @param start The start index of the slice. Defaults to 0.
 * @param stop The last index of the slice. Defaults to `array.length`.
 * @param step The step value of the slice. Defaults to 1.
 * @returns The sliced array.
 */
export declare function slice<T>(array: T[], start?: number, stop?: number, step?: number): T[];
/**
 * Function that mimics Python's string.title() function.
 * @param value The string to title case.
 * @returns The title cased string.
 */
export declare function titleCase(value: string): string;
export declare function strftime_now(format: string): string;
/**
 * A minimalistic implementation of Python's strftime function.
 */
export declare function strftime(date: Date, format: string): string;
/**
 * Function that mimics Python's string.replace() function.
 */
export declare function replace(str: string, oldvalue: string, newvalue: string, count?: number | null): string;
//# sourceMappingURL=utils.d.ts.map