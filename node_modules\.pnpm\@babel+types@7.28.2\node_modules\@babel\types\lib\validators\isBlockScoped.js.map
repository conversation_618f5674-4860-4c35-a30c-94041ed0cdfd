{"version": 3, "names": ["_index", "require", "_isLet", "isBlockScoped", "node", "isFunctionDeclaration", "isClassDeclaration", "isLet"], "sources": ["../../src/validators/isBlockScoped.ts"], "sourcesContent": ["import {\n  isClassDeclaration,\n  isFunctionDeclaration,\n} from \"./generated/index.ts\";\nimport isLet from \"./isLet.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Check if the input `node` is block scoped.\n */\nexport default function isBlockScoped(node: t.Node): boolean {\n  return isFunctionDeclaration(node) || isClassDeclaration(node) || isLet(node);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAIA,IAAAC,MAAA,GAAAD,OAAA;AAMe,SAASE,aAAaA,CAACC,IAAY,EAAW;EAC3D,OAAO,IAAAC,4BAAqB,EAACD,IAAI,CAAC,IAAI,IAAAE,yBAAkB,EAACF,IAAI,CAAC,IAAI,IAAAG,cAAK,EAACH,IAAI,CAAC;AAC/E", "ignoreList": []}