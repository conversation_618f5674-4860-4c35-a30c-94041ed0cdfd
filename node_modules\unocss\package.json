{"name": "unocss", "type": "module", "version": "66.4.2", "description": "The instant on-demand Atomic CSS engine.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-presets/unocss"}, "sponsor": {"url": "https://github.com/sponsors/antfu"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "atomic-css", "atomic-css-engine", "css", "tailwind", "windicss"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./preset-attributify": {"types": "./dist/preset-attributify.d.mts", "default": "./dist/preset-attributify.mjs"}, "./preset-tagify": {"types": "./dist/preset-tagify.d.mts", "default": "./dist/preset-tagify.mjs"}, "./preset-icons": {"types": "./dist/preset-icons.d.mts", "default": "./dist/preset-icons.mjs"}, "./preset-mini": {"types": "./dist/preset-mini.d.mts", "default": "./dist/preset-mini.mjs"}, "./preset-typography": {"types": "./dist/preset-typography.d.mts", "default": "./dist/preset-typography.mjs"}, "./preset-uno": {"types": "./dist/preset-uno.d.mts", "default": "./dist/preset-uno.mjs"}, "./preset-web-fonts": {"types": "./dist/preset-web-fonts.d.mts", "default": "./dist/preset-web-fonts.mjs"}, "./preset-wind": {"types": "./dist/preset-wind.d.mts", "default": "./dist/preset-wind.mjs"}, "./preset-wind3": {"types": "./dist/preset-wind3.d.mts", "default": "./dist/preset-wind3.mjs"}, "./preset-wind4": {"types": "./dist/preset-wind4.d.mts", "default": "./dist/preset-wind4.mjs"}, "./vite": {"types": "./dist/vite.d.mts", "default": "./dist/vite.mjs"}, "./astro": {"types": "./dist/astro.d.mts", "default": "./dist/astro.mjs"}, "./webpack": {"import": {"types": "./dist/webpack.d.mts", "default": "./dist/webpack.mjs"}, "require": {"types": "./dist/webpack.d.cts", "default": "./dist/webpack.cjs"}}, "./postcss": {"import": {"types": "./dist/postcss.d.mts", "default": "./dist/postcss.mjs"}, "require": {"types": "./dist/postcss.d.cts", "default": "./dist/postcss.cjs"}}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["dist"], "engines": {"node": ">=14"}, "peerDependencies": {"vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0", "@unocss/webpack": "66.4.2"}, "peerDependenciesMeta": {"@unocss/webpack": {"optional": true}, "vite": {"optional": true}}, "dependencies": {"@unocss/cli": "66.4.2", "@unocss/core": "66.4.2", "@unocss/astro": "66.4.2", "@unocss/preset-attributify": "66.4.2", "@unocss/postcss": "66.4.2", "@unocss/preset-icons": "66.4.2", "@unocss/preset-mini": "66.4.2", "@unocss/preset-tagify": "66.4.2", "@unocss/preset-typography": "66.4.2", "@unocss/preset-web-fonts": "66.4.2", "@unocss/preset-uno": "66.4.2", "@unocss/preset-wind": "66.4.2", "@unocss/preset-wind4": "66.4.2", "@unocss/preset-wind3": "66.4.2", "@unocss/transformer-attributify-jsx": "66.4.2", "@unocss/transformer-variant-group": "66.4.2", "@unocss/transformer-directives": "66.4.2", "@unocss/transformer-compile-class": "66.4.2", "@unocss/vite": "66.4.2"}, "devDependencies": {"vite": "^7.0.6", "@unocss/webpack": "66.4.2"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}