{"version": 3, "sources": ["../../gpuu/src/webgpu/checker.ts"], "sourcesContent": ["export interface WebGPUCheckResult {\n  supported: boolean\n  fp16Supported: boolean\n  isNode: boolean\n  reason: string\n  adapter?: GPUAdapter\n}\n\nexport async function check(): Promise<WebGPUCheckResult> {\n  try {\n    if (isInNodejsRuntime())\n      return { supported: false, isNode: true, reason: '', fp16Supported: false }\n\n    if (typeof navigator === 'undefined' || !navigator.gpu)\n      return { supported: false, isNode: false, reason: 'WebGPU is not available (navigator.gpu is undefined)', fp16Supported: false }\n\n    const adapter = await navigator.gpu.requestAdapter()\n    if (!adapter)\n      return { supported: false, isNode: false, reason: 'WebGPU is not supported (no adapter found)', fp16Supported: false }\n\n    return { supported: true, isNode: false, reason: '', adapter, fp16Supported: adapter.features.has('shader-f16') }\n  }\n  catch (error) {\n    const errorMessage = error instanceof Error ? error.toString() : String(error)\n    return { supported: false, isNode: false, reason: errorMessage, fp16Supported: false }\n  }\n}\n\nfunction isInNodejsRuntime() {\n  // eslint-disable-next-line node/prefer-global/process\n  return typeof process !== 'undefined'\n    // eslint-disable-next-line node/prefer-global/process\n    && 'versions' in process && process.versions != null && typeof process.versions === 'object'\n    // eslint-disable-next-line node/prefer-global/process\n    && 'node' in process.versions && process.versions.node != null\n}\n\nexport async function isWebGPUSupported() {\n  return check().then(result => result.supported)\n}\n"], "mappings": ";;;AAQA,eAAsB,QAAoC;AACxD,MAAI;AACF,QAAI,kBAAA,EACF,QAAO;MAAE,WAAW;MAAO,QAAQ;MAAM,QAAQ;MAAI,eAAe;IAAO;AAE7E,QAAA,OAAW,cAAc,eAAA,CAAgB,UAAU,IACjD,QAAO;MAAE,WAAW;MAAO,QAAQ;MAAO,QAAQ;MAAwD,eAAe;IAAO;AAElI,UAAM,UAAU,MAAM,UAAU,IAAI,eAAA;AACpC,QAAA,CAAK,QACH,QAAO;MAAE,WAAW;MAAO,QAAQ;MAAO,QAAQ;MAA8C,eAAe;IAAO;AAExH,WAAO;MAAE,WAAW;MAAM,QAAQ;MAAO,QAAQ;MAAI;MAAS,eAAe,QAAQ,SAAS,IAAI,YAAA;IAAe;EAClH,SACM,OAAO;AACZ,UAAM,eAAe,iBAAiB,QAAQ,MAAM,SAAA,IAAa,OAAO,KAAA;AACxE,WAAO;MAAE,WAAW;MAAO,QAAQ;MAAO,QAAQ;MAAc,eAAe;IAAO;EACvF;AACF;AAED,SAAS,oBAAoB;AAE3B,SAAA,OAAc,YAAY,eAErB,cAAc,WAAW,QAAQ,YAAY,QAAA,OAAe,QAAQ,aAAa,YAEjF,UAAU,QAAQ,YAAY,QAAQ,SAAS,QAAQ;AAC7D;AAED,eAAsB,oBAAoB;AACxC,SAAO,MAAA,EAAQ,KAAK,CAAA,WAAU,OAAO,SAAA;AACtC;", "names": []}