import { readFile, stat } from "node:fs/promises";
import { join } from "node:path";
import { defu } from "defu";
import grayMatter from "gray-matter";

//#region src/vite/lfs.ts
const defaultGitAttributes = `# Default
*.7z filter=lfs diff=lfs merge=lfs -text
*.arrow filter=lfs diff=lfs merge=lfs -text
*.bin filter=lfs diff=lfs merge=lfs -text
*.bz2 filter=lfs diff=lfs merge=lfs -text
*.ckpt filter=lfs diff=lfs merge=lfs -text
*.ftz filter=lfs diff=lfs merge=lfs -text
*.gz filter=lfs diff=lfs merge=lfs -text
*.h5 filter=lfs diff=lfs merge=lfs -text
*.joblib filter=lfs diff=lfs merge=lfs -text
*.lfs.* filter=lfs diff=lfs merge=lfs -text
*.mlmodel filter=lfs diff=lfs merge=lfs -text
*.model filter=lfs diff=lfs merge=lfs -text
*.msgpack filter=lfs diff=lfs merge=lfs -text
*.npy filter=lfs diff=lfs merge=lfs -text
*.npz filter=lfs diff=lfs merge=lfs -text
*.onnx filter=lfs diff=lfs merge=lfs -text
*.ot filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text
*.pb filter=lfs diff=lfs merge=lfs -text
*.pickle filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text
*.pt filter=lfs diff=lfs merge=lfs -text
*.pth filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.safetensors filter=lfs diff=lfs merge=lfs -text
saved_model/**/* filter=lfs diff=lfs merge=lfs -text
*.tar.* filter=lfs diff=lfs merge=lfs -text
*.tar filter=lfs diff=lfs merge=lfs -text
*.tflite filter=lfs diff=lfs merge=lfs -text
*.tgz filter=lfs diff=lfs merge=lfs -text
*.wasm filter=lfs diff=lfs merge=lfs -text
*.xz filter=lfs diff=lfs merge=lfs -text
*.zip filter=lfs diff=lfs merge=lfs -text
*.zst filter=lfs diff=lfs merge=lfs -text
*tfevents* filter=lfs diff=lfs merge=lfs -text
`;
function LFS(options) {
	let _config;
	return {
		name: "huggingspace:lfs-gitattributes",
		configResolved(config) {
			_config = config;
		},
		async generateBundle() {
			if (options?.enableFn && !await options.enableFn(_config)) return;
			const extraGlobs = options?.extraGlobs ?? [];
			const extraAttributes = options?.extraAttributes ?? [];
			const withDefault = options?.withDefault ?? true;
			const gitAttributes = withDefault ? defaultGitAttributes : "";
			const extraGlobsIntoGitAttributes = extraGlobs.map((glob) => {
				return `${glob} filter=lfs diff=lfs merge=lfs -text`;
			});
			this.emitFile({
				type: "asset",
				fileName: ".gitattributes",
				source: `${extraAttributes.join("\n")}${extraGlobsIntoGitAttributes.join("\n")}\n${gitAttributes}`
			});
		}
	};
}

//#endregion
//#region src/utils/fs.ts
async function exists(path) {
	try {
		await stat(path);
		return true;
	} catch (error) {
		if (isENOENTError(error)) return false;
		throw error;
	}
}
function isENOENTError(error) {
	if (!(error instanceof Error)) return false;
	if (!("code" in error)) return false;
	if (error.code !== "ENOENT") return false;
	return true;
}

//#endregion
//#region src/vite/space-card/types.ts
const licenseValues = [
	"apache-2.0",
	"mit",
	"openrail",
	"bigscience-openrail-m",
	"creativeml-openrail-m",
	"bigscience-bloom-rail-1.0",
	"bigcode-openrail-m",
	"afl-3.0",
	"artistic-2.0",
	"bsl-1.0",
	"bsd",
	"bsd-2-clause",
	"bsd-3-clause",
	"bsd-3-clause-clear",
	"c-uda",
	"cc",
	"cc0-1.0",
	"cc-by-2.0",
	"cc-by-2.5",
	"cc-by-3.0",
	"cc-by-4.0",
	"cc-by-sa-3.0",
	"cc-by-sa-4.0",
	"cc-by-nc-2.0",
	"cc-by-nc-3.0",
	"cc-by-nc-4.0",
	"cc-by-nd-4.0",
	"cc-by-nc-nd-3.0",
	"cc-by-nc-nd-4.0",
	"cc-by-nc-sa-2.0",
	"cc-by-nc-sa-3.0",
	"cc-by-nc-sa-4.0",
	"cdla-sharing-1.0",
	"cdla-permissive-1.0",
	"cdla-permissive-2.0",
	"wtfpl",
	"ecl-2.0",
	"epl-1.0",
	"epl-2.0",
	"etalab-2.0",
	"eupl-1.1",
	"agpl-3.0",
	"gfdl",
	"gpl",
	"gpl-2.0",
	"gpl-3.0",
	"lgpl",
	"lgpl-2.1",
	"lgpl-3.0",
	"isc",
	"lppl-1.3c",
	"ms-pl",
	"apple-ascl",
	"mpl-2.0",
	"odc-by",
	"odbl",
	"openrail++",
	"osl-3.0",
	"postgresql",
	"ofl-1.1",
	"ncsa",
	"unlicense",
	"zlib",
	"pddl",
	"lgpl-lr",
	"deepfloyd-if-license",
	"llama2",
	"llama3",
	"llama3.1",
	"llama3.2",
	"llama3.3",
	"gemma",
	"unknown",
	"other"
];

//#endregion
//#region src/vite/space-card/index.ts
function SpaceCard(configuration) {
	let _config;
	const _configuration = defu(configuration, {
		emoji: "🚀",
		sdk: "static",
		pinned: false,
		license: "unknown"
	});
	let packageJSON = {};
	let readme = "";
	return {
		name: "huggingspace:readme",
		async configResolved(config) {
			_config = config;
			const rootPackageJSONPath = join(_config.root, "package.json");
			if (await exists(rootPackageJSONPath)) {
				const rootPackageJSONContent = await readFile(rootPackageJSONPath, "utf-8");
				packageJSON = JSON.parse(rootPackageJSONContent);
			}
			const rootReadmePaths = [
				join(_config.root, "README.md"),
				join(_config.root, "readme.md"),
				join(_config.root, "README.markdown"),
				join(_config.root, "readme.markdown"),
				join(_config.root, "README"),
				join(_config.root, "readme")
			];
			for (const rootReadmePath of rootReadmePaths) if (await exists(rootReadmePath)) {
				const readReadme = await readFile(rootReadmePath, "utf-8");
				const { content } = grayMatter(readReadme);
				readme = content;
			}
			if (_configuration.title == null) {
				if (packageJSON == null) throw new Error(`\`title\` is required when \`package.json\` does not exist in the root directory (${rootPackageJSONPath})`);
				if (!packageJSON.name) throw new Error(`\`title\` is required when \`name\` does not exist in the root package.json`);
				_configuration.title = packageJSON.name;
			}
			if (_configuration.license == null) {
				if (packageJSON == null) throw new Error(`\`license\` is required when \`package.json\` does not exist in the root directory (${rootPackageJSONPath})`);
				if (!packageJSON.license) throw new Error(`\`license\` is required when \`license\` does not exist in the root package.json`);
				const license = String(packageJSON.license).toLowerCase();
				if (!licenseValues.includes(license)) throw new Error(`Auto discovered license \`${license}\` in \`package.json\` is not supported. Must be one of licenses: ${licenseValues.join(", ")} to specified in \`configuration\` argument`);
				_configuration.license = license;
			}
			if (_configuration.sdk == null) _configuration.sdk = "static";
			if (_configuration.short_description && _configuration.short_description.length > 60) throw new Error("short_description must be less or equal to 60 characters");
		},
		async generateBundle() {
			if (readme == null) readme = `# ${_configuration.title}`;
			this.emitFile({
				type: "asset",
				fileName: "README.md",
				source: grayMatter.stringify({ content: `\n${readme}` }, _configuration)
			});
		}
	};
}

//#endregion
export { LFS, SpaceCard };