{"version": 3, "sources": ["../../@xsai-transformers/shared/dist/worker/index.js"], "sourcesContent": ["const createTransformersWorker = (createOptions) => {\n  let worker;\n  let isReady = false;\n  let isLoading = false;\n  const load = async (payload, options) => {\n    if (!payload)\n      throw new Error(\"Payload is required\");\n    return new Promise((resolve, reject) => {\n      if (!createOptions.workerURL)\n        throw new Error(\"Worker URL is required\");\n      if (isReady) {\n        resolve();\n        return;\n      }\n      try {\n        let onProgress;\n        if (options?.onProgress != null) {\n          onProgress = options?.onProgress;\n          delete options?.onProgress;\n        }\n        if (!isLoading && !isReady && !worker) {\n          let workerURLString;\n          if (createOptions.workerURL instanceof URL) {\n            const workerURL = new URL(createOptions.workerURL);\n            workerURLString = workerURL.searchParams.get(\"worker-url\");\n          } else {\n            workerURLString = createOptions.workerURL;\n          }\n          if (!workerURLString)\n            throw new Error(\"Worker URL is required\");\n          if (!worker)\n            worker = new Worker(workerURLString, { type: \"module\" });\n          if (!worker)\n            throw new Error(\"Worker not initialized\");\n          worker.postMessage(payload);\n          worker.addEventListener(\"message\", (event) => {\n            switch (event.data.type) {\n              case \"error\":\n                isLoading = false;\n                reject(event.data.data.error);\n                break;\n              case \"progress\":\n                if (onProgress != null && typeof onProgress === \"function\") {\n                  onProgress(event.data.data.progress);\n                }\n                break;\n            }\n          });\n        }\n        worker.addEventListener(\"message\", (event) => {\n          if (event.data.type !== \"status\" || event.data.data.status !== \"ready\")\n            return;\n          isReady = true;\n          isLoading = false;\n          resolve();\n        });\n      } catch (err) {\n        isLoading = false;\n        reject(err);\n      }\n    });\n  };\n  const ensureLoadBeforeProcess = async (options) => {\n    if (options != null && !options?.loadOptions)\n      await load(options?.loadOptions?.payload, options?.loadOptions?.options);\n  };\n  const process = (payload, onResultType, options) => {\n    return new Promise((resolve, reject) => {\n      ensureLoadBeforeProcess(options).then(() => {\n        if (!worker || !isReady) {\n          reject(new Error(\"Model not loaded\"));\n          return;\n        }\n        worker.addEventListener(\"error\", (event) => {\n          reject(event);\n        });\n        let errored = false;\n        let resultDone = false;\n        worker.addEventListener(\"message\", (event) => {\n          switch (event.data.type) {\n            case \"error\":\n              errored = true;\n              reject(event.data.data?.error);\n              break;\n            case onResultType:\n              resultDone = true;\n              resolve(event.data.data);\n              break;\n          }\n        });\n        if (!errored && !resultDone) {\n          worker?.postMessage(payload);\n        }\n      });\n    });\n  };\n  return {\n    dispose: () => {\n      if (!worker)\n        return;\n      worker.terminate();\n      isReady = false;\n      isLoading = false;\n      worker = void 0;\n    },\n    load,\n    process\n  };\n};\n\nexport { createTransformersWorker };\n"], "mappings": ";;;AAAA,IAAM,2BAA2B,CAAC,kBAAkB;AAClD,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,YAAY;AAChB,QAAM,OAAO,OAAO,SAAS,YAAY;AACvC,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,qBAAqB;AACvC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,wBAAwB;AAC1C,UAAI,SAAS;AACX,gBAAQ;AACR;AAAA,MACF;AACA,UAAI;AACF,YAAI;AACJ,aAAI,mCAAS,eAAc,MAAM;AAC/B,uBAAa,mCAAS;AACtB,kDAAgB;AAAA,QAClB;AACA,YAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ;AACrC,cAAI;AACJ,cAAI,cAAc,qBAAqB,KAAK;AAC1C,kBAAM,YAAY,IAAI,IAAI,cAAc,SAAS;AACjD,8BAAkB,UAAU,aAAa,IAAI,YAAY;AAAA,UAC3D,OAAO;AACL,8BAAkB,cAAc;AAAA,UAClC;AACA,cAAI,CAAC;AACH,kBAAM,IAAI,MAAM,wBAAwB;AAC1C,cAAI,CAAC;AACH,qBAAS,IAAI,OAAO,iBAAiB,EAAE,MAAM,SAAS,CAAC;AACzD,cAAI,CAAC;AACH,kBAAM,IAAI,MAAM,wBAAwB;AAC1C,iBAAO,YAAY,OAAO;AAC1B,iBAAO,iBAAiB,WAAW,CAAC,UAAU;AAC5C,oBAAQ,MAAM,KAAK,MAAM;AAAA,cACvB,KAAK;AACH,4BAAY;AACZ,uBAAO,MAAM,KAAK,KAAK,KAAK;AAC5B;AAAA,cACF,KAAK;AACH,oBAAI,cAAc,QAAQ,OAAO,eAAe,YAAY;AAC1D,6BAAW,MAAM,KAAK,KAAK,QAAQ;AAAA,gBACrC;AACA;AAAA,YACJ;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO,iBAAiB,WAAW,CAAC,UAAU;AAC5C,cAAI,MAAM,KAAK,SAAS,YAAY,MAAM,KAAK,KAAK,WAAW;AAC7D;AACF,oBAAU;AACV,sBAAY;AACZ,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,oBAAY;AACZ,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,0BAA0B,OAAO,YAAY;AA9DrD;AA+DI,QAAI,WAAW,QAAQ,EAAC,mCAAS;AAC/B,YAAM,MAAK,wCAAS,gBAAT,mBAAsB,UAAS,wCAAS,gBAAT,mBAAsB,OAAO;AAAA,EAC3E;AACA,QAAM,UAAU,CAAC,SAAS,cAAc,YAAY;AAClD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,8BAAwB,OAAO,EAAE,KAAK,MAAM;AAC1C,YAAI,CAAC,UAAU,CAAC,SAAS;AACvB,iBAAO,IAAI,MAAM,kBAAkB,CAAC;AACpC;AAAA,QACF;AACA,eAAO,iBAAiB,SAAS,CAAC,UAAU;AAC1C,iBAAO,KAAK;AAAA,QACd,CAAC;AACD,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,eAAO,iBAAiB,WAAW,CAAC,UAAU;AA9EtD;AA+EU,kBAAQ,MAAM,KAAK,MAAM;AAAA,YACvB,KAAK;AACH,wBAAU;AACV,sBAAO,WAAM,KAAK,SAAX,mBAAiB,KAAK;AAC7B;AAAA,YACF,KAAK;AACH,2BAAa;AACb,sBAAQ,MAAM,KAAK,IAAI;AACvB;AAAA,UACJ;AAAA,QACF,CAAC;AACD,YAAI,CAAC,WAAW,CAAC,YAAY;AAC3B,2CAAQ,YAAY;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,SAAS,MAAM;AACb,UAAI,CAAC;AACH;AACF,aAAO,UAAU;AACjB,gBAAU;AACV,kBAAY;AACZ,eAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}